# 依赖项
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出
dist/
dist-ssr/
build/
*.local

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov
.nyc_output/

# 依赖项目录
jspm_packages/

# TypeScript 缓存
*.tsbuildinfo

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 可选的 stylelint 缓存
.stylelintcache

# Microbundle 缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的 REPL 历史
.node_repl_history

# 输出的二进制文件
*.tgz

# Yarn 完整性文件
.yarn-integrity

# parcel-bundler 缓存 (https://parceljs.org/)
.cache
.parcel-cache

# Next.js 构建输出
.next

# Nuxt.js 构建 / 生成输出
.nuxt

# Gatsby 文件
.cache/
public

# Storybook 构建输出
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Vite
.vite/

# Rollup
.rollup.cache/

# SvelteKit
.svelte-kit

# 测试
/test-results/
/playwright-report/
/playwright/.cache/

# 本地配置文件
*.local

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar

# 其他
.cache/
.temp/
.tmp/
