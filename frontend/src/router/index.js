import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Category from '../views/Category.vue'
import EntityDetail from '../views/EntityDetail.vue'
import Search from '../views/Search.vue'
import NotFound from '../views/NotFound.vue'
import AdminLayout from '../views/admin/Layout.vue'
import Dashboard from '../views/admin/Dashboard.vue'
import AdminCategories from '../views/admin/Categories.vue'
import AdminEntities from '../views/admin/Entities.vue'
import AdminTimelines from '../views/admin/Timelines.vue'
import AdminMedia from '../views/admin/Media.vue'
import AdminStatistics from '../views/admin/Statistics.vue'
import AdminAICreate from '../views/admin/AICreate.vue'
import { useAuthStore } from '../stores/auth'

const routes = [
  // 前台路由
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { title: '首页' }
  },
  {
    path: '/category/:id',
    name: 'Category',
    component: Category,
    meta: { title: '分类浏览' }
  },
  {
    path: '/entity/:id',
    name: 'EntityDetail',
    component: EntityDetail,
    meta: { title: '详情页面' }
  },
  {
    path: '/entity/:id/timeline',
    name: 'EntityTimeline',
    component: () => import('../views/EntityTimeline.vue'),
    meta: { title: '时间线' }
  },
  // 管理员路由
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: () => import('../views/admin/AdminLogin.vue'),
    meta: { title: '管理员登录' }
  },
  {
    path: '/search',
    name: 'Search',
    component: Search,
    meta: { title: '搜索结果' }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/About.vue'),
    meta: { title: '关于我们' }
  },
  {
    path: '/friend-links',
    name: 'FriendLinks',
    component: () => import('../views/FriendLinks.vue'),
    meta: { title: '友情链接' }
  },

  // 后台管理路由
  {
    path: '/admin',
    name: 'Admin',
    component: AdminLayout,
    meta: { requiresAuth: true, title: '管理后台' },
    children: [
      {
        path: '',
        name: 'AdminDashboard',
        component: Dashboard,
        meta: { title: '管理后台 - 仪表盘' }
      },
      {
        path: 'categories',
        name: 'AdminCategories',
        component: AdminCategories,
        meta: { title: '管理后台 - 分类管理' }
      },
      {
        path: 'entities',
        name: 'AdminEntities',
        component: AdminEntities,
        meta: { title: '管理后台 - 实体管理' }
      },
      {
        path: 'timelines',
        name: 'AdminTimelines',
        component: AdminTimelines,
        meta: { title: '管理后台 - 时间线管理' }
      },
      {
        path: 'media',
        name: 'AdminMedia',
        component: AdminMedia,
        meta: { title: '管理后台 - 媒体管理' }
      },
      {
        path: 'statistics',
        name: 'AdminStatistics',
        component: AdminStatistics,
        meta: { title: '管理后台 - 数据统计' }
      },
      {
        path: 'ai-create',
        name: 'AdminAICreate',
        component: AdminAICreate,
        meta: { title: '管理后台 - AI创建' }
      },
      {
        path: 'about-us',
        name: 'AdminAboutUs',
        component: () => import('../views/admin/AboutUs.vue'),
        meta: { requiresAuth: true, title: '关于我们' }
      },
      {
        path: 'friend-links',
        name: 'AdminFriendLinks',
        component: () => import('../views/admin/AdminFriendLinks.vue'),
        meta: { requiresAuth: true, title: '友情链接管理' }
      }
    ]
  },

  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: { title: '页面未找到' }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    const authStore = useAuthStore()

    // 如果没有token，直接跳转到登录页
    if (!authStore.token) {
      next('/admin/login')
      return
    }

    // 如果有token但未认证，尝试初始化认证状态
    if (!authStore.isAuthenticated) {
      try {
        await authStore.initAuth()
        if (!authStore.isAuthenticated) {
          next('/admin/login')
          return
        }
      } catch (error) {
        console.error('认证初始化失败:', error)
        next('/admin/login')
        return
      }
    }
  }

  next()
})

export default router
