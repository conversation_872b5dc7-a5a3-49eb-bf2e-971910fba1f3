import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import './styles/variables.css'
import App from './App.vue'
import { useAppStore, useAuthStore } from './stores'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(Antd)

// 初始化应用状态
const appStore = useAppStore()
const authStore = useAuthStore()

// 异步初始化应用
async function initApp() {
  // 初始化应用
  appStore.initApp()

  // 初始化认证状态
  await authStore.initAuth()

  // 挂载应用
  app.mount('#app')
}

initApp()
