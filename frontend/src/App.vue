<template>
  <div id="app" :data-theme="theme">
    <router-view />
  </div>
</template>

<script>
import { computed, onMounted } from 'vue'
import { useAppStore } from './stores/app'

export default {
  name: 'App',
  setup() {
    const appStore = useAppStore()

    // 计算主题
    const theme = computed(() => appStore.theme.mode)

    // 初始化应用
    onMounted(() => {
      appStore.initApp()
    })

    return {
      theme
    }
  }
}
</script>

<style>
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  min-height: 100vh;
  transition: background-color var(--transition-normal) var(--ease-out),
              color var(--transition-normal) var(--ease-out);
}

/* 确保主题切换时的平滑过渡 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  transition: background-color var(--transition-normal) var(--ease-out),
              color var(--transition-normal) var(--ease-out);
}
</style>
