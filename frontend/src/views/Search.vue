<template>
  <div class="search-page">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item>
        <router-link to="/">首页</router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>搜索结果</a-breadcrumb-item>
    </a-breadcrumb>

    <div class="search-content">
      <div class="search-header">
        <a-input-search
          v-model:value="searchKeyword"
          placeholder="请输入搜索关键词"
          enter-button="搜索"
          size="large"
          @search="handleSearch"
        />
      </div>

      <div class="search-results">
        <div v-if="searchKeyword" class="results-header">
          <h2>搜索结果</h2>
          <p class="search-info">
            关键词: <a-tag color="blue">{{ searchKeyword }}</a-tag>
            <span v-if="!searchLoading && searchResults.length > 0">
              找到 {{ searchPagination.total }} 个结果
            </span>
          </p>
        </div>

        <!-- 搜索结果列表 -->
        <div v-if="searchKeyword" class="results-list">
          <!-- 加载状态 -->
          <div v-if="searchLoading" class="loading-container">
            <a-spin size="large" />
          </div>

          <!-- 搜索结果 -->
          <div v-else-if="searchResults.length > 0" class="results-grid">
            <a-row :gutter="[16, 16]">
              <a-col
                v-for="entity in searchResults"
                :key="entity.id"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
                :xl="6"
              >
                <EntityCard :entity="entity" />
              </a-col>
            </a-row>

            <!-- 分页 -->
            <div class="pagination-container">
              <a-pagination
                v-model:current="searchPagination.current"
                v-model:page-size="searchPagination.pageSize"
                :total="searchPagination.total"
                :show-size-changer="true"
                :show-quick-jumper="true"
                :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
                @change="handlePageChange"
                @show-size-change="handlePageSizeChange"
              />
            </div>
          </div>

          <!-- 无结果 -->
          <div v-else class="no-results">
            <a-empty
              :description="`未找到包含'${searchKeyword}'的内容`"
              :image="Empty.PRESENTED_IMAGE_SIMPLE"
            >
              <template #extra>
                <a-button type="primary" @click="clearSearch">
                  清除搜索
                </a-button>
              </template>
            </a-empty>
          </div>
        </div>

        <!-- 默认状态 -->
        <div v-else class="default-state">
          <EntityList
            :show-filters="true"
            :show-pagination="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Empty } from 'ant-design-vue'
import EntityCard from '../components/EntityCard.vue'
import EntityList from '../components/EntityList.vue'
import { useEntitiesStore, useAppStore } from '../stores'
import { computed, onMounted, watch, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export default {
  name: 'Search',
  components: {
    EntityCard,
    EntityList
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const entitiesStore = useEntitiesStore()
    const appStore = useAppStore()

    // 响应式数据
    const searchKeyword = ref('')

    // 计算属性
    const searchResults = computed(() => entitiesStore.searchResults)
    const searchLoading = computed(() => entitiesStore.searchLoading)
    const searchPagination = computed(() => entitiesStore.searchPagination)

    // 方法
    const performSearch = async (keyword) => {
      if (!keyword.trim()) return

      try {
        await entitiesStore.searchEntities({ search: keyword })
        appStore.setPageTitle(`搜索: ${keyword}`)
      } catch (error) {
        console.error('搜索失败:', error)
      }
    }

    const handleSearch = (value) => {
      const keyword = value.trim()
      if (keyword) {
        searchKeyword.value = keyword
        // 更新URL参数
        router.push({ path: '/search', query: { q: keyword } })
        // 重置分页并搜索
        entitiesStore.setSearchPagination({ current: 1 })
        performSearch(keyword)
      }
    }

    const handlePageChange = (page, pageSize) => {
      entitiesStore.setSearchPagination({ current: page, pageSize })
      performSearch(searchKeyword.value)
    }

    const handlePageSizeChange = (current, size) => {
      entitiesStore.setSearchPagination({ current: 1, pageSize: size })
      performSearch(searchKeyword.value)
    }

    const clearSearch = () => {
      searchKeyword.value = ''
      router.push({ path: '/search' })
      appStore.setPageTitle('搜索')
    }

    // 监听路由查询参数变化
    watch(
      () => route.query.q,
      (newKeyword) => {
        if (newKeyword) {
          searchKeyword.value = newKeyword
          performSearch(newKeyword)
        } else {
          searchKeyword.value = ''
        }
      },
      { immediate: true }
    )

    // 组件挂载时处理初始搜索
    onMounted(() => {
      const keyword = route.query.q
      if (keyword) {
        searchKeyword.value = keyword
        performSearch(keyword)
      } else {
        appStore.setPageTitle('搜索')
      }
    })

    return {
      Empty,
      searchKeyword,
      searchResults,
      searchLoading,
      searchPagination,
      handleSearch,
      handlePageChange,
      handlePageSizeChange,
      clearSearch
    }
  }
}
</script>

<style scoped>
/* 整体页面样式 - 与Home页面保持一致的背景和布局 */
.search-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: var(--space-xl);
}

/* 搜索内容容器 - 现代化卡片设计 */
.search-content {
  max-width: 1200px;
  margin: 0 auto;
  background: var(--gradient-bg-primary);
  padding: var(--space-4xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-card-hover);
  position: relative;
  overflow: hidden;
}

/* 添加顶部装饰条 - 与Home页面一致 */
.search-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-shimmer);
  animation: shimmer 3s ease-in-out infinite;
}

/* 搜索头部样式优化 */
.search-header {
  margin-bottom: var(--space-4xl);
}

/* 统一搜索框样式 - 与Home页面头部搜索框保持一致 */
.search-header :deep(.ant-input-search) {
  max-width: 600px;
  margin: 0 auto;
  display: block;
}

.search-header :deep(.ant-input-affix-wrapper) {
  border-radius: 20px;
  border: 1px solid #d9d9d9;
  background: #ffffff;
  height: 48px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.search-header :deep(.ant-input-affix-wrapper):hover {
  border-color: #ff4d4f;
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.15);
}

.search-header :deep(.ant-input-affix-wrapper-focused) {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.search-header :deep(.ant-input) {
  border: none;
  background: transparent;
  padding: 12px 20px;
  font-size: 16px;
  height: 46px;
  line-height: 1.5;
}

.search-header :deep(.ant-input-search-button) {
  border-radius: 0 20px 20px 0;
  border: none;
  background: #ff4d4f;
  height: 48px;
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s ease;
}

.search-header :deep(.ant-input-search-button):hover {
  background: #ff7875;
  transform: scale(1.05);
}

/* 结果头部样式 */
.results-header {
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.results-header h2 {
  margin-bottom: 12px;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.search-info {
  color: #8c8c8c;
  margin: 0;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 统一标签样式 */
.search-info :deep(.ant-tag) {
  border-radius: 12px;
  border: none;
  background: #ff4d4f;
  color: white;
  font-weight: 500;
  padding: 4px 12px;
}

/* 加载状态优化 - 与Home页面保持一致 */
.loading-container {
  text-align: center;
  padding: 80px 0;
  position: relative;
}

.loading-container :deep(.ant-spin) {
  color: #ff4d4f;
}

/* 结果网格样式 */
.results-grid {
  margin-bottom: 32px;
}

.results-grid :deep(.ant-row) {
  margin: -12px;
}

.results-grid :deep(.ant-col) {
  padding: 12px;
  transition: all 0.3s ease;
}

/* 统一卡片悬浮效果 */
.results-grid :deep(.ant-col:hover) {
  transform: translateY(-4px);
}

/* 分页容器样式 - 与Home页面保持一致 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 32px 0;
  background: #ffffff;
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 16px 16px;
  margin: 0 -32px -32px -32px;
}

.pagination-container :deep(.ant-pagination-item) {
  border-radius: 6px;
  border-color: #d9d9d9;
}

.pagination-container :deep(.ant-pagination-item-active) {
  background: #ff4d4f;
  border-color: #ff4d4f;
}

.pagination-container :deep(.ant-pagination-item-active a) {
  color: #ffffff;
}

.pagination-container :deep(.ant-pagination-item:hover) {
  border-color: #ff4d4f;
}

.pagination-container :deep(.ant-pagination-item:hover a) {
  color: #ff4d4f;
}

.pagination-container :deep(.ant-pagination-prev),
.pagination-container :deep(.ant-pagination-next) {
  border-radius: 6px;
}

.pagination-container :deep(.ant-pagination-prev:hover),
.pagination-container :deep(.ant-pagination-next:hover) {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

/* 无结果状态优化 */
.no-results {
  text-align: center;
  padding: 80px 0;
}

.no-results :deep(.ant-empty-description) {
  color: #8c8c8c;
  font-size: 16px;
}

.no-results :deep(.ant-btn-primary) {
  background: #ff4d4f;
  border-color: #ff4d4f;
  border-radius: 6px;
  height: 40px;
  padding: 0 24px;
  font-weight: 500;
}

.no-results :deep(.ant-btn-primary):hover {
  background: #ff7875;
  border-color: #ff7875;
}

/* 默认状态样式 */
.default-state {
  margin-top: 32px;
}

/* 面包屑导航样式优化 */
.search-page :deep(.ant-breadcrumb) {
  margin: 0 0 24px 0;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 20px;
}

.search-page :deep(.ant-breadcrumb-link) {
  color: #8c8c8c;
  transition: color 0.3s ease;
}

.search-page :deep(.ant-breadcrumb-link):hover {
  color: #ff4d4f;
}

.search-page :deep(.ant-breadcrumb-separator) {
  color: #d9d9d9;
}

/* 闪烁动画 - 与Home页面保持一致 */
@keyframes shimmer {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-page {
    padding: 16px;
  }

  .search-content {
    padding: 24px 20px;
    border-radius: 12px;
  }

  .search-header :deep(.ant-input-affix-wrapper) {
    height: 44px;
  }

  .search-header :deep(.ant-input) {
    height: 42px;
    padding: 10px 16px;
    font-size: 14px;
  }

  .search-header :deep(.ant-input-search-button) {
    height: 44px;
    width: 44px;
  }

  .results-header h2 {
    font-size: 20px;
  }

  .pagination-container {
    padding: 24px 0;
    margin: 0 -20px -24px -20px;
  }
}
</style>
