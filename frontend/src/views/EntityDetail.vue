<template>
  <div class="entity-detail-page">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>

    <!-- 时间线展示 -->
    <TimeLines
      v-else-if="entity"
      :entity-name="entity.name"
      :entity-subtitle="getEntityTypeLabel(entity.type || entity.entity_type).toUpperCase()"
      :entity-description="entity.description"
      :entity-data="entity"
      :timeline-data="entityTimelines"
      :default-image="entity.avatar || entity.cover_image"
      @like="handleLike"
      @flower="handleFlower"
      @share="handleShare"
    />

    <!-- 错误状态 -->
    <div v-else class="error-state">
      <a-result
        status="404"
        title="实体不存在"
        sub-title="抱歉，您访问的实体不存在或已被删除。"
      >
        <template #extra>
          <a-button type="primary" @click="$router.push('/')">
            返回首页
          </a-button>
        </template>
      </a-result>
    </div>
  </div>
</template>

<script>
import {
  HeartOutlined,
  GiftOutlined,
  CalendarOutlined,
  EyeOutlined,
  ShareAltOutlined,
  ClockCircleOutlined,
  PlusOutlined,
  UserOutlined,
  BankOutlined
} from '@ant-design/icons-vue'
import TimeLines from '../components/timeLines.vue'
import { useEntitiesStore, useTimelinesStore, useAppStore } from '../stores'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

export default {
  name: 'EntityDetail',
  components: {
    HeartOutlined,
    GiftOutlined,
    CalendarOutlined,
    EyeOutlined,
    ShareAltOutlined,
    ClockCircleOutlined,
    PlusOutlined,
    UserOutlined,
    BankOutlined,
    TimeLines
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const entitiesStore = useEntitiesStore()
    const timelinesStore = useTimelinesStore()
    const appStore = useAppStore()

    // 响应式数据
    const likeLoading = ref(false)
    const flowerLoading = ref(false)
    const showTimelineActions = ref(false) // 是否显示时间线操作按钮（管理员功能）

    // 计算属性
    const entity = computed(() => entitiesStore.currentEntity)
    const loading = computed(() => entitiesStore.loading)
    const entityTimelines = computed(() => timelinesStore.entityTimelines)
    const timelinesLoading = computed(() => timelinesStore.loading)
    const timelinesPagination = computed(() => timelinesStore.pagination)

    // 方法
    const loadEntity = async (id) => {
      try {
        await entitiesStore.fetchEntity(parseInt(id))
        if (entitiesStore.currentEntity) {
          appStore.setPageTitle(`${entitiesStore.currentEntity.name} - 详情`)
          // 加载该实体的时间线
          await loadEntityTimelines(parseInt(id))
        }
      } catch (error) {
        console.error('加载实体失败:', error)
      }
    }

    const loadEntityTimelines = async (entityId) => {
      try {
        console.log('🔄 开始加载实体时间线, entityId:', entityId);
        await timelinesStore.fetchTimelinesByEntity(entityId)
        console.log('✅ 时间线加载完成, entityTimelines:', entityTimelines.value);
      } catch (error) {
        console.error('加载时间线失败:', error)
      }
    }

    const getEntityIcon = (type) => {
      const iconMap = {
        person: 'UserOutlined',
        event: 'CalendarOutlined',
        enterprise: 'BankOutlined'
      }
      return iconMap[type] || 'UserOutlined'
    }

    const getEntityTypeLabel = (type) => {
      const labelMap = {
        person: '人物',
        event: '事件',
        enterprise: '企业'
      }
      return labelMap[type] || '未知'
    }

    const getEntityTypeColor = (type) => {
      const colorMap = {
        person: 'blue',
        event: 'green',
        enterprise: 'orange'
      }
      return colorMap[type] || 'default'
    }

    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    const handleLike = async () => {
      if (!entity.value) return

      likeLoading.value = true
      try {
        const userInfo = {
          ip_address: 'unknown',
          user_agent: navigator.userAgent
        }
        await entitiesStore.likeEntity(entity.value.id, userInfo)
      } catch (error) {
        console.error('点赞失败:', error)
      } finally {
        likeLoading.value = false
      }
    }

    const handleFlower = async () => {
      if (!entity.value) return

      flowerLoading.value = true
      try {
        const userInfo = {
          ip_address: 'unknown',
          user_agent: navigator.userAgent
        }
        await entitiesStore.flowerEntity(entity.value.id, userInfo)
      } catch (error) {
        console.error('送花失败:', error)
      } finally {
        flowerLoading.value = false
      }
    }

    const handleShare = () => {
      if (navigator.share) {
        navigator.share({
          title: entity.value?.name,
          text: entity.value?.description,
          url: window.location.href
        })
      } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
          message.success('链接已复制到剪贴板')
        })
      }
    }

    const addTimeline = () => {
      // 添加时间线功能（管理员功能）
      console.log('添加时间线')
    }

    const editTimeline = (timeline) => {
      // 编辑时间线功能（管理员功能）
      console.log('编辑时间线:', timeline)
    }

    const handleTimelinePageChange = ({ page, pageSize }) => {
      timelinesStore.setPagination({ current: page, pageSize })
      loadEntityTimelines(parseInt(route.params.id))
    }

    const handleTimelinePageSizeChange = ({ current, size }) => {
      timelinesStore.setPagination({ current: 1, pageSize: size })
      loadEntityTimelines(parseInt(route.params.id))
    }

    const viewTimeline = () => {
      // 跳转到独立的时间线页面
      router.push(`/entity/${route.params.id}/timeline`)
    }

    // 组件挂载时加载数据
    onMounted(() => {
      const entityId = route.params.id
      if (entityId) {
        loadEntity(entityId)
      }
    })

    return {
      entity,
      loading,
      entityTimelines,
      timelinesLoading,
      timelinesPagination,
      likeLoading,
      flowerLoading,
      showTimelineActions,
      getEntityIcon,
      getEntityTypeLabel,
      getEntityTypeColor,
      formatDate,
      handleLike,
      handleFlower,
      handleShare,
      viewTimeline
    }
  }
}
</script>

<style scoped>
.entity-detail-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: var(--space-xl);
}

.entity-detail-page :deep(.ant-breadcrumb) {
  margin: var(--space-lg) 0;
  padding: var(--space-lg) var(--space-2xl);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-xs);
}

.entity-detail-page :deep(.ant-breadcrumb a) {
  color: var(--primary-color);
  transition: var(--transition-colors);
}

.entity-detail-page :deep(.ant-breadcrumb a:hover) {
  color: var(--primary-hover);
}

.loading-container {
  text-align: center;
  padding: var(--space-5xl) 0;
}

.entity-content {
  max-width: 1200px;
  margin: 0 auto;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-card);
  overflow: hidden;
}

.entity-header {
  display: flex;
  padding: var(--space-4xl);
  border-bottom: 1px solid var(--border-primary);
  gap: var(--space-3xl);
}

.entity-avatar {
  flex-shrink: 0;
}

.entity-avatar img {
  width: 120px;
  height: 120px;
  border-radius: var(--radius-lg);
  object-fit: cover;
  box-shadow: var(--shadow-md);
}

.default-avatar {
  width: 120px;
  height: 120px;
  border-radius: var(--radius-lg);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: white;
  box-shadow: var(--shadow-md);
}

.entity-info {
  flex: 1;
  min-width: 0;
}

.entity-title {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.entity-title h1 {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-3xl);
  font-weight: var(--font-semibold);
}

.entity-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #8c8c8c;
  font-size: 14px;
}

.entity-description {
  margin-bottom: 24px;
}

.entity-description p {
  color: #595959;
  line-height: 1.6;
  font-size: 16px;
  margin: 0;
}

.entity-actions {
  margin-top: 24px;
}

.timeline-section {
  padding: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header h2 {
  margin: 0;
  color: #262626;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-state {
  text-align: center;
  padding: 100px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .entity-detail-page {
    padding: 12px;
  }

  .entity-header {
    flex-direction: column;
    padding: 24px;
    text-align: center;
  }

  .entity-avatar {
    align-self: center;
  }

  .entity-title {
    justify-content: center;
    flex-wrap: wrap;
  }

  .entity-title h1 {
    font-size: 24px;
  }

  .entity-meta {
    justify-content: center;
  }

  .timeline-section {
    padding: 24px 16px;
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
</style>
