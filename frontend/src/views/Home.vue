<template>
  <a-layout class="home-layout">
    <!-- 参考设计的头部导航 -->
    <a-layout-header class="top-header">
      <div class="header-container">
        <div class="logo-area">
          <div class="logo-container">
            <div class="logo-icon">
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="32" height="32" rx="8" fill="url(#gradient)"/>
                <path d="M8 12C8 10.8954 8.89543 10 10 10H14C15.1046 10 16 10.8954 16 12V20C16 21.1046 15.1046 22 14 22H10C8.89543 22 8 21.1046 8 20V12Z" fill="white"/>
                <path d="M18 8C18 6.89543 18.8954 6 20 6H22C23.1046 6 24 6.89543 24 8V24C24 25.1046 23.1046 26 22 26H20C18.8954 26 18 25.1046 18 24V8Z" fill="white"/>
                <circle cx="12" cy="16" r="2" fill="#ff4d4f"/>
                <circle cx="21" cy="16" r="2" fill="#ff4d4f"/>
                <defs>
                  <linearGradient id="gradient" x1="0" y1="0" x2="32" y2="32" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#ff4d4f"/>
                    <stop offset="1" stop-color="#ff7875"/>
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <div class="logo-text-group">
              <span class="logo-main-text">数字生命馆</span>
              <span class="logo-sub-text">Everything is alive</span>
            </div>
          </div>
        </div>

        <div class="search-area">
          <a-input-search
            placeholder="搜索人物、事件、企业..."
            class="header-search"
            @search="handleSearch"
          />
        </div>

        <div class="header-actions">
          <ThemeToggle />
        </div>
      </div>
    </a-layout-header>

    <!-- 主要内容区域 - 左右布局 -->
    <a-layout class="main-layout">
      <!-- 左侧导航栏 -->
      <a-layout-sider
        width="240"
        class="left-sidebar"
        :collapsed="siderCollapsed"
        :collapsible="false"
        :breakpoint="'lg'"
        :collapsed-width="0"
      >
        <div class="sidebar-content">
          <!-- 分类标题 -->
          <div class="category-header">
            <h3>浏览分类</h3>
          </div>

          <!-- 分类列表 -->
          <div class="category-list">
            <!-- 真实分类数据 -->
            <div v-if="!categoriesLoading && realCategories.length > 0">
              <div
                v-for="category in realCategories"
                :key="category.id"
                class="category-item"
                @click="goToCategory(category.id)"
              >
                <div class="category-icon">
                  <component :is="getCategoryIcon(category)" />
                </div>
                <div class="category-info">
                  <div class="category-name">{{ category.name }}</div>
                  <div class="category-desc">{{ category.description }}</div>
                </div>
                <div class="category-count">{{ category.count || 0 }}</div>
              </div>
            </div>

            <!-- 分类加载状态 -->
            <div v-else-if="categoriesLoading" class="loading-container">
              <a-spin size="large" />
            </div>

            <!-- 默认分类 -->
            <div v-else>
              <div
                v-for="category in defaultCategories"
                :key="category.id"
                class="category-item"
                @click="goToCategory(category.id)"
              >
                <div class="category-icon">
                  <component :is="category.icon" />
                </div>
                <div class="category-info">
                  <div class="category-name">{{ category.name }}</div>
                  <div class="category-desc">{{ category.description }}</div>
                </div>
                <div class="category-count">{{ getDefaultCategoryCount(category.id) }}</div>
              </div>
            </div>
            <div class="category-item" @click="goToAbout">
              <div class="category-icon">
                <InfoCircleOutlined />
              </div>
              <div class="category-info">
                <div class="category-name">关于我们</div>
                <div class="category-desc">了解本站</div>
              </div>
            </div>
            <div class="category-item" @click="goToFriendLinks">
              <div class="category-icon">
                <LinkOutlined />
              </div>
              <div class="category-info">
                <div class="category-name">友情链接</div>
                <div class="category-desc">合作伙伴</div>
              </div>
            </div>
          </div>
        </div>
      </a-layout-sider>

      <!-- 右侧主内容区域 -->
      <a-layout-content class="main-content">
        <div class="content-wrapper">
          <!-- 分类切换头部 -->
          <div class="content-top">
            <div class="content-actions">
              <a-button class="view-toggle" :class="{ active: viewMode === 'grid' }" @click="setViewMode('grid')">
                <span class="toggle-icon">⊞</span>
              </a-button>
              <a-button class="view-toggle" :class="{ active: viewMode === 'list' }" @click="setViewMode('list')">
                <span class="toggle-icon">☰</span>
              </a-button>
            </div>
          </div>

          <!-- 分类筛选栏 -->
          <div class="filter-bar">
            <div class="filter-left">
              <!-- 分类筛选 -->
              <div class="filter-group">
                <span class="filter-label">分类筛选：</span>
                <a-button
                  :class="['filter-btn', { active: activeFilter.category === null }]"
                  @click="setFilter('category', null)"
                >
                  全部
                </a-button>
                <a-button
                  v-for="category in realCategories"
                  :key="category.id"
                  :class="['filter-btn', { active: activeFilter.category === category.id }]"
                  @click="setFilter('category', category.id)"
                >
                  <component :is="getCategoryIcon(category)" class="filter-icon" />
                  {{ category.name }}
                </a-button>
              </div>

            </div>
            <div class="filter-right">
              <span class="filter-text">
                <a-spin v-if="entitiesLoading" size="small" />
              </span>
            </div>
          </div>

          <!-- 内容网格 -->
          <div class="content-grid" :class="{ 'list-view': viewMode === 'list' }">
            <!-- 装饰性背景元素 -->
            <div class="grid-decoration">
              <div class="decoration-circle decoration-1"></div>
              <div class="decoration-circle decoration-2"></div>
            </div>

            <div v-if="entitiesLoading" class="loading-container">
              <div class="loading-spinner">
                <a-spin size="large" />
                <div class="loading-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
              <p class="loading-text">正在加载精彩内容...</p>
            </div>
            <div v-else-if="entities.length === 0" class="empty-container">
              <div class="empty-illustration">
                <div class="empty-icon">📚</div>
                <h3>暂无内容</h3>
                <p>当前筛选条件下没有找到相关内容</p>
              </div>
            </div>
            <div v-else class="entities-grid">
              <a-row :gutter="[24, 24]">
                <a-col
                  v-for="(entity, index) in entities"
                  :key="entity.id"
                  :xs="24"
                  :sm="12"
                  :md="viewMode === 'list' ? 24 : 8"
                  :lg="viewMode === 'list' ? 24 : 6"
                  :xl="viewMode === 'list' ? 24 : 6"
                  :style="{ animationDelay: `${index * 0.1}s` }"
                  class="entity-col"
                >
                  <EntityCard :entity="entity" />
                </a-col>
              </a-row>
            </div>
          </div>

          <!-- 分页组件 -->
          <div class="pagination-wrapper">
            <a-pagination
              v-model:current="currentPage"
              v-model:page-size="pageSize"
              :total="totalEntities"
              :show-size-changer="true"
              :show-quick-jumper="true"
              :show-total="(total, range) => `显示第 ${range[0]} 到第 ${range[1]} 条，共 ${total} 条`"
              :page-size-options="['12', '24', '48', '96']"
              @change="handlePageChange"
              @show-size-change="handlePageSizeChange"
              class="custom-pagination"
            />
          </div>
        </div>
      </a-layout-content>
    </a-layout>

    <!-- 页脚 -->
    <a-layout-footer class="modern-footer">
      <div class="footer-content">
        <p>&copy; 2025 数字生命馆. All rights reserved.</p>
      </div>
    </a-layout-footer>
  </a-layout>
</template>

<script>
import {
  UserOutlined,
  CalendarOutlined,
  BankOutlined,
  EyeOutlined,
  HeartOutlined,
  RightOutlined,
  InfoCircleOutlined,
  LinkOutlined
} from '@ant-design/icons-vue'
import EntityList from '../components/EntityList.vue'
import EntityCard from '../components/EntityCard.vue'
import ThemeToggle from '../components/ThemeToggle.vue'
import { useCategoriesStore } from '../stores'
import { entitiesAPI } from '../api/entities'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'Home',
  components: {
    UserOutlined,
    CalendarOutlined,
    BankOutlined,
    EyeOutlined,
    HeartOutlined,
    RightOutlined,
    EntityList,
    EntityCard,
    InfoCircleOutlined,
    LinkOutlined,
    ThemeToggle
  },
  setup() {
    const categoriesStore = useCategoriesStore()
    const router = useRouter()

    // 响应式数据
    const siderCollapsed = ref(false)
    const viewMode = ref('grid')
    const selectedCategory = ref('all')

    // 筛选相关数据
    const activeFilter = ref({
      category: null,
      entityType: null
    })
    const entitiesLoading = ref(false)
    const totalEntities = ref(0)
    const entities = ref([])

    // 计算属性
    const realCategories = computed(() => categoriesStore.categories)
    const categoriesLoading = computed(() => categoriesStore.loading)

    // 所有分类（包含"全部"选项）
    const allCategories = computed(() => {
      const allOption = {
        id: 'all',
        name: '全部',
        icon: 'EyeOutlined',
        count: getTotalCount()
      }

      if (realCategories.value.length > 0) {
        return [allOption, ...realCategories.value]
      } else {
        return [allOption, ...defaultCategories.map(cat => ({
          ...cat,
          count: getDefaultCategoryCount(cat.id)
        }))]
      }
    })

    // 默认分类（当没有真实数据时显示）
    const defaultCategories = [
      {
        id: 2,
        name: '事件',
        description: '重要历史事件记录',
        icon: 'CalendarOutlined'
      },
      {
        id: 3,
        name: '企业',
        description: '企业发展历程',
        icon: 'BankOutlined'
      }
    ]

    // 方法
    const loadCategories = async () => {
      try {
        await categoriesStore.fetchCategories()
      } catch (error) {
        console.error('加载分类失败:', error)
      }
    }

    const getCategoryIcon = (category) => {
      // 根据分类名称或类型返回对应图标
      const name = category.name.toLowerCase()
      if (name.includes('人物') || name.includes('人')) {
        return 'UserOutlined'
      } else if (name.includes('事件') || name.includes('历史')) {
        return 'CalendarOutlined'
      } else if (name.includes('企业') || name.includes('公司')) {
        return 'BankOutlined'
      }
      return 'UserOutlined'
    }

    const handleSearch = (value) => {
      if (value.trim()) {
        router.push({ path: '/search', query: { q: value } })
      }
    }

    const goToCategory = (id) => {
      router.push(`/category/${id}`)
    }

    const goToAbout = () => {
      router.push('/about')
    }

    const goToFriendLinks = () => {
      router.push('/friend-links')
    }

    const goToAllEntities = () => {
      router.push('/search')
    }

    const onSiderCollapse = (collapsed) => {
      siderCollapsed.value = collapsed
    }



    const getDefaultCategoryCount = (categoryId) => {
      // 模拟数据，实际应该从store获取
      const counts = { 2: 15, 3: 8 }
      return counts[categoryId] || 0
    }

    const setViewMode = (mode) => {
      viewMode.value = mode
    }

    const switchCategory = (categoryId) => {
      selectedCategory.value = categoryId
      // 这里可以添加筛选逻辑，比如调用EntityList的筛选方法
      console.log('切换到分类:', categoryId)
    }

    const getTotalCount = () => {
      if (realCategories.value.length > 0) {
        return realCategories.value.reduce((total, cat) => total + (cat.count || 0), 0)
      } else {
        return defaultCategories.reduce((total, cat) => total + getDefaultCategoryCount(cat.id), 0)
      }
    }

    // 筛选相关方法
    const setFilter = (type, value) => {
      activeFilter.value[type] = value
      // 筛选时重置到第一页
      currentPage.value = 1
      loadEntities(1)
    }

    // 分页相关方法
    const handlePageChange = (page) => {
      loadEntities(page)
    }

    const handlePageSizeChange = (current, size) => {
      pageSize.value = size
      currentPage.value = 1
      loadEntities(1, size)
    }

    // 分页相关数据
    const currentPage = ref(1)
    const pageSize = ref(12)
    const totalPages = ref(0)

    const loadEntities = async (page = 1, size = pageSize.value) => {
      try {
        entitiesLoading.value = true
        const params = {
          page: page,
          size: size
        }

        // 添加分类筛选
        if (activeFilter.value.category) {
          params.category_id = activeFilter.value.category
        }

        // 添加类型筛选
        if (activeFilter.value.entityType) {
          params.entity_type = activeFilter.value.entityType
        }

        console.log('API请求参数:', params)
        const response = await entitiesAPI.getEntities(params)
        console.log('API响应:', response)

        // 处理不同的响应格式
        if (response.data) {
          // 如果响应有data字段
          entities.value = response.data.items || response.data || []
          totalEntities.value = response.data.total || response.data.length || 0
          totalPages.value = Math.ceil(totalEntities.value / size)
        } else {
          // 直接响应格式
          entities.value = response.items || response || []
          totalEntities.value = response.total || response.length || 0
          totalPages.value = Math.ceil(totalEntities.value / size)
        }

        // 更新当前页码
        currentPage.value = page

        console.log('处理后的实体数据:', entities.value)
        console.log('总数:', totalEntities.value)
        console.log('总页数:', totalPages.value)
      } catch (error) {
        console.error('加载实体数据失败:', error)
        entities.value = []
        totalEntities.value = 0
        totalPages.value = 0

        // 如果API调用失败，使用模拟数据
        entities.value = []
        totalEntities.value = entities.value.length
        totalPages.value = 1
        currentPage.value = 1
      } finally {
        entitiesLoading.value = false
      }
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadCategories()
      // 延迟加载实体数据，确保页面先渲染
      setTimeout(() => {
        loadEntities()
      }, 100)
    })

    return {
      siderCollapsed,
      viewMode,
      selectedCategory,
      realCategories,
      categoriesLoading,
      defaultCategories,
      allCategories,
      activeFilter,
      entitiesLoading,
      totalEntities,
      entities,
      currentPage,
      pageSize,
      totalPages,
      getCategoryIcon,
      handleSearch,
      goToCategory,
      goToAllEntities,
      goToAbout,
      goToFriendLinks,
      onSiderCollapse,
      getDefaultCategoryCount,
      setViewMode,
      switchCategory,
      getTotalCount,
      setFilter,
      loadEntities,
      handlePageChange,
      handlePageSizeChange
    }
  }
}
</script>

<style scoped>
/* 整体布局 */
.home-layout {
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* 顶部导航栏 */
.top-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  padding: 0;
  height: 64px;
  line-height: normal;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  padding: 0 24px;
  gap: 20px;
}

.logo-area {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo-text-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1;
}

.logo-main-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
  letter-spacing: 0.5px;
  margin: 0;
  white-space: nowrap;
}

.logo-sub-text {
  font-size: 12px;
  color: var(--text-tertiary);
  font-weight: 400;
  margin-top: 2px;
  letter-spacing: 0.3px;
  white-space: nowrap;
}

.search-area {
  flex: 1;
  max-width: 400px;
  margin: 0 40px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.header-search {
  width: 100%;
}

.header-search :deep(.ant-input-affix-wrapper) {
  border-radius: 20px;
  border: 1px solid #d9d9d9;
  background: #ffffff;
  height: 36px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

.header-search :deep(.ant-input) {
  border: none;
  background: transparent;
  padding: 8px 16px;
  font-size: 14px;
  height: 34px;
  line-height: 1.5;
}

.header-search :deep(.ant-input-search-button) {
  border-radius: 0 20px 20px 0;
  border: none;
  background: #f5f5f5;
  height: 36px;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-search :deep(.ant-input-search-button):hover {
  background: #e6f7ff;
  color: #1890ff;
}



/* 主布局 */
.main-layout {
  min-height: calc(100vh - 64px);
  background: var(--bg-secondary);
}

/* 左侧导航栏 */
.left-sidebar {
  background: var(--bg-primary);
  border-right: 1px solid var(--border-primary);
  box-shadow: var(--shadow-xs);
}

.sidebar-content {
  padding: 24px 16px;
  height: 100%;
}

/* 分类标题 */
.category-header {
  padding: 0 8px 20px;
  margin-bottom: 16px;
}

.category-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 分类列表 */
.category-list {
  padding: 0;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 14px 8px;
  margin-bottom: 6px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
}

.category-item:hover {
  background: var(--bg-tertiary);
  transform: translateX(1px);
}

.category-icon {
  flex: 0 0 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  border-radius: 8px;
  margin-right: 12px;
  color: white;
  font-size: 16px;
}

.category-info {
  flex: 1;
  min-width: 0;
}

.category-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
  line-height: 1.3;
}

.category-desc {
  font-size: 12px;
  color: var(--text-tertiary);
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.category-count {
  flex: 0 0 auto;
  background: #f0f0f0;
  color: #595959;
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

/* 主内容区域 */
.main-content {
  background: var(--bg-secondary);
  padding: 0;
}

.content-wrapper {
  padding: 20px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 内容头部 */
.content-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

/* 分类切换标签 */
.category-tabs {
  display: flex;
  gap: 8px;
  align-items: center;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #ffffff;
  color: #595959;
  font-size: 14px;
  height: 36px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.category-tab:hover {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.category-tab.active {
  background: #ff4d4f;
  border-color: #ff4d4f;
  color: #ffffff;
}

.tab-icon {
  font-size: 14px;
}

.tab-count {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  font-weight: 500;
}

.category-tab.active .tab-count {
  background: rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

.category-tab:not(.active) .tab-count {
  background: #f0f0f0;
  color: #8c8c8c;
}

/* 视图切换按钮 */
.content-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.view-toggle {
  width: 36px;
  height: 36px;
  border: 1px solid #d9d9d9;
  background: #ffffff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
}

.view-toggle:hover {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.view-toggle.active {
  border-color: #ff4d4f;
  background: #ff4d4f;
  color: white;
}

.view-toggle.active:hover {
  border-color: #ff4d4f;
  background: #ff4d4f;
  color: white;
}

.toggle-icon {
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--bg-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
}

.filter-left {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 80px;
}

.filter-btn {
  height: 32px;
  border-radius: 6px;
  font-size: 13px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  border: 1px solid var(--border-tertiary);
  background: var(--bg-primary);
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.filter-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.filter-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: #ffffff;
}

.filter-btn.active:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: #ffffff;
}

.filter-icon {
  font-size: 12px;
}

.filter-right {
  color: var(--text-secondary);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.filter-text {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 内容网格 - 现代化设计 */
.content-grid {
  background: var(--gradient-bg-primary);
  border-radius: 16px;
  padding: 0;
  box-shadow: var(--shadow-card-hover);
  margin-bottom: 24px;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.content-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-shimmer);
  animation: shimmer 3s ease-in-out infinite;
}

/* 简化装饰元素 */
.grid-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
  opacity: 0.3;
  padding: 10px;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 77, 79, 0.03) 0%, transparent 70%);
}

.decoration-1 {
  width: 120px;
  height: 120px;
  top: 20px;
  right: 20px;
}

.decoration-2 {
  width: 80px;
  height: 80px;
  bottom: 20px;
  left: 20px;
}

/* 现代化列表视图样式 */
.content-grid.list-view {
  padding: 0;
  background: transparent;
}

.content-grid.list-view .entities-grid {
  padding: 20px 0px !important;
  margin: 0 !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.content-grid.list-view :deep(.ant-row) {
  margin: 0 !important;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100% !important;
  box-sizing: border-box !important;
}

.content-grid.list-view :deep(.ant-col) {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  flex: 0 0 100% !important;
  margin-bottom: 0;
  padding: 0 !important;
  height: auto;
  box-sizing: border-box !important;
}

.content-grid.list-view :deep(.entity-card) {
  border-radius: 16px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  margin: 0 24px;
  background: #ffffff;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: row;
  height: 140px !important;
  min-height: 140px !important;
  max-height: 140px !important;
  width: calc(100% - 20px) !important;
  min-width: calc(100% - 20px) !important;
  max-width: calc(100% - 20px) !important;
}

.content-grid.list-view :deep(.entity-card:hover) {
  transform: translateX(8px);
  box-shadow: 0 8px 32px rgba(255, 77, 79, 0.12);
  border-color: #ff4d4f;
}

.content-grid.list-view :deep(.card-cover),
.content-grid.list-view :deep(.ant-card-cover) {
  width: 200px !important;
  height: 140px !important;
  min-width: 200px !important;
  max-width: 200px !important;
  min-height: 140px !important;
  max-height: 140px !important;
  border-radius: 16px 0 0 16px;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.content-grid.list-view :deep(.cover-image) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.content-grid.list-view :deep(.default-cover) {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-grid.list-view :deep(.entity-icon) {
  font-size: 32px;
  color: white;
}

.content-grid.list-view :deep(.ant-card-body) {
  flex: 1;
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: #ffffff;
  height: 140px !important;
  min-height: 140px !important;
  max-height: 140px !important;
  position: relative;
  overflow: hidden;
}

.content-grid.list-view :deep(.card-title) {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content-grid.list-view :deep(.card-description) {
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 42px;
  flex-shrink: 0;
}

/* 内容区域布局优化 */
.content-grid.list-view :deep(.card-content) {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex: 1;
}

/* 标题和描述的容器 */
.content-grid.list-view :deep(.card-header) {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.content-grid.list-view :deep(.ant-card-actions) {
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 24px;
  height: auto;
  flex-shrink: 0;
}

.content-grid.list-view :deep(.action-item) {
  color: #8c8c8c;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0;
  transition: all 0.2s ease;
}

.content-grid.list-view :deep(.action-item:hover) {
  color: #ff4d4f;
  transform: none;
}

.content-grid.list-view :deep(.entity-type-badge) {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.content-grid.list-view :deep(.category-tag) {
  position: absolute;
  top: 12px;
  left: 12px;
}

/* 列表视图特殊效果 */
.content-grid.list-view :deep(.entity-card::before) {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #ff4d4f 0%, #ff7875 100%);
  transform: scaleY(0);
  transform-origin: bottom;
  transition: transform 0.3s ease;
}

.content-grid.list-view :deep(.entity-card:hover::before) {
  transform: scaleY(1);
}

/* 强制统一尺寸 - 针对所有相关元素 */
.content-grid.list-view :deep(.ant-card),
.content-grid.list-view :deep(.entity-card),
.content-grid.list-view :deep(.ant-card-bordered) {
  height: 140px !important;
  min-height: 140px !important;
  max-height: 140px !important;
  width: calc(100% - 48px) !important;
  min-width: calc(100% - 48px) !important;
  max-width: calc(100% - 48px) !important;
  box-sizing: border-box !important;
}

/* 确保Ant Design Card内部结构也遵循高度约束 */
.content-grid.list-view :deep(.ant-card-cover) {
  height: 140px !important;
  min-height: 140px !important;
  max-height: 140px !important;
}

.content-grid.list-view :deep(.ant-card-meta) {
  height: auto !important;
}

.content-grid.list-view :deep(.ant-card-meta-detail) {
  height: auto !important;
  overflow: hidden !important;
}

.content-grid.list-view :deep(.ant-card-actions) {
  height: auto !important;
  min-height: auto !important;
}

/* 列表视图交替背景 */
.content-grid.list-view :deep(.ant-col:nth-child(even) .entity-card) {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

/* 列表视图阴影渐变 */
.content-grid.list-view :deep(.card-cover::after) {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 40px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 100%);
  pointer-events: none;
}

/* 列表视图进入动画 */
.content-grid.list-view :deep(.entity-card) {
  animation: slideInFromLeft 0.6s ease-out forwards;
}

.content-grid.list-view :deep(.ant-col:nth-child(1) .entity-card) {
  animation-delay: 0.1s;
}

.content-grid.list-view :deep(.ant-col:nth-child(2) .entity-card) {
  animation-delay: 0.2s;
}

.content-grid.list-view :deep(.ant-col:nth-child(3) .entity-card) {
  animation-delay: 0.3s;
}

.content-grid.list-view :deep(.ant-col:nth-child(4) .entity-card) {
  animation-delay: 0.4s;
}

.content-grid.list-view :deep(.ant-col:nth-child(n+5) .entity-card) {
  animation-delay: 0.5s;
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 列表视图响应式优化 */
@media (max-width: 768px) {
  .content-grid.list-view :deep(.entity-card) {
    flex-direction: column;
    height: auto;
    margin: 0 16px;
  }

  .content-grid.list-view :deep(.card-cover) {
    width: 100%;
    height: 160px;
    border-radius: 16px 16px 0 0;
  }

  .content-grid.list-view :deep(.ant-card-body) {
    padding: 16px;
  }

  .content-grid.list-view :deep(.entity-card:hover) {
    transform: translateY(-4px);
  }
}

/* 分页组件 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 24px 0;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-primary);
}

.custom-pagination {
  display: flex;
  align-items: center;
  gap: 16px;
}

.custom-pagination :deep(.ant-pagination-item) {
  border-radius: 6px;
  border-color: var(--border-tertiary);
}

.custom-pagination :deep(.ant-pagination-item-active) {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.custom-pagination :deep(.ant-pagination-item-active a) {
  color: #ffffff;
}

.custom-pagination :deep(.ant-pagination-item:hover) {
  border-color: var(--primary-color);
}

.custom-pagination :deep(.ant-pagination-item:hover a) {
  color: var(--primary-color);
}

.custom-pagination :deep(.ant-pagination-prev),
.custom-pagination :deep(.ant-pagination-next) {
  border-radius: 6px;
}

.custom-pagination :deep(.ant-pagination-prev:hover),
.custom-pagination :deep(.ant-pagination-next:hover) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* 加载状态优化 */
.loading-container {
  text-align: center;
  padding: 80px 0;
  position: relative;
  z-index: 1;
}

.loading-spinner {
  position: relative;
  display: inline-block;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 4px;
  margin-top: 20px;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff4d4f;
  animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

.loading-text {
  margin-top: 24px;
  color: #666666;
  font-size: 16px;
  font-weight: 500;
}

/* 空状态优化 */
.empty-container {
  text-align: center;
  padding: 80px 0;
  position: relative;
  z-index: 1;
}

.empty-illustration {
  max-width: 300px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.6;
  animation: pulse 2s ease-in-out infinite;
}

.empty-illustration h3 {
  font-size: 20px;
  color: #262626;
  margin-bottom: 12px;
  font-weight: 600;
}

.empty-illustration p {
  color: #8c8c8c;
  font-size: 14px;
  line-height: 1.6;
}

.entities-grid {
  padding: 24px;
  background: transparent;
}

/* 网格布局优化 */
.entities-grid .ant-row {
  margin: -12px;
}

.entities-grid .ant-col {
  padding: 12px;
  transition: all 0.3s ease;
}

/* 卡片悬浮效果 */
.entities-grid .ant-col:hover {
  transform: translateY(-4px);
}

/* 统一的卡片布局 */
.entities-grid .ant-col {
  display: flex;
  align-items: stretch;
}

.entities-grid .ant-col:hover {
  transform: translateY(-4px);
}

/* 实体列入场动画 */
.entity-col {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

/* 关键帧动画 */
@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 0.3; }
}

/* 页脚 */
.modern-footer {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-secondary);
  color: var(--text-secondary);
  text-align: center;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-container {
    padding: 0 16px;
  }

  .search-area {
    margin: 0 20px;
    max-width: 300px;
  }

  .content-wrapper {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .top-header {
    height: var(--header-height-mobile);
  }

  .header-container {
    height: var(--header-height-mobile);
    padding: 0 var(--mobile-padding);
    gap: var(--mobile-margin);
    flex-direction: row;
  }

  .logo-container {
    gap: 6px;
    flex-shrink: 0;
  }

  .logo-main-text {
    font-size: 15px;
  }

  .logo-sub-text {
    font-size: 10px;
  }

  .search-area {
    flex: 1;
    margin: 0 var(--mobile-margin);
    max-width: none;
  }

  .search-area :deep(.ant-input-search) {
    height: var(--mobile-input-height);
  }

  .search-area :deep(.ant-input) {
    height: var(--mobile-input-height);
    font-size: 14px;
  }

  .header-actions {
    flex-shrink: 0;
  }

  .main-layout {
    min-height: calc(100vh - var(--header-height-mobile));
  }

  .left-sidebar {
    position: fixed;
    top: var(--header-height-mobile);
    left: -100%;
    width: var(--sidebar-mobile-width);
    height: calc(100vh - var(--header-height-mobile));
    z-index: 1000;
    transition: left 0.3s ease;
    overflow-y: auto;
  }

  .left-sidebar.mobile-open {
    left: 0;
  }

  .main-content {
    margin-left: 0;
    padding: var(--mobile-padding);
  }

  .content-top {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--mobile-margin);
    padding: var(--mobile-margin) 0;
  }

  .category-tabs {
    flex-wrap: wrap;
    gap: 6px;
    width: 100%;
    overflow-x: auto;
    padding-bottom: 4px;
  }

  .category-tab {
    padding: 8px 16px;
    font-size: 13px;
    height: 36px;
    min-width: fit-content;
    flex-shrink: 0;
  }

  .content-actions {
    width: 100%;
    justify-content: space-between;
  }

  .filter-section {
    padding: var(--mobile-padding);
    margin-bottom: var(--mobile-margin);
  }

  .filter-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--mobile-margin);
  }

  .filter-left {
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;
  }

  .filter-btn {
    height: 36px;
    padding: 0 14px;
    font-size: 13px;
  }

  .content-grid {
    margin: 0 -8px;
    padding: var(--mobile-padding);
  }

  .pagination-wrapper {
    padding: var(--mobile-padding) 0;
  }

  .custom-pagination :deep(.ant-pagination) {
    justify-content: center;
  }

  .custom-pagination :deep(.ant-pagination-item),
  .custom-pagination :deep(.ant-pagination-prev),
  .custom-pagination :deep(.ant-pagination-next) {
    min-width: 36px;
    height: 36px;
    line-height: 34px;
  }

/* 超小屏幕优化 (手机竖屏) */
@media (max-width: 480px) {
  .header-container {
    padding: 0 12px;
    gap: 8px;
  }

  .logo-main-text {
    font-size: 14px;
  }

  .logo-sub-text {
    display: none;
  }

  .search-area {
    margin: 0 8px;
  }

  .main-content {
    padding: 12px;
  }

  .content-grid {
    padding: 12px;
    margin: 0 -6px;
  }

  .category-tab {
    padding: 6px 12px;
    height: 32px;
    font-size: 12px;
  }

  .filter-section {
    padding: 12px;
  }

  .filter-btn {
    height: 32px;
    padding: 0 12px;
    font-size: 12px;
  }
}

/* 平板横屏优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .header-container {
    padding: 0 20px;
  }

  .main-content {
    padding: 20px;
  }

  .content-grid {
    padding: 20px;
  }

  .left-sidebar {
    width: 200px;
  }

  .main-content {
    margin-left: 200px;
  }
}
}

/* EntityCard 组件统一样式 */
.content-grid :deep(.entity-card) {
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border-primary);
  background: var(--bg-primary);
  overflow: hidden;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-grid :deep(.entity-card:hover) {
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.content-grid :deep(.card-cover) {
  border-radius: 12px 12px 0 0;
  overflow: hidden;
  position: relative;
  height: 200px;
}

.content-grid :deep(.cover-image) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.content-grid :deep(.entity-card:hover .cover-image) {
  transform: scale(1.05);
}

.content-grid :deep(.default-cover) {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.content-grid :deep(.entity-icon) {
  font-size: 48px;
  color: #1890ff;
}

.content-grid :deep(.ant-card-body) {
  padding: 16px;
  background: var(--bg-primary);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content-grid :deep(.card-title) {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
  line-height: 1.4;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-grid :deep(.card-description) {
  color: var(--text-tertiary);
  font-size: 14px;
  line-height: 1.5;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-grid :deep(.ant-card-actions) {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  margin: 0;
}

.content-grid :deep(.action-item) {
  color: var(--text-tertiary);
  transition: all 0.2s ease;
  cursor: pointer;
}

.content-grid :deep(.action-item:hover) {
  color: var(--primary-color);
}

.content-grid :deep(.entity-type-badge) {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 2;
}

.content-grid :deep(.category-tag) {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
}
</style>
