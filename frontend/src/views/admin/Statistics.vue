<template>
  <div class="admin-statistics">
    <div class="page-header">
      <h2>数据统计报表</h2>
      <div class="header-actions">
        <a-range-picker
          v-model:value="dateRange"
          @change="handleDateRangeChange"
          :placeholder="['开始日期', '结束日期']"
        />
        <a-button type="primary" @click="refreshData" :loading="loading">
          <template #icon><ReloadOutlined /></template>
          刷新数据
        </a-button>
      </div>
    </div>

    <a-spin :spinning="loading">
      <!-- 图表区域 -->
      <div class="charts-section">
        <a-row :gutter="[24, 24]">
          <!-- 增长趋势图 -->
          <a-col :xs="24" :lg="12">
            <LineChart
              title="内容增长趋势"
              :data="growthTrendData"
              :y-axis-keys="['entities', 'timelines']"
              :series-names="['实体数量', '时间线数量']"
              :colors="['#1890ff', '#52c41a']"
              :height="350"
              :loading="loading"
              @refresh="refreshData"
            />
          </a-col>

          <!-- 用户行为趋势图 -->
          <a-col :xs="24" :lg="12">
            <LineChart
              title="用户行为趋势"
              :data="actionTrendData"
              :y-axis-keys="['views', 'likes', 'flowers']"
              :series-names="['浏览量', '点赞数', '送花数']"
              :colors="['#13c2c2', '#eb2f96', '#fa8c16']"
              :height="350"
              :loading="loading"
              @refresh="refreshData"
            />
          </a-col>

          <!-- 实体类型分布 -->
          <a-col :xs="24" :lg="12">
            <PieChart
              title="实体类型分布"
              :data="entityTypeData"
              :height="350"
              :loading="loading"
              @refresh="refreshData"
            />
          </a-col>

          <!-- 活跃分类排行 -->
          <a-col :xs="24" :lg="12">
            <BarChart
              title="活跃分类排行"
              :data="activeCategoryData"
              x-axis-key="name"
              y-axis-key="entity_count"
              :height="350"
              :loading="loading"
              @refresh="refreshData"
            />
          </a-col>
        </a-row>
      </div>

      <!-- 热门内容表格 -->
      <div class="popular-content-section">
        <a-card title="热门内容排行" :bordered="false">
          <a-table
            :columns="popularColumns"
            :data-source="popularEntities"
            :pagination="false"
            :loading="loading"
            size="middle"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'type'">
                <a-tag :color="getTypeColor(record.type)">
                  {{ getTypeText(record.type) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'total_interactions'">
                <a-statistic
                  :value="record.total_interactions"
                  :value-style="{ fontSize: '14px' }"
                />
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import LineChart from '../../components/charts/LineChart.vue'
import PieChart from '../../components/charts/PieChart.vue'
import BarChart from '../../components/charts/BarChart.vue'
import { userActionsAPI } from '../../api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const dateRange = ref([])
const dashboardStats = ref({
  overview: {},
  user_actions: {},
  entity_types: {},
  growth_trends: [],
  popular_entities: [],
  active_categories: []
})

// 计算属性
const growthTrendData = computed(() => {
  return dashboardStats.value.growth_trends || []
})

const actionTrendData = computed(() => {
  return dashboardStats.value.growth_trends || []
})

const entityTypeData = computed(() => {
  const types = dashboardStats.value.entity_types || {}
  return Object.entries(types).map(([key, value]) => ({
    name: getTypeText(key),
    value: value
  }))
})

const activeCategoryData = computed(() => {
  return dashboardStats.value.active_categories || []
})

const popularEntities = computed(() => {
  return dashboardStats.value.popular_entities || []
})

// 表格列定义
const popularColumns = [
  {
    title: '排名',
    key: 'index',
    width: 80,
    customRender: ({ index }) => index + 1
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '总互动数',
    dataIndex: 'total_interactions',
    key: 'total_interactions',
    width: 120,
    sorter: (a, b) => a.total_interactions - b.total_interactions
  }
]

// 方法
const getTypeText = (type) => {
  const typeMap = {
    person: '人物',
    event: '事件',
    enterprise: '企业'
  }
  return typeMap[type] || type
}

const getTypeColor = (type) => {
  const colorMap = {
    person: 'blue',
    event: 'green',
    enterprise: 'orange'
  }
  return colorMap[type] || 'default'
}

const loadDashboardStats = async () => {
  try {
    loading.value = true
    const response = await userActionsAPI.getDashboardStats()

    // 后端直接返回数据对象，不包装在success字段中
    dashboardStats.value = response
  } catch (error) {
    console.error('加载统计数据失败:', error)
    message.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadDashboardStats()
}

const handleDateRangeChange = (dates) => {
  // TODO: 根据日期范围筛选数据
  console.log('日期范围变更:', dates)
}

// 组件挂载时加载数据
onMounted(() => {
  loadDashboardStats()
})
</script>

<style scoped>
.admin-statistics {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 4px;
}

.page-header h2 {
  margin: 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.charts-section {
  margin-bottom: 24px;
}

.popular-content-section {
  margin-bottom: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-statistics {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .page-header h2 {
    font-size: 20px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
