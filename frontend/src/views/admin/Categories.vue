<template>
  <div class="categories-page">
    <div class="page-header">
      <h1>分类管理</h1>
      <a-button type="primary" @click="showAddModal">
        <template #icon><PlusOutlined /></template>
        新增分类
      </a-button>
    </div>

    <!-- 搜索区域 -->
    <a-card class="search-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-input
            v-model:value="searchForm.search"
            placeholder="请输入分类名称搜索"
            allow-clear
            @pressEnter="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" @click="handleSearch">
            <template #icon><SearchOutlined /></template>
            搜索
          </a-button>
        </a-col>
        <a-col :span="4">
          <a-button @click="handleReset">
            <template #icon><ReloadOutlined /></template>
            重置
          </a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 分类列表 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="categoriesStore.categories"
        :loading="categoriesStore.loading"
        :pagination="paginationConfig"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="showEditModal(record)">
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个分类吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record.id)"
              >
                <a-button type="link" size="small" danger>
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑分类模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑分类' : '新增分类'"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="分类名称" name="name">
          <a-input
            v-model:value="formData.name"
            placeholder="请输入分类名称"
            :maxlength="50"
            show-count
          />
        </a-form-item>

        <a-form-item label="分类描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入分类描述"
            :rows="3"
            :maxlength="200"
            show-count
          />
        </a-form-item>

        <a-form-item label="排序权重" name="sort_order">
          <a-input-number
            v-model:value="formData.sort_order"
            placeholder="请输入排序权重（数值越大越靠前）"
            :min="0"
            :max="9999"
            style="width: 100%"
          />
          <div style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
            排序权重用于控制分类在列表中的显示顺序，数值越大越靠前
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { useCategoriesStore } from '../../stores/categories'

export default {
  name: 'AdminCategories',
  components: {
    PlusOutlined,
    SearchOutlined,
    ReloadOutlined,
    EditOutlined,
    DeleteOutlined
  },
  setup() {
    const categoriesStore = useCategoriesStore()
    const formRef = ref()

    // 响应式数据
    const modalVisible = ref(false)
    const isEdit = ref(false)
    const submitLoading = ref(false)
    const currentEditId = ref(null)

    // 搜索表单
    const searchForm = reactive({
      search: '',
      page: 1,
      size: 10
    })

    // 表单数据
    const formData = reactive({
      name: '',
      description: '',
      sort_order: 0
    })

    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入分类名称', trigger: 'blur' },
        { min: 2, max: 50, message: '分类名称长度应为2-50个字符', trigger: 'blur' }
      ],
      description: [
        { max: 200, message: '描述长度不能超过200个字符', trigger: 'blur' }
      ],
      sort_order: [
        { required: true, message: '请输入排序权重', trigger: 'blur' },
        { type: 'number', min: 0, max: 9999, message: '排序权重应为0-9999的数字', trigger: 'blur' }
      ]
    }

    // 表格列配置
    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80
      },
      {
        title: '分类名称',
        dataIndex: 'name',
        key: 'name',
        ellipsis: true
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        ellipsis: true
      },
      {
        title: '排序权重',
        dataIndex: 'sort_order',
        key: 'sort_order',
        width: 120,
        sorter: true
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180,
        sorter: true
      },
      {
        title: '操作',
        key: 'action',
        width: 150,
        fixed: 'right'
      }
    ]

    // 分页配置
    const paginationConfig = computed(() => ({
      current: categoriesStore.pagination.page,
      pageSize: categoriesStore.pagination.size,
      total: categoriesStore.pagination.total,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
    }))

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    }

    // 加载分类列表
    const loadCategories = async () => {
      try {
        await categoriesStore.fetchCategories(searchForm)
      } catch (error) {
        console.error('加载分类列表失败:', error)
        message.error('加载分类列表失败')
      }
    }

    // 搜索
    const handleSearch = () => {
      searchForm.page = 1
      loadCategories()
    }

    // 重置搜索
    const handleReset = () => {
      searchForm.search = ''
      searchForm.page = 1
      loadCategories()
    }

    // 表格变化处理
    const handleTableChange = (pagination, filters, sorter) => {
      searchForm.page = pagination.current
      searchForm.size = pagination.pageSize
      loadCategories()
    }

    // 显示添加模态框
    const showAddModal = () => {
      isEdit.value = false
      currentEditId.value = null
      resetForm()
      modalVisible.value = true
    }

    // 显示编辑模态框
    const showEditModal = (record) => {
      isEdit.value = true
      currentEditId.value = record.id
      formData.name = record.name
      formData.description = record.description || ''
      formData.sort_order = record.sort_order || 0
      modalVisible.value = true
    }

    // 重置表单
    const resetForm = () => {
      formData.name = ''
      formData.description = ''
      formData.sort_order = 0
      if (formRef.value) {
        formRef.value.resetFields()
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        submitLoading.value = true

        if (isEdit.value) {
          // 编辑分类
          await categoriesStore.updateCategory(currentEditId.value, formData)
          message.success('分类更新成功')
        } else {
          // 添加分类
          await categoriesStore.createCategory(formData)
          message.success('分类创建成功')
        }

        modalVisible.value = false
        loadCategories()
      } catch (error) {
        console.error('提交失败:', error)
        if (error.errors) {
          // 表单验证错误
          return
        }
        message.error(isEdit.value ? '分类更新失败' : '分类创建失败')
      } finally {
        submitLoading.value = false
      }
    }

    // 取消操作
    const handleCancel = () => {
      modalVisible.value = false
      resetForm()
    }

    // 删除分类
    const handleDelete = async (id) => {
      try {
        await categoriesStore.deleteCategory(id)
        message.success('分类删除成功')
        loadCategories()
      } catch (error) {
        console.error('删除失败:', error)
        message.error('分类删除失败')
      }
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadCategories()
    })

    return {
      categoriesStore,
      formRef,
      modalVisible,
      isEdit,
      submitLoading,
      searchForm,
      formData,
      formRules,
      columns,
      paginationConfig,
      formatDate,
      handleSearch,
      handleReset,
      handleTableChange,
      showAddModal,
      showEditModal,
      handleSubmit,
      handleCancel,
      handleDelete
    }
  }
}
</script>

<style scoped>
.categories-page {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.search-card {
  margin-bottom: 16px;
}

.search-card .ant-card-body {
  padding: 16px;
}

/* 表格样式优化 */
:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

/* 模态框样式 */
:deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .categories-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .page-header h1 {
    text-align: center;
  }

  .search-card .ant-row {
    flex-direction: column;
  }

  .search-card .ant-col {
    width: 100% !important;
    margin-bottom: 8px;
  }
}
</style>
