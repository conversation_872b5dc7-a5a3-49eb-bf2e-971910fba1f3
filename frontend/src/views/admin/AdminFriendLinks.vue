<template>
  <div class="admin-friend-links">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <LinkOutlined class="title-icon" />
          友情链接管理
        </h1>
        <p class="page-description">管理网站的友情链接</p>
      </div>
      <div class="header-right">
        <a-button type="primary" @click="showCreateModal">
          <PlusOutlined />
          添加友情链接
        </a-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-input
            v-model:value="searchForm.search"
            placeholder="搜索友情链接名称或描述"
            allow-clear
            @change="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchForm.is_active"
            placeholder="状态筛选"
            allow-clear
            @change="handleSearch"
          >
            <a-select-option :value="true">启用</a-select-option>
            <a-select-option :value="false">禁用</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchForm.sort_by"
            @change="handleSearch"
          >
            <a-select-option value="sort_order">排序权重</a-select-option>
            <a-select-option value="created_at">创建时间</a-select-option>
            <a-select-option value="name">名称</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchForm.sort_order"
            @change="handleSearch"
          >
            <a-select-option value="desc">降序</a-select-option>
            <a-select-option value="asc">升序</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-button @click="resetSearch">重置</a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 友情链接表格 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="friendLinks"
        :loading="loading"
        :pagination="paginationConfig"
        @change="handleTableChange"
        row-key="id"
      >
        <!-- 图标列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'icon'">
            <div class="icon-cell">
              <img
                v-if="record.icon_url"
                :src="record.icon_url"
                :alt="record.name"
                class="link-icon"
                @error="handleImageError"
              />
              <LinkOutlined v-else class="default-icon" />
            </div>
          </template>

          <!-- 名称列 -->
          <template v-else-if="column.key === 'name'">
            <div class="name-cell">
              <div class="link-name">{{ record.name }}</div>
              <div class="link-url">{{ record.url }}</div>
            </div>
          </template>

          <!-- 描述列 -->
          <template v-else-if="column.key === 'description'">
            <div class="description-cell">
              {{ record.description || '-' }}
            </div>
          </template>

          <!-- 状态列 -->
          <template v-else-if="column.key === 'is_active'">
            <a-tag :color="record.is_active ? 'success' : 'default'">
              {{ record.is_active ? '启用' : '禁用' }}
            </a-tag>
          </template>

          <!-- 排序权重列 -->
          <template v-else-if="column.key === 'sort_order'">
            <a-tag color="blue">{{ record.sort_order }}</a-tag>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="editFriendLink(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个友情链接吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteFriendLink(record.id)"
              >
                <a-button type="link" size="small" danger>
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 创建/编辑友情链接模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEditing ? '编辑友情链接' : '添加友情链接'"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="600px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="链接名称" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入链接名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="链接地址" name="url">
              <a-input v-model:value="formData.url" placeholder="请输入链接地址" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="链接描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入链接描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="图标地址" name="icon_url">
          <a-input v-model:value="formData.icon_url" placeholder="请输入图标地址（可选）" />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="排序权重" name="sort_order">
              <a-input-number
                v-model:value="formData.sort_order"
                :min="0"
                :max="9999"
                placeholder="数字越大越靠前"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="is_active">
              <a-switch
                v-model:checked="formData.is_active"
                checked-children="启用"
                un-checked-children="禁用"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import {
  LinkOutlined,
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useFriendLinksStore } from '@/stores/friend-links'
import dayjs from 'dayjs'

// 状态管理
const friendLinksStore = useFriendLinksStore()
const { friendLinks, loading, pagination } = storeToRefs(friendLinksStore)

// 响应式数据
const modalVisible = ref(false)
const modalLoading = ref(false)
const isEditing = ref(false)
const editingId = ref(null)
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  search: '',
  is_active: null,
  sort_by: 'sort_order',
  sort_order: 'desc'
})

// 表单数据
const formData = reactive({
  name: '',
  url: '',
  description: '',
  icon_url: '',
  sort_order: 0,
  is_active: true
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入链接名称', trigger: 'blur' },
    { min: 1, max: 100, message: '链接名称长度在1-100个字符', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入链接地址', trigger: 'blur' },
    { min: 1, max: 500, message: '链接地址长度在1-500个字符', trigger: 'blur' }
  ],
  description: [
    { max: 1000, message: '描述长度不能超过1000个字符', trigger: 'blur' }
  ],
  icon_url: [
    { max: 500, message: '图标地址长度不能超过500个字符', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', min: 0, max: 9999, message: '排序权重范围0-9999', trigger: 'blur' }
  ]
}

// 表格列配置
const columns = [
  {
    title: '图标',
    key: 'icon',
    width: 80,
    align: 'center'
  },
  {
    title: '链接信息',
    key: 'name',
    width: 300
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: true
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    align: 'center'
  },
  {
    title: '排序',
    key: 'sort_order',
    width: 80,
    align: 'center'
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center'
  }
]

// 分页配置
const paginationConfig = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}))

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})

// 获取数据
const fetchData = () => {
  friendLinksStore.setFilters(searchForm)
  friendLinksStore.fetchFriendLinks()
}

// 搜索处理
const handleSearch = () => {
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    search: '',
    is_active: null,
    sort_by: 'sort_order',
    sort_order: 'desc'
  })
  friendLinksStore.resetFilters()
  fetchData()
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
  friendLinksStore.setPagination(pag.current, pag.pageSize)
  fetchData()
}

// 显示创建模态框
const showCreateModal = () => {
  isEditing.value = false
  editingId.value = null
  resetFormData()
  modalVisible.value = true
}

// 编辑友情链接
const editFriendLink = (record) => {
  isEditing.value = true
  editingId.value = record.id
  Object.assign(formData, {
    name: record.name,
    url: record.url,
    description: record.description || '',
    icon_url: record.icon_url || '',
    sort_order: record.sort_order,
    is_active: record.is_active
  })
  modalVisible.value = true
}

// 删除友情链接
const deleteFriendLink = async (id) => {
  try {
    await friendLinksStore.deleteFriendLink(id)
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 模态框确定处理
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true

    if (isEditing.value) {
      await friendLinksStore.updateFriendLink(editingId.value, formData)
    } else {
      await friendLinksStore.createFriendLink(formData)
    }

    modalVisible.value = false
    resetFormData()
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 模态框取消处理
const handleModalCancel = () => {
  modalVisible.value = false
  resetFormData()
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    name: '',
    url: '',
    description: '',
    icon_url: '',
    sort_order: 0,
    is_active: true
  })
  formRef.value?.resetFields()
}

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 图片加载错误处理
const handleImageError = (event) => {
  event.target.style.display = 'none'
}
</script>

<script>
export default {
  name: 'AdminFriendLinks'
}
</script>

<style scoped>
.admin-friend-links {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  color: #1890ff;
}

.page-description {
  color: #666;
  margin: 0;
}

.header-right {
  flex-shrink: 0;
}

.search-section {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 表格单元格样式 */
.icon-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.link-icon {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  object-fit: cover;
}

.default-icon {
  font-size: 24px;
  color: #ccc;
}

.name-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.link-name {
  font-weight: 500;
  color: #333;
}

.link-url {
  font-size: 12px;
  color: #999;
  font-family: monospace;
}

.description-cell {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-friend-links {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-section .ant-row {
    flex-direction: column;
  }

  .search-section .ant-col {
    width: 100% !important;
    margin-bottom: 8px;
  }
}
</style>
