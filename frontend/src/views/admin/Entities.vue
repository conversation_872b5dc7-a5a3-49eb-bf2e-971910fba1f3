<template>
  <div class="entities-page">
    <div class="page-header">
      <h1>实体管理</h1>
      <a-button type="primary" @click="showAddModal">
        <template #icon><PlusOutlined /></template>
        新增实体
      </a-button>
    </div>

    <!-- 搜索筛选区域 -->
    <a-card class="search-card" :bordered="false">
      <div class="search-form">
        <a-row :gutter="[16, 16]" align="middle">
          <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <div class="search-item">
              <label class="search-label">实体名称</label>
              <a-input
                v-model:value="searchForm.search"
                placeholder="请输入实体名称"
                allow-clear
                @pressEnter="handleSearch"
              >
                <template #prefix>
                  <SearchOutlined style="color: #bfbfbf" />
                </template>
              </a-input>
            </div>
          </a-col>

          <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <div class="search-item">
              <label class="search-label">分类筛选</label>
              <a-select
                v-model:value="searchForm.category_id"
                placeholder="全部分类"
                allow-clear
                style="width: 100%"
                @change="handleSearch"
              >
                <a-select-option
                  v-for="category in categoriesStore.categories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </div>
          </a-col>

          <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <div class="search-item">
              <label class="search-label">类型筛选</label>
              <a-select
                v-model:value="searchForm.entity_type"
                placeholder="全部类型"
                allow-clear
                style="width: 100%"
                @change="handleSearch"
              >
                <a-select-option value="person">
                  <span style="color: #52c41a">
                    <UserOutlined style="margin-right: 6px" />
                    人物
                  </span>
                </a-select-option>
                <a-select-option value="event">
                  <span style="color: #fa8c16">
                    <CalendarOutlined style="margin-right: 6px" />
                    事件
                  </span>
                </a-select-option>
                <a-select-option value="enterprise">
                  <span style="color: #1890ff">
                    <BankOutlined style="margin-right: 6px" />
                    企业
                  </span>
                </a-select-option>
              </a-select>
            </div>
          </a-col>

          <a-col :xs="24" :sm="12" :md="24" :lg="6" :xl="6">
            <div class="search-actions">
              <a-space>
                <a-button type="primary" @click="handleSearch" :loading="entitiesStore.loading">
                  <template #icon><SearchOutlined /></template>
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
              </a-space>
            </div>
          </a-col>
        </a-row>

        <!-- 快速筛选标签 -->
        <div class="quick-filters" v-if="hasActiveFilters">
          <a-divider style="margin: 16px 0 12px 0" />
          <div class="filter-tags">
            <span class="filter-label">当前筛选：</span>
            <a-tag
              v-if="searchForm.search"
              closable
              color="blue"
              @close="clearSearchFilter('search')"
            >
              名称：{{ searchForm.search }}
            </a-tag>
            <a-tag
              v-if="searchForm.category_id"
              closable
              color="green"
              @close="clearSearchFilter('category_id')"
            >
              分类：{{ getCategoryName(searchForm.category_id) }}
            </a-tag>
            <a-tag
              v-if="searchForm.entity_type"
              closable
              color="orange"
              @close="clearSearchFilter('entity_type')"
            >
              类型：{{ getTypeLabel(searchForm.entity_type) }}
            </a-tag>
            <a-button type="link" size="small" @click="handleReset" style="padding: 0; height: auto;">
              清空所有
            </a-button>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 实体列表 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="entitiesStore.entities"
        :loading="entitiesStore.loading"
        :pagination="paginationConfig"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'avatar'">
            <a-avatar
              :src="record.avatar"
              :size="40"
              shape="square"
            >
              {{ record.name.charAt(0) }}
            </a-avatar>
          </template>
          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              <component :is="getTypeIcon(record.type)" style="margin-right: 4px" />
              {{ getTypeLabel(record.type) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'category'">
            <a-tag color="blue">
              {{ getCategoryName(record.category_id) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'summary'">
            <div class="summary-text">
              {{ record.summary || '-' }}
            </div>
          </template>
          <template v-else-if="column.key === 'stats'">
            <a-space direction="vertical" size="small">
              <span><EyeOutlined /> {{ record.view_count || 0 }}</span>
              <span><HeartOutlined /> {{ record.like_count || 0 }}</span>
              <span><GiftOutlined /> {{ record.flower_count || 0 }}</span>
            </a-space>
          </template>
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="showEditModal(record)">
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个实体吗？删除后相关时间线也会被删除！"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record.id)"
              >
                <a-button type="link" size="small" danger>
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑实体模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑实体' : '新增实体'"
      :confirm-loading="submitLoading"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="实体名称" name="name">
              <a-input
                v-model:value="formData.name"
                placeholder="请输入实体名称"
                :maxlength="200"
                show-count
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="实体类型" name="type">
              <a-select
                v-model:value="formData.type"
                placeholder="请选择实体类型"
              >
                <a-select-option value="person">人物</a-select-option>
                <a-select-option value="event">事件</a-select-option>
                <a-select-option value="enterprise">企业</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="所属分类" name="category_id">
              <a-select
                v-model:value="formData.category_id"
                placeholder="请选择分类"
              >
                <a-select-option
                  v-for="category in categoriesStore.categories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="头像/图标" name="avatar">
              <MediaSelector
                v-model:value="formData.avatar"
                placeholder="选择头像或图标"
                media-type="image"
                :preview-size="{ width: 80, height: 80 }"
                default-category="avatar"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="简介" name="summary">
          <a-textarea
            v-model:value="formData.summary"
            placeholder="请输入简介"
            :rows="2"
            :maxlength="500"
            show-count
          />
        </a-form-item>

        <a-form-item label="详细描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入详细描述"
            :rows="4"
            :maxlength="2000"
            show-count
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="开始时间" name="birth_date">
              <a-date-picker
                v-model:value="formData.birth_date"
                placeholder="请选择开始时间"
                style="width: 100%"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="结束时间" name="death_date">
              <a-date-picker
                v-model:value="formData.death_date"
                placeholder="请选择结束时间"
                style="width: 100%"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  HeartOutlined,
  GiftOutlined,
  UserOutlined,
  CalendarOutlined,
  BankOutlined
} from '@ant-design/icons-vue'
import { useEntitiesStore } from '../../stores/entities'
import { useCategoriesStore } from '../../stores/categories'
import MediaSelector from '../../components/MediaSelector.vue'

export default {
  name: 'AdminEntities',
  components: {
    PlusOutlined,
    SearchOutlined,
    ReloadOutlined,
    EditOutlined,
    DeleteOutlined,
    EyeOutlined,
    HeartOutlined,
    GiftOutlined,
    UserOutlined,
    CalendarOutlined,
    BankOutlined,
    MediaSelector
  },
  setup() {
    const entitiesStore = useEntitiesStore()
    const categoriesStore = useCategoriesStore()
    const formRef = ref()

    // 响应式数据
    const modalVisible = ref(false)
    const isEdit = ref(false)
    const submitLoading = ref(false)
    const currentEditId = ref(null)

    // 搜索表单
    const searchForm = reactive({
      search: '',
      category_id: null,
      entity_type: null,
      page: 1,
      size: 10
    })

    // 表单数据
    const formData = reactive({
      name: '',
      type: '',
      category_id: null,
      avatar: '',
      summary: '',
      description: '',
      birth_date: null,
      death_date: null
    })

    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入实体名称', trigger: 'blur' },
        { min: 2, max: 200, message: '实体名称长度应为2-200个字符', trigger: 'blur' }
      ],
      type: [
        { required: true, message: '请选择实体类型', trigger: 'change' }
      ],
      category_id: [
        { required: true, message: '请选择所属分类', trigger: 'change' }
      ],
      summary: [
        { max: 500, message: '简介长度不能超过500个字符', trigger: 'blur' }
      ],
      description: [
        { max: 2000, message: '描述长度不能超过2000个字符', trigger: 'blur' }
      ]
    }

    // 表格列配置
    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80
      },
      {
        title: '头像',
        dataIndex: 'avatar',
        key: 'avatar',
        width: 80
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 150,
        ellipsis: true
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        width: 80
      },
      {
        title: '分类',
        dataIndex: 'category_id',
        key: 'category',
        width: 100
      },
      {
        title: '简介',
        dataIndex: 'summary',
        key: 'summary',
        ellipsis: true
      },
      {
        title: '统计',
        key: 'stats',
        width: 80
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180,
        sorter: true
      },
      {
        title: '操作',
        key: 'action',
        width: 150,
        fixed: 'right'
      }
    ]

    // 分页配置
    const paginationConfig = computed(() => ({
      current: entitiesStore.pagination.page,
      pageSize: entitiesStore.pagination.size,
      total: entitiesStore.pagination.total,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
    }))

    // 获取类型颜色
    const getTypeColor = (type) => {
      const colors = {
        person: 'green',
        event: 'orange',
        enterprise: 'blue'
      }
      return colors[type] || 'default'
    }

    // 获取类型标签
    const getTypeLabel = (type) => {
      const labels = {
        person: '人物',
        event: '事件',
        enterprise: '企业'
      }
      return labels[type] || type
    }

    // 获取类型图标组件
    const getTypeIcon = (type) => {
      const icons = {
        person: UserOutlined,
        event: CalendarOutlined,
        enterprise: BankOutlined
      }
      return icons[type] || UserOutlined
    }

    // 获取分类名称
    const getCategoryName = (categoryId) => {
      const category = categoriesStore.categories.find(c => c.id === categoryId)
      return category ? category.name : '未知分类'
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return dayjs(dateString).format('YYYY-MM-DD HH:mm')
    }

    // 检查是否有活跃的筛选条件
    const hasActiveFilters = computed(() => {
      return !!(searchForm.search || searchForm.category_id || searchForm.entity_type)
    })

    // 清除单个筛选条件
    const clearSearchFilter = (filterKey) => {
      searchForm[filterKey] = filterKey === 'search' ? '' : null
      handleSearch()
    }

    // 加载实体列表
    const loadEntities = async () => {
      try {
        await entitiesStore.fetchEntities(searchForm)
      } catch (error) {
        console.error('加载实体列表失败:', error)
        message.error('加载实体列表失败')
      }
    }

    // 加载分类列表
    const loadCategories = async () => {
      try {
        await categoriesStore.fetchCategories({ page: 1, size: 100 })
      } catch (error) {
        console.error('加载分类列表失败:', error)
      }
    }

    // 搜索
    const handleSearch = () => {
      searchForm.page = 1
      entitiesStore.setFilters({
        search: searchForm.search,
        category_id: searchForm.category_id,
        entity_type: searchForm.entity_type
      })
      loadEntities()
    }

    // 重置搜索
    const handleReset = () => {
      searchForm.search = ''
      searchForm.category_id = null
      searchForm.entity_type = null
      searchForm.page = 1
      entitiesStore.setFilters({
        search: '',
        category_id: null,
        entity_type: null
      })
      loadEntities()
    }

    // 表格变化处理
    const handleTableChange = (pagination, filters, sorter) => {
      searchForm.page = pagination.current
      searchForm.size = pagination.pageSize
      loadEntities()
    }

    // 显示添加模态框
    const showAddModal = () => {
      isEdit.value = false
      currentEditId.value = null
      resetForm()
      modalVisible.value = true
    }

    // 显示编辑模态框
    const showEditModal = (record) => {
      isEdit.value = true
      currentEditId.value = record.id
      formData.name = record.name
      formData.type = record.type
      formData.category_id = record.category_id
      formData.avatar = record.avatar || ''
      formData.summary = record.summary || ''
      formData.description = record.description || ''
      formData.birth_date = record.birth_date ? dayjs(record.birth_date) : null
      formData.death_date = record.death_date ? dayjs(record.death_date) : null
      modalVisible.value = true
    }

    // 重置表单
    const resetForm = () => {
      formData.name = ''
      formData.type = ''
      formData.category_id = null
      formData.avatar = ''
      formData.summary = ''
      formData.description = ''
      formData.birth_date = null
      formData.death_date = null
      if (formRef.value) {
        formRef.value.resetFields()
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        submitLoading.value = true

        // 处理日期格式
        const submitData = {
          ...formData,
          birth_date: formData.birth_date ? formData.birth_date.format('YYYY-MM-DD HH:mm:ss') : null,
          death_date: formData.death_date ? formData.death_date.format('YYYY-MM-DD HH:mm:ss') : null
        }

        if (isEdit.value) {
          // 编辑实体
          await entitiesStore.updateEntity(currentEditId.value, submitData)
          message.success('实体更新成功')
        } else {
          // 添加实体
          await entitiesStore.createEntity(submitData)
          message.success('实体创建成功')
        }

        modalVisible.value = false
        loadEntities()
      } catch (error) {
        console.error('提交失败:', error)
        if (error.errors) {
          // 表单验证错误
          return
        }
        message.error(isEdit.value ? '实体更新失败' : '实体创建失败')
      } finally {
        submitLoading.value = false
      }
    }

    // 取消操作
    const handleCancel = () => {
      modalVisible.value = false
      resetForm()
    }

    // 删除实体
    const handleDelete = async (id) => {
      try {
        await entitiesStore.deleteEntity(id)
        message.success('实体删除成功')
        loadEntities()
      } catch (error) {
        console.error('删除失败:', error)
        message.error('实体删除失败')
      }
    }

    // 组件挂载时加载数据
    onMounted(async () => {
      await loadCategories()
      await loadEntities()
    })

    return {
      entitiesStore,
      categoriesStore,
      formRef,
      modalVisible,
      isEdit,
      submitLoading,
      searchForm,
      formData,
      formRules,
      columns,
      paginationConfig,
      getTypeColor,
      getTypeLabel,
      getTypeIcon,
      getCategoryName,
      formatDate,
      hasActiveFilters,
      clearSearchFilter,
      handleSearch,
      handleReset,
      handleTableChange,
      showAddModal,
      showEditModal,
      handleSubmit,
      handleCancel,
      handleDelete
    }
  }
}
</script>

<style scoped>
.entities-page {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.search-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.search-card .ant-card-body {
  padding: 20px;
}

.search-form {
  width: 100%;
}

.search-item {
  width: 100%;
}

.search-label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  font-weight: 500;
  color: #666;
}

.search-actions {
  display: flex;
  align-items: flex-end;
  height: 100%;
  padding-top: 21px;
}

/* 快速筛选标签样式 */
.quick-filters {
  margin-top: 8px;
}

.filter-tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-label {
  font-size: 13px;
  color: #666;
  margin-right: 8px;
  font-weight: 500;
}

.filter-tags .ant-tag {
  margin: 0;
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.summary-text {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 表格样式优化 */
:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

/* 模态框样式 */
:deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #f0f0f0;
}

/* 统计数据样式 */
.ant-space-vertical .ant-space-item {
  font-size: 12px;
  color: #666;
}

.ant-space-vertical .ant-space-item .anticon {
  margin-right: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .entities-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .page-header h1 {
    text-align: center;
  }

  .search-card .ant-card-body {
    padding: 16px;
  }

  .search-actions {
    padding-top: 0;
    margin-top: 16px;
    justify-content: center;
  }

  .search-actions .ant-space {
    width: 100%;
    justify-content: center;
  }

  .search-actions .ant-btn {
    flex: 1;
    max-width: 120px;
  }

  .summary-text {
    max-width: 150px;
  }

  .filter-tags {
    justify-content: flex-start;
  }

  .filter-label {
    width: 100%;
    margin-bottom: 8px;
  }
}

@media (max-width: 576px) {
  .search-card .ant-card-body {
    padding: 12px;
  }

  .search-label {
    font-size: 12px;
    margin-bottom: 4px;
  }

  .search-actions .ant-btn {
    font-size: 12px;
    padding: 4px 12px;
  }
}
</style>
