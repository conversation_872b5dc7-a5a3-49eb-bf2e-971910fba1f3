<template>
  <div>
    <a-page-header title="关于我们设置" sub-title="编辑关于我们页面的内容和打赏图片" />
    <a-card>
      <a-form :model="formState" @finish="onFinish" layout="vertical">
        <a-form-item label="内容" name="content">
          <MdEditor v-model="formState.content" @uploadImg="onUploadImg" />
        </a-form-item>
        <a-form-item label="打赏图片" name="donation_image_url">
          <a-upload
            v-model:file-list="fileList"
            name="donation"
            list-type="picture-card"
            :show-upload-list="false"
            :before-upload="beforeUpload"
            @change="handleUploadChange"
          >
            <img v-if="formState.donation_image_url" :src="formState.donation_image_url" alt="donation" style="width: 100%" />
            <div v-else>
              <loading-outlined v-if="uploading"></loading-outlined>
              <plus-outlined v-else></plus-outlined>
              <div class="ant-upload-text">上传</div>
            </div>
          </a-upload>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" :loading="loading">
            保存
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { message, Upload } from 'ant-design-vue';
import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue';
import { MdEditor } from 'md-editor-v3';
import 'md-editor-v3/lib/style.css';
import { aboutAPI } from '../../api/about';
import { mediaAPI } from '../../api/media';

export default {
  name: 'AdminAboutUs',
  components: {
    MdEditor,
    PlusOutlined,
    LoadingOutlined,
  },
  setup() {
    const formState = ref({
      content: '',
      donation_image_url: '',
    });
    const loading = ref(false);
    const uploading = ref(false);
    const fileList = ref([]);

    const fetchAboutUs = async () => {
      try {
        const response = await aboutAPI.getAboutUs();
        if (response.data) {
          formState.value = response.data;
        }
      } catch (error) {
        console.error('获取关于我们信息失败:', error);
        message.error('获取关于我们信息失败');
      }
    };

    const onFinish = async () => {
      loading.value = true;
      try {
        await aboutAPI.updateAboutUs(formState.value);
        message.success('保存成功');
      } catch (error) {
        console.error('保存失败:', error);
        message.error('保存失败');
      } finally {
        loading.value = false;
      }
    };

    const onUploadImg = async (files, callback) => {
      const formData = new FormData();
      // We upload one file at a time in the editor
      formData.append('file', files);
      try {
        const res = await mediaAPI.uploadFile(formData);
        // The `res` object is already response.data due to the axios interceptor.
        // The correct path is res.file.file_url
        callback([res.file.file_url]);
      } catch (error) {
        console.error("Editor image upload failed:", error);
        message.error('图片上传失败');
      }
    };

    const handleUploadChange = info => {
      if (info.file.status === 'uploading') {
        uploading.value = true;
        return;
      }
      if (info.file.status === 'done') {
        uploading.value = false;
        formState.value.donation_image_url = info.file.response.url;
      }
      if (info.file.status === 'error') {
        uploading.value = false;
        message.error('上传失败');
      }
    };
    
    const beforeUpload = file => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('你只能上传 JPG/PNG 格式的图片!');
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('图片大小必须小于 2MB!');
      }
      
      if(isJpgOrPng && isLt2M) {
          const formData = new FormData();
          formData.append('file', file);
          uploading.value = true;
           mediaAPI.uploadFile(formData).then(res => {
               // The `res` object is already response.data due to the axios interceptor.
               // The correct path is res.file.file_url
               formState.value.donation_image_url = res.file.file_url;
               uploading.value = false;
               message.success('上传成功');
           }).catch((err)=> {
               console.error("Donation image upload failed:", err);
               uploading.value = false;
               message.error('上传失败');
           })
      }
      
      return false; // 阻止默认上传
    };

    onMounted(() => {
      fetchAboutUs();
    });

    return {
      formState,
      loading,
      uploading,
      fileList,
      onFinish,
      onUploadImg,
      handleUploadChange,
      beforeUpload
    };
  },
};
</script>
