<template>
  <div class="ai-create-container">
    <a-card title="AI智能创建时间线" class="main-card">
      <template #extra>
        <a-tag color="blue">
          <RobotOutlined />
          AI驱动
        </a-tag>
      </template>

      <!-- 输入区域 -->
      <div class="input-section">
        <a-form layout="vertical" :model="form">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="实体名称" required>
                <a-input
                  v-model:value="form.entityName"
                  placeholder="请输入要创建时间线的实体名称，如：项羽、李白等"
                  size="large"
                  :disabled="loading"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="实体类型">
                <a-select v-model:value="form.entityType" size="large" :disabled="loading">
                  <a-select-option value="person">人物</a-select-option>
                  <a-select-option value="event">事件</a-select-option>
                  <a-select-option value="enterprise">企业</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="分类">
                <a-select
                  v-model:value="form.categoryId"
                  size="large"
                  :disabled="loading || categoriesLoading"
                  :loading="categoriesLoading"
                  placeholder="请选择分类"
                >
                  <a-select-option
                    v-for="category in categories"
                    :key="category.id"
                    :value="category.id"
                  >
                    {{ category.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item>
            <a-button
              type="primary"
              size="large"
              :loading="loading"
              @click="generateTimeline"
              :disabled="!form.entityName.trim()"
              block
            >
              <template #icon><RobotOutlined /></template>
              {{ loading ? 'AI正在生成中...' : '开始AI生成时间线' }}
            </a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 生成过程显示 -->
      <div v-if="generating" class="generation-section">
        <a-divider>
          <span class="divider-text">
            <LoadingOutlined spin />
            AI生成过程
          </span>
        </a-divider>
        
        <div class="generation-log">
          <div v-for="(log, index) in generationLogs" :key="index" class="log-item">
            <a-tag :color="log.type === 'info' ? 'blue' : log.type === 'success' ? 'green' : 'orange'">
              {{ log.timestamp }}
            </a-tag>
            {{ log.message }}
          </div>
        </div>
      </div>

      <!-- 结果预览 -->
      <div v-if="aiResult" class="result-section">
        <a-divider>
          <span class="divider-text">
            <CheckCircleOutlined style="color: #52c41a" />
            AI生成结果预览
          </span>
        </a-divider>

        <div class="result-header">
          <!-- 实体信息展示 -->
          <div class="entity-info">
            <div class="entity-avatar" v-if="aiResult.thingsImg">
              <a-avatar
                :size="64"
                :src="aiResult.thingsImg"
                :alt="aiResult.things"
              />
            </div>
            <div class="entity-details">
              <h3 class="entity-name">{{ aiResult.things }}</h3>
              <p class="entity-stats">
                生成了 {{ editMode ? editableEvents.length : aiResult.output.length }} 个时间线事件
              </p>
            </div>
          </div>

          <a-alert
            :message="`AI已为「${aiResult.things}」生成完整的时间线数据`"
            type="success"
            show-icon
            style="margin: 16px 0"
          />

          <!-- 编辑模式切换 -->
          <div class="edit-mode-controls">
            <a-space>
              <a-button
                :type="editMode ? 'default' : 'primary'"
                @click="toggleEditMode"
                :icon="editMode ? EyeOutlined : EditOutlined"
              >
                {{ editMode ? '预览模式' : '编辑模式' }}
              </a-button>

              <a-button
                v-if="editMode"
                @click="resetToOriginal"
                :icon="UndoOutlined"
              >
                重置为原始内容
              </a-button>

              <a-button
                v-if="editMode"
                type="dashed"
                @click="addNewEvent"
                :icon="PlusOutlined"
              >
                添加事件
              </a-button>
            </a-space>
          </div>
        </div>

        <!-- 预览模式 -->
        <div v-if="!editMode" class="timeline-preview">
          <a-timeline>
            <a-timeline-item
              v-for="(event, index) in aiResult.output"
              :key="index"
              :color="index === 0 ? 'green' : 'blue'"
            >
              <template #dot>
                <ClockCircleOutlined style="font-size: 16px" />
              </template>
              <div class="timeline-event">
                <div class="event-time">{{ event.time }}</div>
                <div class="event-desc">{{ event.desc }}</div>
                <div class="event-image" v-if="event.image">
                  <a-image
                    :src="event.image"
                    :width="100"
                    :height="60"
                    :preview="true"
                    fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                  />
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>

        <!-- 编辑模式 -->
        <div v-else class="timeline-edit">
          <div
            v-for="(event, index) in editableEvents"
            :key="index"
            class="editable-event-card"
          >
            <a-card
              size="small"
              :title="`事件 ${index + 1}`"
              :class="{ 'expanded': expandedEvents.has(index) }"
            >
              <template #extra>
                <a-space>
                  <a-button
                    size="small"
                    type="text"
                    @click="toggleEventExpand(index)"
                    :icon="expandedEvents.has(index) ? ShrinkOutlined : ExpandAltOutlined"
                  >
                    {{ expandedEvents.has(index) ? '收起' : '展开' }}
                  </a-button>
                  <a-popconfirm
                    title="确定要删除这个事件吗？"
                    @confirm="removeEvent(index)"
                    ok-text="确定"
                    cancel-text="取消"
                  >
                    <a-button
                      size="small"
                      type="text"
                      danger
                      :icon="DeleteOutlined"
                    >
                      删除
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </template>

              <!-- 简化预览 -->
              <div v-if="!expandedEvents.has(index)" class="event-summary">
                <div class="summary-time">{{ event.time }}</div>
                <div class="summary-desc">{{ event.desc.substring(0, 50) }}{{ event.desc.length > 50 ? '...' : '' }}</div>
              </div>

              <!-- 详细编辑 -->
              <div v-else class="event-edit-form">
                <a-form layout="vertical">
                  <a-form-item label="事件时间">
                    <a-input
                      v-model:value="event.time"
                      placeholder="请输入事件时间，如：701年、公元前232年等"
                    />
                  </a-form-item>

                  <a-form-item label="事件描述">
                    <a-textarea
                      v-model:value="event.desc"
                      :rows="4"
                      placeholder="请输入详细的事件描述..."
                    />
                  </a-form-item>

                  <a-form-item label="事件图片URL">
                    <a-input
                      v-model:value="event.image"
                      placeholder="请输入图片URL地址"
                    />
                    <div v-if="event.image" class="image-preview">
                      <a-image
                        :src="event.image"
                        :width="120"
                        :height="80"
                        :preview="true"
                        fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                      />
                    </div>
                  </a-form-item>
                </a-form>
              </div>
            </a-card>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-space>
            <a-button @click="resetForm">
              <template #icon><ReloadOutlined /></template>
              重新生成
            </a-button>
            <a-button
              type="primary"
              :loading="creating"
              @click="createTimelines"
            >
              <template #icon><SaveOutlined /></template>
              {{ creating ? '创建中...' : '确认创建到数据库' }}
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 创建结果 -->
      <div v-if="createResult" class="create-result-section">
        <a-divider>
          <span class="divider-text">
            <CheckCircleOutlined style="color: #52c41a" />
            创建结果
          </span>
        </a-divider>

        <a-result
          :status="createResult.success ? 'success' : 'warning'"
          :title="createResult.message"
        >
          <template #subTitle>
            <div v-if="createResult.success">
              <p>实体ID: {{ createResult.entity_id }}</p>
              <p>成功创建: {{ createResult.successful_events }} 个事件</p>
              <p v-if="createResult.failed_events > 0">失败: {{ createResult.failed_events }} 个事件</p>
            </div>
          </template>
          <template #extra>
            <a-space>
              <a-button type="primary" @click="resetForm">继续创建</a-button>
              <a-button @click="$router.push('/admin/timelines')">查看时间线</a-button>
            </a-space>
          </template>
        </a-result>
      </div>
    </a-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  RobotOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ReloadOutlined,
  SaveOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  DeleteOutlined,
  UndoOutlined,
  ExpandAltOutlined,
  ShrinkOutlined
} from '@ant-design/icons-vue'
import { createAITimeline, generateTimelineStream } from '../../api/ai-timeline'
import request from '../../api/request'

export default {
  name: 'AdminAICreate',
  components: {
    RobotOutlined,
    LoadingOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    ReloadOutlined,
    SaveOutlined,
    EditOutlined,
    EyeOutlined,
    PlusOutlined,
    DeleteOutlined,
    UndoOutlined,
    ExpandAltOutlined,
    ShrinkOutlined
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const generating = ref(false)
    const creating = ref(false)
    const categoriesLoading = ref(false)
    const generationLogs = ref([])
    const aiResult = ref(null)
    const createResult = ref(null)
    const categories = ref([])

    // 编辑模式相关
    const editMode = ref(false)
    const editableEvents = ref([])
    const expandedEvents = ref(new Set())

    const form = reactive({
      entityName: '',
      entityType: 'person',
      categoryId: null
    })



    // 获取分类数据
    const fetchCategories = async () => {
      categoriesLoading.value = true
      try {
        const response = await request({
          url: '/categories/',
          method: 'GET',
          params: {
            page: 1,
            size: 100 // 获取所有分类
          }
        })

        categories.value = response.items || []

        // 如果有分类数据且当前没有选中分类，选择第一个
        if (categories.value.length > 0 && !form.categoryId) {
          form.categoryId = categories.value[0].id
        }

      } catch (error) {
        console.error('获取分类数据失败:', error)
        message.error('获取分类数据失败: ' + error.message)
      } finally {
        categoriesLoading.value = false
      }
    }

    // 添加日志
    const addLog = (message, type = 'info') => {
      generationLogs.value.push({
        timestamp: new Date().toLocaleTimeString(),
        message,
        type
      })
    }

    // 生成时间线
    const generateTimeline = async () => {
      if (!form.entityName.trim()) {
        message.error('请输入实体名称')
        return
      }

      loading.value = true
      generating.value = true
      generationLogs.value = []
      aiResult.value = null
      createResult.value = null

      try {
        addLog(`开始为 "${form.entityName}" 生成时间线...`, 'info')

        // 使用流式生成，传递分类ID以选择合适的工作流
        await generateTimelineStream(
          form.entityName,
          form.categoryId, // 传递分类ID
          // 进度回调
          (message) => {
            addLog(message, 'info')
          },
          // 完成回调
          (result) => {
            aiResult.value = result
            // 初始化可编辑事件数据
            editableEvents.value = JSON.parse(JSON.stringify(result.output))
            addLog('AI生成完成！', 'success')
            generating.value = false
            loading.value = false
          },
          // 错误回调
          (error) => {
            console.error('AI生成失败:', error)
            message.error('AI生成失败: ' + error.message)
            addLog('AI生成失败: ' + error.message, 'error')
            generating.value = false
            loading.value = false
          }
        )

      } catch (error) {
        console.error('AI生成失败:', error)
        message.error('AI生成失败: ' + error.message)
        addLog('AI生成失败: ' + error.message, 'error')
        generating.value = false
        loading.value = false
      }
    }

    // 编辑模式相关方法
    const toggleEditMode = () => {
      editMode.value = !editMode.value
      if (editMode.value) {
        // 进入编辑模式时，确保有可编辑数据
        if (editableEvents.value.length === 0 && aiResult.value) {
          editableEvents.value = JSON.parse(JSON.stringify(aiResult.value.output))
        }
      }
    }

    const toggleEventExpand = (index) => {
      if (expandedEvents.value.has(index)) {
        expandedEvents.value.delete(index)
      } else {
        expandedEvents.value.add(index)
      }
    }

    const resetToOriginal = () => {
      if (aiResult.value) {
        editableEvents.value = JSON.parse(JSON.stringify(aiResult.value.output))
        message.success('已重置为原始AI生成内容')
      }
    }

    const addNewEvent = () => {
      editableEvents.value.push({
        desc: '请输入事件描述...',
        image: '',
        time: '请输入时间...'
      })
    }

    const removeEvent = (index) => {
      editableEvents.value.splice(index, 1)
      expandedEvents.value.delete(index)
      message.success('事件已删除')
    }

    // 创建时间线到数据库
    const createTimelines = async () => {
      if (!aiResult.value) return

      creating.value = true
      try {
        // 使用编辑后的数据或原始数据
        const finalData = {
          output: editMode.value ? editableEvents.value : aiResult.value.output,
          things: aiResult.value.things,
          thingsImg: aiResult.value.thingsImg  // 包含头像URL
        }

        const requestData = {
          content: finalData,
          category_id: form.categoryId,
          entity_type: form.entityType
        }

        const result = await createAITimeline(requestData)
        createResult.value = result

        if (result.success) {
          message.success('时间线创建成功！')
        } else {
          message.warning('时间线创建部分成功，请查看详细结果')
        }
      } catch (error) {
        console.error('创建时间线失败:', error)
        message.error('创建时间线失败: ' + error.message)
      } finally {
        creating.value = false
      }
    }

    // 重置表单
    const resetForm = () => {
      form.entityName = ''
      form.entityType = 'person'
      form.categoryId = categories.value.length > 0 ? categories.value[0].id : null
      aiResult.value = null
      createResult.value = null
      generationLogs.value = []
      generating.value = false
      creating.value = false
      loading.value = false
      // 重置编辑模式
      editMode.value = false
      editableEvents.value = []
      expandedEvents.value.clear()
    }

    // 组件挂载时获取分类数据
    onMounted(() => {
      fetchCategories()
    })

    return {
      form,
      loading,
      generating,
      creating,
      categoriesLoading,
      categories,
      generationLogs,
      aiResult,
      createResult,
      // 编辑模式相关
      editMode,
      editableEvents,
      expandedEvents,
      // 方法
      generateTimeline,
      createTimelines,
      resetForm,
      toggleEditMode,
      toggleEventExpand,
      resetToOriginal,
      addNewEvent,
      removeEvent
    }
  }
}
</script>

<style scoped>
.ai-create-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.main-card {
  max-width: 1200px;
  margin: 0 auto;
}

.input-section {
  margin-bottom: 24px;
}

.generation-section {
  margin: 24px 0;
}

.generation-log {
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  margin-bottom: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
}

.log-item:last-child {
  margin-bottom: 0;
}

.result-section {
  margin: 24px 0;
}

.result-header {
  margin-bottom: 24px;
}

.entity-info {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  margin-bottom: 16px;
  color: white;
}

.entity-avatar {
  margin-right: 16px;
}

.entity-avatar .ant-avatar {
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.entity-details {
  flex: 1;
}

.entity-name {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.entity-stats {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.edit-mode-controls {
  margin-top: 16px;
  text-align: center;
}

.timeline-preview {
  background: #fafafa;
  border-radius: 8px;
  padding: 24px;
  margin: 16px 0;
}

.timeline-event {
  padding: 8px 0;
}

.event-time {
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
}

.event-desc {
  color: #262626;
  line-height: 1.6;
  margin-bottom: 8px;
}

.event-image {
  margin-top: 8px;
}

.action-buttons {
  text-align: center;
  margin-top: 24px;
}

/* 编辑模式样式 */
.timeline-edit {
  background: #fafafa;
  border-radius: 8px;
  padding: 24px;
  margin: 16px 0;
}

.editable-event-card {
  margin-bottom: 16px;
}

.editable-event-card:last-child {
  margin-bottom: 0;
}

.editable-event-card .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.editable-event-card .ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.editable-event-card.expanded .ant-card {
  border-color: #1890ff;
}

.event-summary {
  padding: 8px 0;
}

.summary-time {
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-desc {
  color: #666;
  line-height: 1.5;
  font-size: 13px;
}

.event-edit-form {
  margin-top: 16px;
}

.image-preview {
  margin-top: 8px;
  text-align: center;
}

.create-result-section {
  margin: 24px 0;
}

.divider-text {
  font-weight: 600;
  color: #262626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-create-container {
    padding: 16px;
  }

  .timeline-preview {
    padding: 16px;
  }
}
</style>
