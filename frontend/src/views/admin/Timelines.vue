<template>
  <div class="timelines-page">
    <div class="page-header">
      <h1>时间线管理</h1>
      <a-button
        type="primary"
        @click="showAddModal"
        :disabled="!selectedEntity"
      >
        <template #icon><PlusOutlined /></template>
        为 {{ selectedEntity?.name || '选中实体' }} 新增时间线
      </a-button>
    </div>

    <!-- 左右分栏布局 -->
    <div class="main-content">
      <!-- 左侧实体列表 -->
      <div class="left-panel">
        <a-card title="实体列表" size="small" class="entity-panel">
          <template #extra>
            <a-input
              v-model:value="entitySearch"
              placeholder="搜索实体"
              size="small"
              style="width: 150px"
              @input="handleEntitySearch"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
          </template>

          <div class="entity-list">
            <div
              v-for="entity in filteredEntities"
              :key="entity.id"
              :class="['entity-item', { active: selectedEntity?.id === entity.id }]"
              @click="selectEntity(entity)"
            >
              <div class="entity-info">
                <a-tag :color="getTypeColor(entity.type)" size="small">
                  <component :is="getTypeIcon(entity.type)" style="margin-right: 4px" />
                  {{ getTypeLabel(entity.type) === 'person' ? '人物' : getTypeLabel(entity.type) === 'event' ? '事件' : '企业' }}
                </a-tag>
                <span class="entity-name">{{ entity.name }}</span>
              </div>
              <div class="entity-stats">
                <a-badge
                  :count="getEntityTimelineCount(entity.id)"
                  :number-style="{ backgroundColor: '#52c41a' }"
                />
              </div>
            </div>
          </div>

          <div v-if="filteredEntities.length === 0" class="empty-state">
            <a-empty description="暂无实体数据" />
          </div>
        </a-card>
      </div>

      <!-- 右侧时间线管理 -->
      <div class="right-panel">
        <a-card
          :title="selectedEntity ? `${selectedEntity.name} 的时间线` : '请选择实体'"
          size="small"
          class="timeline-panel"
        >
          <template #extra v-if="selectedEntity">
            <a-space>
              <a-button size="small" @click="handleRefresh">
                <template #icon><ReloadOutlined /></template>
                刷新
              </a-button>
              <a-button type="primary" size="small" @click="showAddModal">
                <template #icon><PlusOutlined /></template>
                新增时间线
              </a-button>
            </a-space>
          </template>

          <!-- 时间线搜索 -->
          <div v-if="selectedEntity" class="timeline-search">
            <a-row :gutter="16" align="middle">
              <a-col :span="12">
                <a-input
                  v-model:value="timelineSearch"
                  placeholder="搜索时间线标题"
                  allow-clear
                  @pressEnter="handleTimelineSearch"
                >
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :span="8">
                <a-range-picker
                  v-model:value="dateRange"
                  size="small"
                  @change="handleDateRangeChange"
                />
              </a-col>
              <a-col :span="4">
                <a-button @click="handleTimelineReset">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
              </a-col>
            </a-row>
          </div>

          <!-- 时间线列表 -->
          <div v-if="selectedEntity" class="timeline-content">
            <a-table
              :columns="timelineColumns"
              :data-source="timelinesStore.entityTimelines"
              :loading="timelinesStore.loading"
              :pagination="paginationConfig"
              row-key="id"
              size="small"
              @change="handleTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'title'">
                  <div class="timeline-title">
                    <a-tooltip :title="record.content || '暂无详细内容'">
                      <span class="title-text">{{ record.title }}</span>
                    </a-tooltip>
                  </div>
                </template>
                <template v-else-if="column.key === 'event_date'">
                  <div class="event-date">
                    <CalendarOutlined style="margin-right: 4px; color: #1890ff" />
                    {{ formatDate(record.event_date) }}
                  </div>
                </template>
                <template v-else-if="column.key === 'media'">
                  <div class="media-container">
                    <div v-if="record.image_url" class="media-item">
                      <a-image
                        :src="record.image_url"
                        :width="32"
                        :height="24"
                        style="border-radius: 3px; object-fit: cover"
                        :preview="{ src: record.image_url }"
                      />
                    </div>
                    <div v-if="record.video_url" class="media-item">
                      <VideoCameraOutlined style="color: #fa8c16; font-size: 16px" />
                    </div>
                    <div v-if="!record.image_url && !record.video_url" class="media-empty">
                      -
                    </div>
                  </div>
                </template>
                <template v-else-if="column.key === 'sort_order'">
                  <a-tag color="purple">{{ record.sort_order || 0 }}</a-tag>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="showEditModal(record)">
                      <template #icon><EditOutlined /></template>
                      编辑
                    </a-button>
                    <a-popconfirm
                      title="确定要删除这个时间线节点吗？"
                      ok-text="确定"
                      cancel-text="取消"
                      @confirm="handleDelete(record.id)"
                    >
                      <a-button type="link" size="small" danger>
                        <template #icon><DeleteOutlined /></template>
                        删除
                      </a-button>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>

          <!-- 未选择实体的提示 -->
          <div v-else class="empty-state">
            <a-empty description="请从左侧选择一个实体来管理其时间线" />
          </div>
        </a-card>
      </div>
    </div>


    <!-- 添加/编辑时间线模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑时间线' : '新增时间线'"
      :confirm-loading="submitLoading"
      width="900px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="16">
            <a-form-item label="时间线标题" name="title">
              <a-input
                v-model:value="formData.title"
                placeholder="请输入时间线标题"
                :maxlength="200"
                show-count
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="所属实体">
              <a-input
                :value="selectedEntity?.name"
                disabled
              >
                <template #prefix>
                  <component :is="getTypeIcon(selectedEntity?.type)" style="color: #1890ff" />
                </template>
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="详细内容" name="content">
          <a-textarea
            v-model:value="formData.content"
            placeholder="请输入详细内容"
            :rows="4"
            :maxlength="2000"
            show-count
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="事件时间" name="event_date">
              <a-date-picker
                v-model:value="formData.event_date"
                placeholder="请选择事件时间"
                style="width: 100%"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排序权重" name="sort_order">
              <a-input-number
                v-model:value="formData.sort_order"
                placeholder="请输入排序权重"
                :min="0"
                :max="9999"
                style="width: 100%"
              />
              <div style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
                数值越大越靠前显示
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="时间线图片" name="image_url">
              <MediaSelector
                v-model:value="formData.image_url"
                placeholder="选择时间线图片"
                media-type="image"
                :preview-size="{ width: 120, height: 80 }"
                default-category="timeline_image"
              />
              <div style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
                图片是必填项，用于时间线展示
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="时间线视频" name="video_url">
              <MediaSelector
                v-model:value="formData.video_url"
                placeholder="选择时间线视频（可选）"
                media-type="video"
                :preview-size="{ width: 120, height: 80 }"
                default-category="timeline_video"
              />
              <div style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
                视频是可选项，可以丰富时间线内容
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  CalendarOutlined,
  VideoCameraOutlined,
  UserOutlined,
  TeamOutlined,
  BankOutlined
} from '@ant-design/icons-vue'
import { useTimelinesStore } from '../../stores/timelines'
import { useEntitiesStore } from '../../stores/entities'
import MediaSelector from '../../components/MediaSelector.vue'

export default {
  name: 'AdminTimelines',
  components: {
    PlusOutlined,
    SearchOutlined,
    ReloadOutlined,
    EditOutlined,
    DeleteOutlined,
    CalendarOutlined,
    VideoCameraOutlined,
    UserOutlined,
    TeamOutlined,
    BankOutlined,
    MediaSelector
  },
  setup() {
    const timelinesStore = useTimelinesStore()
    const entitiesStore = useEntitiesStore()
    const formRef = ref()

    // 响应式数据
    const modalVisible = ref(false)
    const isEdit = ref(false)
    const submitLoading = ref(false)
    const currentEditId = ref(null)
    const dateRange = ref([])
    const selectedEntity = ref(null)
    const entitySearch = ref('')
    const timelineSearch = ref('')

    // 搜索表单
    const searchForm = reactive({
      search: '',
      entity_id: null,
      start_date: null,
      end_date: null,
      page: 1,
      size: 10
    })

    // 表单数据
    const formData = reactive({
      title: '',
      entity_id: null,
      content: '',
      event_date: null,
      image_url: '',
      video_url: '',
      sort_order: 0
    })

    // 表单验证规则
    const formRules = {
      title: [
        { required: true, message: '请输入时间线标题', trigger: 'blur' },
        { min: 2, max: 200, message: '标题长度应为2-200个字符', trigger: 'blur' }
      ],
      event_date: [
        { required: true, message: '请选择事件时间', trigger: 'change' }
      ],
      image_url: [
        { required: true, message: '请选择时间线图片', trigger: 'blur' }
      ],
      content: [
        { max: 2000, message: '内容长度不能超过2000个字符', trigger: 'blur' }
      ],
      sort_order: [
        { type: 'number', min: 0, max: 9999, message: '排序权重应为0-9999的数字', trigger: 'blur' }
      ]
    }

    // 时间线表格列配置（右侧面板用）
    const timelineColumns = [
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        width: 200,
        ellipsis: true
      },
      {
        title: '事件时间',
        dataIndex: 'event_date',
        key: 'event_date',
        width: 160,
        sorter: true
      },
      {
        title: '媒体',
        key: 'media',
        width: 100
      },
      {
        title: '排序',
        dataIndex: 'sort_order',
        key: 'sort_order',
        width: 80,
        sorter: true
      },
      {
        title: '操作',
        key: 'action',
        width: 140,
        fixed: 'right'
      }
    ]

    // 分页配置
    const paginationConfig = computed(() => ({
      current: timelinesStore.pagination.page,
      pageSize: timelinesStore.pagination.size,
      total: timelinesStore.pagination.total,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
      size: 'small'
    }))

    // 过滤后的实体列表
    const filteredEntities = computed(() => {
      if (!entitySearch.value) {
        return entitiesStore.entities
      }
      return entitiesStore.entities.filter(entity =>
        entity.name.toLowerCase().includes(entitySearch.value.toLowerCase())
      )
    })

    // 获取类型颜色
    const getTypeColor = (type) => {
      const colors = {
        person: '#52c41a',
        event: '#fa8c16',
        enterprise: '#1890ff'
      }
      return colors[type] || '#666'
    }

    // 获取类型标签
    const getTypeLabel = (type) => {
      const labels = {
        person: 'person',
        event: 'event',
        enterprise: 'enterprise'
      }
      return labels[type] || type
    }

    // 获取类型图标组件
    const getTypeIcon = (type) => {
      const icons = {
        person: UserOutlined,
        event: CalendarOutlined,
        enterprise: BankOutlined
      }
      return icons[type] || UserOutlined
    }

    // 获取实体名称
    const getEntityName = (entityId) => {
      const entity = entitiesStore.entities.find(e => e.id === entityId)
      return entity ? entity.name : '未知实体'
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return dayjs(dateString).format('YYYY-MM-DD HH:mm')
    }

    // 获取实体的时间线数量
    const getEntityTimelineCount = (entityId) => {
      // 这里可以从store中获取，暂时返回0
      return 0
    }

    // 选择实体
    const selectEntity = (entity) => {
      selectedEntity.value = entity
      // 清空之前的时间线数据
      timelinesStore.clearEntityTimelines()
      // 加载该实体的时间线
      loadEntityTimelines(entity.id)
    }

    // 实体搜索处理
    const handleEntitySearch = () => {
      // 实时搜索，filteredEntities计算属性会自动更新
    }

    // 加载指定实体的时间线
    const loadEntityTimelines = async (entityId) => {
      try {
        await timelinesStore.fetchTimelinesByEntity(entityId, {
          search: timelineSearch.value,
          start_date: searchForm.start_date,
          end_date: searchForm.end_date
        })
      } catch (error) {
        console.error('加载实体时间线失败:', error)
        message.error('加载实体时间线失败')
      }
    }

    // 时间线搜索
    const handleTimelineSearch = () => {
      if (selectedEntity.value) {
        loadEntityTimelines(selectedEntity.value.id)
      }
    }

    // 时间线重置
    const handleTimelineReset = () => {
      timelineSearch.value = ''
      dateRange.value = []
      searchForm.start_date = null
      searchForm.end_date = null
      if (selectedEntity.value) {
        loadEntityTimelines(selectedEntity.value.id)
      }
    }

    // 刷新当前实体的时间线
    const handleRefresh = () => {
      if (selectedEntity.value) {
        loadEntityTimelines(selectedEntity.value.id)
      }
    }

    // 检查是否有活跃的筛选条件
    const hasActiveFilters = computed(() => {
      return !!(searchForm.search || searchForm.entity_id || searchForm.start_date)
    })

    // 格式化日期范围显示
    const formatDateRange = () => {
      if (!searchForm.start_date) return ''
      const start = dayjs(searchForm.start_date).format('YYYY-MM-DD')
      const end = searchForm.end_date ? dayjs(searchForm.end_date).format('YYYY-MM-DD') : start
      return start === end ? start : `${start} ~ ${end}`
    }

    // 实体选项过滤
    const filterEntityOption = (input, option) => {
      return option.children[1].toLowerCase().indexOf(input.toLowerCase()) >= 0
    }

    // 清除单个筛选条件
    const clearSearchFilter = (filterKey) => {
      searchForm[filterKey] = filterKey === 'search' ? '' : null
      handleSearch()
    }

    // 清除日期筛选
    const clearDateFilter = () => {
      searchForm.start_date = null
      searchForm.end_date = null
      dateRange.value = []
      handleSearch()
    }

    // 日期范围变化处理
    const handleDateRangeChange = (dates) => {
      if (dates && dates.length === 2) {
        searchForm.start_date = dates[0].format('YYYY-MM-DD')
        searchForm.end_date = dates[1].format('YYYY-MM-DD')
      } else {
        searchForm.start_date = null
        searchForm.end_date = null
      }
      handleSearch()
    }

    // 加载时间线列表
    const loadTimelines = async () => {
      try {
        await timelinesStore.fetchTimelines(searchForm)
      } catch (error) {
        console.error('加载时间线列表失败:', error)
        message.error('加载时间线列表失败')
      }
    }

    // 加载实体列表
    const loadEntities = async () => {
      try {
        await entitiesStore.fetchEntities({ page: 1, size: 100 })
      } catch (error) {
        console.error('加载实体列表失败:', error)
      }
    }

    // 搜索
    const handleSearch = () => {
      searchForm.page = 1
      timelinesStore.setFilters({
        search: searchForm.search,
        entity_id: searchForm.entity_id,
        start_date: searchForm.start_date,
        end_date: searchForm.end_date
      })
      loadTimelines()
    }

    // 重置搜索
    const handleReset = () => {
      searchForm.search = ''
      searchForm.entity_id = null
      searchForm.start_date = null
      searchForm.end_date = null
      searchForm.page = 1
      dateRange.value = []
      timelinesStore.setFilters({
        search: '',
        entity_id: null,
        start_date: null,
        end_date: null
      })
      loadTimelines()
    }

    // 表格变化处理
    const handleTableChange = (pagination, filters, sorter) => {
      searchForm.page = pagination.current
      searchForm.size = pagination.pageSize
      loadTimelines()
    }

    // 显示添加模态框
    const showAddModal = () => {
      if (!selectedEntity.value) {
        message.warning('请先选择一个实体')
        return
      }
      isEdit.value = false
      currentEditId.value = null
      resetForm()
      modalVisible.value = true
    }

    // 显示编辑模态框
    const showEditModal = (record) => {
      isEdit.value = true
      currentEditId.value = record.id
      formData.title = record.title
      formData.content = record.content || ''
      formData.event_date = record.event_date ? dayjs(record.event_date) : null
      formData.image_url = record.image_url || ''
      formData.video_url = record.video_url || ''
      formData.sort_order = record.sort_order || 0
      modalVisible.value = true
    }

    // 重置表单
    const resetForm = () => {
      formData.title = ''
      formData.content = ''
      formData.event_date = null
      formData.image_url = ''
      formData.video_url = ''
      formData.sort_order = 0
      if (formRef.value) {
        formRef.value.resetFields()
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        submitLoading.value = true

        // 处理日期格式
        const submitData = {
          ...formData,
          entity_id: selectedEntity.value.id, // 使用选中的实体ID
          event_date: formData.event_date ? formData.event_date.format('YYYY-MM-DD HH:mm:ss') : null
        }

        if (isEdit.value) {
          // 编辑时间线
          await timelinesStore.updateTimeline(currentEditId.value, submitData)
          message.success('时间线更新成功')
        } else {
          // 添加时间线
          await timelinesStore.createTimeline(submitData)
          message.success('时间线创建成功')
        }

        modalVisible.value = false
        // 刷新当前实体的时间线
        loadEntityTimelines(selectedEntity.value.id)
      } catch (error) {
        console.error('提交失败:', error)
        if (error.errors) {
          // 表单验证错误
          return
        }
        message.error(isEdit.value ? '时间线更新失败' : '时间线创建失败')
      } finally {
        submitLoading.value = false
      }
    }

    // 取消操作
    const handleCancel = () => {
      modalVisible.value = false
      resetForm()
    }

    // 删除时间线
    const handleDelete = async (id) => {
      try {
        await timelinesStore.deleteTimeline(id)
        message.success('时间线删除成功')
        // 刷新当前实体的时间线
        if (selectedEntity.value) {
          loadEntityTimelines(selectedEntity.value.id)
        }
      } catch (error) {
        console.error('删除失败:', error)
        message.error('时间线删除失败')
      }
    }

    // 组件挂载时加载数据
    onMounted(async () => {
      await loadEntities()
      // 不再自动加载所有时间线，等用户选择实体后再加载
    })

    return {
      timelinesStore,
      entitiesStore,
      formRef,
      modalVisible,
      isEdit,
      submitLoading,
      dateRange,
      selectedEntity,
      entitySearch,
      timelineSearch,
      searchForm,
      formData,
      formRules,
      timelineColumns,
      paginationConfig,
      filteredEntities,
      getTypeColor,
      getTypeLabel,
      getTypeIcon,
      getEntityName,
      formatDate,
      getEntityTimelineCount,
      selectEntity,
      handleEntitySearch,
      handleTimelineSearch,
      handleTimelineReset,
      handleRefresh,
      handleDateRangeChange,
      handleTableChange,
      showAddModal,
      showEditModal,
      handleSubmit,
      handleCancel,
      handleDelete
    }
  }
}
</script>

<style scoped>
.timelines-page {
  padding: 24px;
  height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.page-header h1 {
  margin: 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  gap: 16px;
  flex: 1;
  min-height: 0;
}

/* 左侧实体面板 */
.left-panel {
  width: 300px;
  flex-shrink: 0;
}

.entity-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.entity-panel :deep(.ant-card-body) {
  flex: 1;
  padding: 12px;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.entity-list {
  flex: 1;
  overflow-y: auto;
  margin-top: 8px;
}

.entity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.entity-item:hover {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
}

.entity-item.active {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.entity-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.entity-name {
  font-size: 13px;
  color: #262626;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.entity-stats {
  flex-shrink: 0;
}

/* 右侧时间线面板 */
.right-panel {
  flex: 1;
  min-width: 0;
}

.timeline-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.timeline-panel :deep(.ant-card-body) {
  flex: 1;
  padding: 12px;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.timeline-search {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;
}

.timeline-content {
  flex: 1;
  min-height: 0;
}

.timeline-content :deep(.ant-table-wrapper) {
  height: 100%;
}

.timeline-content :deep(.ant-table) {
  height: 100%;
}

.timeline-content :deep(.ant-table-container) {
  height: calc(100% - 55px);
}

.timeline-content :deep(.ant-table-body) {
  height: 100%;
  overflow-y: auto;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 表格内容样式 */
.timeline-title {
  max-width: 180px;
}

.title-text {
  font-weight: 500;
  color: #262626;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.title-text:hover {
  color: #1890ff;
}

.event-date {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #666;
}

.media-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.media-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-empty {
  color: #bfbfbf;
  font-size: 14px;
}

/* 表格样式优化 */
:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

/* 模态框样式 */
:deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .timelines-page {
    padding: 16px;
    height: calc(100vh - 64px);
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    margin-bottom: 12px;
  }

  .page-header h1 {
    text-align: center;
    font-size: 20px;
  }

  .main-content {
    flex-direction: column;
    gap: 12px;
  }

  .left-panel {
    width: 100%;
    height: 200px;
  }

  .right-panel {
    flex: 1;
  }

  .timeline-search {
    padding: 8px;
  }

  .timeline-search .ant-row {
    flex-direction: column;
    gap: 8px;
  }

  .timeline-search .ant-col {
    width: 100% !important;
  }

  .entity-item {
    padding: 6px 8px;
  }

  .entity-name {
    font-size: 12px;
  }

  .timeline-title {
    max-width: 120px;
  }
}

@media (max-width: 576px) {
  .timelines-page {
    padding: 12px;
  }

  .page-header h1 {
    font-size: 18px;
  }

  .page-header button {
    font-size: 12px;
    padding: 4px 12px;
  }

  .left-panel {
    height: 150px;
  }

  .entity-panel :deep(.ant-card-head) {
    padding: 8px 12px;
    min-height: auto;
  }

  .entity-panel :deep(.ant-card-head-title) {
    font-size: 14px;
  }

  .entity-panel :deep(.ant-card-extra) {
    font-size: 12px;
  }

  .timeline-panel :deep(.ant-card-head) {
    padding: 8px 12px;
    min-height: auto;
  }

  .timeline-panel :deep(.ant-card-head-title) {
    font-size: 14px;
  }
}
</style>
