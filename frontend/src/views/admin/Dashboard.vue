<template>
  <div class="dashboard-content">
    <div class="dashboard-header">
      <h2>数据仪表盘</h2>
      <div class="header-actions">
        <a-button type="primary" @click="refreshDashboard" :loading="loading">
          <template #icon><ReloadOutlined /></template>
          刷新数据
        </a-button>
      </div>
      <div class="last-update">
        最后更新: {{ lastUpdateTime }}
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <a-row :gutter="[16, 16]" class="stats-cards">
      <a-col :xs="24" :sm="12" :lg="6">
        <StatCard
          title="总实体数"
          :value="dashboardStats.overview?.total_entities || 0"
          :icon="UserOutlined"
          color="#1890ff"
          :loading="loading"
        />
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <StatCard
          title="总时间线数"
          :value="dashboardStats.overview?.total_timelines || 0"
          :icon="ClockCircleOutlined"
          color="#52c41a"
          :loading="loading"
        />
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <StatCard
          title="总分类数"
          :value="dashboardStats.overview?.total_categories || 0"
          :icon="AppstoreOutlined"
          color="#faad14"
          :loading="loading"
        />
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <StatCard
          title="媒体文件数"
          :value="dashboardStats.overview?.total_media_files || 0"
          :icon="FileImageOutlined"
          color="#722ed1"
          :loading="loading"
        />
      </a-col>
    </a-row>

    <!-- 用户行为统计卡片 -->
    <a-row :gutter="[16, 16]" class="stats-cards">
      <a-col :xs="24" :sm="12" :lg="6">
        <StatCard
          title="总浏览量"
          :value="dashboardStats.user_actions?.total_views || 0"
          :icon="EyeOutlined"
          color="#13c2c2"
          :loading="loading"
        />
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <StatCard
          title="总点赞数"
          :value="dashboardStats.user_actions?.total_likes || 0"
          :icon="HeartOutlined"
          color="#eb2f96"
          :loading="loading"
        />
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <StatCard
          title="总送花数"
          :value="dashboardStats.user_actions?.total_flowers || 0"
          :icon="GiftOutlined"
          color="#fa8c16"
          :loading="loading"
        />
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <StatCard
          title="独立访客数"
          :value="dashboardStats.user_actions?.unique_visitors || 0"
          :icon="TeamOutlined"
          color="#f5222d"
          :loading="loading"
        />
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import {
  UserOutlined,
  ClockCircleOutlined,
  AppstoreOutlined,
  FileImageOutlined,
  EyeOutlined,
  HeartOutlined,
  GiftOutlined,
  TeamOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import StatCard from '../../components/charts/StatCard.vue'
import { userActionsAPI } from '../../api'

export default {
  name: 'Dashboard',
  components: {
    StatCard
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const lastUpdateTime = ref('')

    // 仪表盘统计数据
    const dashboardStats = ref({
      overview: {},
      user_actions: {},
      entity_types: {},
      growth_trends: [],
      popular_entities: [],
      active_categories: []
    })

    // 方法
    const loadDashboardStats = async () => {
      try {
        loading.value = true
        const response = await userActionsAPI.getDashboardStats()

        // 后端直接返回数据对象，不包装在success字段中
        dashboardStats.value = response
        lastUpdateTime.value = new Date().toLocaleString()
      } catch (error) {
        console.error('获取仪表盘数据失败:', error)
        message.error('获取仪表盘数据失败')
      } finally {
        loading.value = false
      }
    }

    const refreshDashboard = () => {
      loadDashboardStats()
    }

    // 生命周期
    onMounted(() => {
      loadDashboardStats()
    })

    return {
      loading,
      lastUpdateTime,
      dashboardStats,
      refreshDashboard,
      // 图标
      UserOutlined,
      ClockCircleOutlined,
      AppstoreOutlined,
      FileImageOutlined,
      EyeOutlined,
      HeartOutlined,
      GiftOutlined,
      TeamOutlined,
      ReloadOutlined
    }
  }
}
</script>

<style scoped>
.dashboard-content {
  padding: 0;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.dashboard-header h2 {
  margin: 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.last-update {
  color: #8c8c8c;
  font-size: 14px;
  position: absolute;
  right: 0;
  top: 40px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stats-cards:last-child {
  margin-bottom: 0;
}

/* ========== 响应式设计 ========== */
/* 平板设备 */
@media (max-width: 1024px) {
  .dashboard-page {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

/* 移动设备 */
@media (max-width: 768px) {
  .dashboard-page {
    padding: var(--mobile-padding);
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--mobile-margin);
    padding: var(--mobile-padding);
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .last-update {
    position: static;
    margin-top: 8px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--mobile-margin);
  }

  .stats-cards {
    margin-bottom: var(--mobile-margin);
  }

  .stats-cards :deep(.ant-card-body) {
    padding: var(--mobile-card-padding);
  }

  .stats-cards :deep(.ant-statistic-title) {
    font-size: 13px;
  }

  .stats-cards :deep(.ant-statistic-content) {
    font-size: 20px;
  }
}

/* 超小屏幕 */
@media (max-width: 480px) {
  .dashboard-page {
    padding: 12px;
  }

  .page-header {
    padding: 12px;
  }

  .stats-cards :deep(.ant-card-body) {
    padding: 12px;
  }

  .stats-cards :deep(.ant-statistic-content) {
    font-size: 18px;
  }
}
</style>