<template>
  <div class="admin-login-page">
    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <h1 class="login-title">数字生命馆</h1>
          <p class="login-subtitle">管理员登录</p>
        </div>
        
        <a-form
          :model="loginForm"
          :rules="loginRules"
          @finish="handleLogin"
          @finishFailed="handleLoginFailed"
          layout="vertical"
          class="login-form"
        >
          <a-form-item name="username" label="用户名">
            <a-input
              v-model:value="loginForm.username"
              size="large"
              placeholder="请输入管理员用户名"
              :prefix="h(UserOutlined)"
            />
          </a-form-item>
          
          <a-form-item name="password" label="密码">
            <a-input-password
              v-model:value="loginForm.password"
              size="large"
              placeholder="请输入密码"
              :prefix="h(LockOutlined)"
            />
          </a-form-item>
          
          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              :loading="loginLoading"
              block
            >
              登录
            </a-button>
          </a-form-item>
        </a-form>
        
        <div class="login-footer">
          <a-button type="link" @click="$router.push('/')">
            <template #icon><HomeOutlined /></template>
            返回首页
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, ref, h } from 'vue'
import { useRouter } from 'vue-router'
import { UserOutlined, LockOutlined, HomeOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useAuthStore } from '../../stores/auth'

export default {
  name: 'AdminLogin',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    
    // 响应式数据
    const loginLoading = ref(false)
    const loginForm = reactive({
      username: '',
      password: ''
    })
    
    // 表单验证规则
    const loginRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度应为3-20个字符', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度至少6个字符', trigger: 'blur' }
      ]
    }
    
    // 登录处理
    const handleLogin = async (values) => {
      loginLoading.value = true
      try {
        console.log('开始登录:', values)
        await authStore.login(values)
        console.log('登录成功，认证状态:', authStore.isAuthenticated)
        console.log('用户信息:', authStore.user)
        message.success('登录成功')
        // 跳转到管理后台首页
        console.log('准备跳转到 /admin')
        await router.push('/admin')
        console.log('跳转完成')
      } catch (error) {
        console.error('登录失败:', error)
        message.error('登录失败，请检查用户名和密码')
      } finally {
        loginLoading.value = false
      }
    }
    
    // 登录失败处理
    const handleLoginFailed = (errorInfo) => {
      console.log('表单验证失败:', errorInfo)
    }
    
    return {
      h,
      loginForm,
      loginRules,
      loginLoading,
      handleLogin,
      handleLoginFailed,
      UserOutlined,
      LockOutlined,
      HomeOutlined
    }
  }
}
</script>

<style scoped>
.admin-login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.login-subtitle {
  font-size: 16px;
  color: #8c8c8c;
  margin: 0;
}

.login-form {
  margin-bottom: 24px;
}

.login-form .ant-form-item {
  margin-bottom: 24px;
}

.login-form .ant-form-item:last-child {
  margin-bottom: 0;
}

.login-footer {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .admin-login-page {
    padding: 12px;
  }
  
  .login-card {
    padding: 24px;
  }
  
  .login-title {
    font-size: 24px;
  }
}
</style>
