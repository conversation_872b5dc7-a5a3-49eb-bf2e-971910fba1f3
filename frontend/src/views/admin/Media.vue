<template>
  <div class="admin-media">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>媒体管理</h2>
        <div class="stats-info">
          <a-statistic
            title="总文件数"
            :value="mediaStats.total_files || 0"
            :value-style="{ fontSize: '16px' }"
          />
          <a-divider type="vertical" />
          <a-statistic
            title="总大小"
            :value="mediaStats.total_size_mb || 0"
            suffix="MB"
            :value-style="{ fontSize: '16px' }"
          />
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData" :loading="loading">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
        <a-button type="primary" @click="showUploadModal">
          <template #icon><UploadOutlined /></template>
          上传文件
        </a-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <a-card class="search-card" :bordered="false">
      <div class="search-form">
        <a-row :gutter="[16, 16]" align="middle">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="search-item">
              <label class="search-label">文件名搜索</label>
              <a-input
                v-model:value="searchForm.search"
                placeholder="请输入文件名"
                allow-clear
                @pressEnter="handleSearch"
              >
                <template #prefix>
                  <SearchOutlined style="color: #bfbfbf" />
                </template>
              </a-input>
            </div>
          </a-col>

          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="search-item">
              <label class="search-label">媒体类型</label>
              <a-select
                v-model:value="searchForm.media_type"
                placeholder="全部类型"
                allow-clear
                style="width: 100%"
                @change="handleSearch"
              >
                <a-select-option value="image">图片</a-select-option>
                <a-select-option value="video">视频</a-select-option>
                <a-select-option value="document">文档</a-select-option>
                <a-select-option value="other">其他</a-select-option>
              </a-select>
            </div>
          </a-col>

          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="search-item">
              <label class="search-label">分类筛选</label>
              <a-select
                v-model:value="searchForm.category"
                placeholder="全部分类"
                allow-clear
                style="width: 100%"
                @change="handleSearch"
              >
                <a-select-option value="avatar">头像</a-select-option>
                <a-select-option value="timeline_image">时间线图片</a-select-option>
                <a-select-option value="timeline_video">时间线视频</a-select-option>
                <a-select-option value="general">通用文件</a-select-option>
              </a-select>
            </div>
          </a-col>

          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="search-item">
              <a-space>
                <a-button type="primary" @click="handleSearch" :loading="loading">
                  <template #icon><SearchOutlined /></template>
                  搜索
                </a-button>
                <a-button @click="resetSearch">
                  重置
                </a-button>
              </a-space>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-card>

    <!-- 批量操作栏 -->
    <a-card v-if="selectedRowKeys.length > 0" class="batch-actions" :bordered="false">
      <div class="batch-info">
        <span>已选择 {{ selectedRowKeys.length }} 个文件</span>
        <a-space>
          <a-button danger @click="handleBatchDelete" :loading="batchDeleteLoading">
            <template #icon><DeleteOutlined /></template>
            批量删除
          </a-button>
          <a-button @click="clearSelection">
            取消选择
          </a-button>
        </a-space>
      </div>
    </a-card>

    <!-- 媒体文件列表 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="mediaFiles"
        :loading="loading"
        :pagination="paginationConfig"
        :row-selection="rowSelection"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'preview'">
            <div class="media-preview">
              <a-image
                v-if="record.media_type === 'image'"
                :src="record.file_url"
                :width="60"
                :height="40"
                style="border-radius: 4px; object-fit: cover"
                :preview="{ src: record.file_url }"
              />
              <div v-else-if="record.media_type === 'video'" class="video-preview">
                <VideoCameraOutlined style="font-size: 24px; color: #fa8c16" />
              </div>
              <div v-else-if="record.media_type === 'document'" class="document-preview">
                <FileTextOutlined style="font-size: 24px; color: #1890ff" />
              </div>
              <div v-else class="other-preview">
                <FileOutlined style="font-size: 24px; color: #8c8c8c" />
              </div>
            </div>
          </template>

          <template v-else-if="column.key === 'filename'">
            <div class="filename-cell">
              <div class="filename">{{ record.original_filename }}</div>
              <div class="file-info">
                {{ formatFileSize(record.file_size) }} • {{ getMediaTypeText(record.media_type) }}
              </div>
            </div>
          </template>

          <template v-else-if="column.key === 'category'">
            <a-tag :color="getCategoryColor(record.category)">
              {{ getCategoryText(record.category) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'created_at'">
            <div class="date-cell">
              {{ formatDate(record.created_at) }}
            </div>
          </template>

          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button size="small" @click="handleView(record)">
                <template #icon><EyeOutlined /></template>
                查看
              </a-button>
              <a-button size="small" @click="handleEdit(record)">
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>
              <a-button size="small" @click="handleDownload(record)">
                <template #icon><DownloadOutlined /></template>
                下载
              </a-button>
              <a-button size="small" danger @click="handleDelete(record)">
                <template #icon><DeleteOutlined /></template>
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 上传文件模态框 -->
    <a-modal
      v-model:open="uploadModalVisible"
      title="上传文件"
      :confirm-loading="uploadLoading"
      width="600px"
      @ok="handleUploadSubmit"
      @cancel="handleUploadCancel"
    >
      <a-form
        ref="uploadFormRef"
        :model="uploadForm"
        :rules="uploadRules"
        layout="vertical"
      >
        <a-form-item label="选择文件" name="file">
          <a-upload-dragger
            v-model:file-list="fileList"
            :before-upload="beforeUpload"
            :multiple="false"
            :show-upload-list="true"
            accept="image/*,video/*,.pdf,.doc,.docx,.txt,.csv"
          >
            <p class="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">
              支持图片、视频、文档等格式，单个文件不超过100MB
            </p>
          </a-upload-dragger>
        </a-form-item>

        <a-form-item label="文件分类" name="category">
          <a-select
            v-model:value="uploadForm.category"
            placeholder="请选择文件分类"
          >
            <a-select-option value="avatar">头像</a-select-option>
            <a-select-option value="timeline_image">时间线图片</a-select-option>
            <a-select-option value="timeline_video">时间线视频</a-select-option>
            <a-select-option value="general">通用文件</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="文件描述" name="description">
          <a-textarea
            v-model:value="uploadForm.description"
            placeholder="请输入文件描述（可选）"
            :rows="3"
            :maxlength="500"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑文件模态框 -->
    <a-modal
      v-model:open="editModalVisible"
      title="编辑文件信息"
      :confirm-loading="editLoading"
      width="500px"
      @ok="handleEditSubmit"
      @cancel="handleEditCancel"
    >
      <a-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        layout="vertical"
      >
        <a-form-item label="文件分类" name="category">
          <a-select
            v-model:value="editForm.category"
            placeholder="请选择文件分类"
          >
            <a-select-option value="avatar">头像</a-select-option>
            <a-select-option value="timeline_image">时间线图片</a-select-option>
            <a-select-option value="timeline_video">时间线视频</a-select-option>
            <a-select-option value="general">通用文件</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="文件描述" name="description">
          <a-textarea
            v-model:value="editForm.description"
            placeholder="请输入文件描述（可选）"
            :rows="3"
            :maxlength="500"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看文件模态框 -->
    <a-modal
      v-model:open="viewModalVisible"
      :title="viewFile?.original_filename"
      :footer="null"
      width="800px"
      centered
    >
      <div v-if="viewFile" class="file-detail">
        <div class="file-preview">
          <a-image
            v-if="viewFile.media_type === 'image'"
            :src="viewFile.file_url"
            style="max-width: 100%; max-height: 400px"
          />
          <div v-else-if="viewFile.media_type === 'video'" class="video-container">
            <video
              :src="viewFile.file_url"
              controls
              style="max-width: 100%; max-height: 400px"
            >
              您的浏览器不支持视频播放
            </video>
          </div>
          <div v-else class="file-info-display">
            <FileOutlined style="font-size: 48px; color: #8c8c8c" />
            <p>{{ viewFile.original_filename }}</p>
          </div>
        </div>

        <a-descriptions :column="2" style="margin-top: 16px">
          <a-descriptions-item label="文件名">
            {{ viewFile.original_filename }}
          </a-descriptions-item>
          <a-descriptions-item label="文件大小">
            {{ formatFileSize(viewFile.file_size) }}
          </a-descriptions-item>
          <a-descriptions-item label="媒体类型">
            {{ getMediaTypeText(viewFile.media_type) }}
          </a-descriptions-item>
          <a-descriptions-item label="分类">
            {{ getCategoryText(viewFile.category) }}
          </a-descriptions-item>
          <a-descriptions-item label="MIME类型">
            {{ viewFile.mime_type }}
          </a-descriptions-item>
          <a-descriptions-item label="上传时间">
            {{ formatDate(viewFile.created_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="文件描述" :span="2">
            {{ viewFile.description || '无描述' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  UploadOutlined,
  ReloadOutlined,
  SearchOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  DownloadOutlined,
  VideoCameraOutlined,
  FileTextOutlined,
  FileOutlined,
  InboxOutlined
} from '@ant-design/icons-vue'
import { mediaAPI } from '../../api'

export default {
  name: 'AdminMedia',
  components: {
    UploadOutlined,
    ReloadOutlined,
    SearchOutlined,
    DeleteOutlined,
    EditOutlined,
    EyeOutlined,
    DownloadOutlined,
    VideoCameraOutlined,
    FileTextOutlined,
    FileOutlined,
    InboxOutlined
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const uploadLoading = ref(false)
    const editLoading = ref(false)
    const batchDeleteLoading = ref(false)

    const mediaFiles = ref([])
    const mediaStats = ref({})
    const selectedRowKeys = ref([])

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
    })

    // 搜索表单
    const searchForm = reactive({
      search: '',
      media_type: undefined,
      category: undefined
    })

    // 上传相关
    const uploadModalVisible = ref(false)
    const uploadFormRef = ref()
    const fileList = ref([])
    const uploadForm = reactive({
      category: 'general',
      description: ''
    })

    // 编辑相关
    const editModalVisible = ref(false)
    const editFormRef = ref()
    const editForm = reactive({
      category: '',
      description: ''
    })
    const currentEditId = ref(null)

    // 查看相关
    const viewModalVisible = ref(false)
    const viewFile = ref(null)

    // 表格列定义
    const columns = [
      {
        title: '预览',
        key: 'preview',
        width: 80,
        align: 'center'
      },
      {
        title: '文件信息',
        key: 'filename',
        width: 300
      },
      {
        title: '分类',
        key: 'category',
        width: 120,
        align: 'center'
      },
      {
        title: '上传时间',
        key: 'created_at',
        width: 180,
        align: 'center'
      },
      {
        title: '操作',
        key: 'actions',
        width: 280,
        align: 'center'
      }
    ]

    // 表单验证规则
    const uploadRules = {
      category: [
        { required: true, message: '请选择文件分类', trigger: 'change' }
      ]
    }

    const editRules = {
      category: [
        { required: true, message: '请选择文件分类', trigger: 'change' }
      ]
    }

    // 计算属性
    const paginationConfig = computed(() => ({
      ...pagination,
      onChange: (page, pageSize) => {
        pagination.current = page
        pagination.pageSize = pageSize
        loadMediaFiles()
      },
      onShowSizeChange: (current, size) => {
        pagination.current = 1
        pagination.pageSize = size
        loadMediaFiles()
      }
    }))

    const rowSelection = computed(() => ({
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys) => {
        selectedRowKeys.value = keys
      }
    }))

    // 方法
    const loadMediaFiles = async () => {
      try {
        loading.value = true
        const params = {
          page: pagination.current,
          size: pagination.pageSize,
          ...searchForm
        }

        // 过滤空值
        Object.keys(params).forEach(key => {
          if (params[key] === undefined || params[key] === '') {
            delete params[key]
          }
        })

        const response = await mediaAPI.getMediaFiles(params)
        mediaFiles.value = response.items || []
        pagination.total = response.total || 0
      } catch (error) {
        console.error('加载媒体文件失败:', error)
        message.error('加载媒体文件失败')
      } finally {
        loading.value = false
      }
    }

    const loadMediaStats = async () => {
      try {
        const response = await mediaAPI.getMediaStats()
        mediaStats.value = response
      } catch (error) {
        console.error('加载媒体统计失败:', error)
      }
    }

    const refreshData = () => {
      loadMediaFiles()
      loadMediaStats()
    }

    const handleSearch = () => {
      pagination.current = 1
      loadMediaFiles()
    }

    const resetSearch = () => {
      Object.assign(searchForm, {
        search: '',
        media_type: undefined,
        category: undefined
      })
      pagination.current = 1
      loadMediaFiles()
    }

    const handleTableChange = (pag, filters, sorter) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      loadMediaFiles()
    }

    // 上传相关方法
    const showUploadModal = () => {
      uploadModalVisible.value = true
    }

    const beforeUpload = (file) => {
      // 文件类型验证
      const isValidType = file.type.startsWith('image/') ||
                         file.type.startsWith('video/') ||
                         file.type === 'application/pdf' ||
                         file.type.includes('document') ||
                         file.type === 'text/plain' ||
                         file.type === 'text/csv'

      if (!isValidType) {
        message.error('不支持的文件类型')
        return false
      }

      // 文件大小验证
      const isLt100M = file.size / 1024 / 1024 < 100
      if (!isLt100M) {
        message.error('文件大小不能超过100MB')
        return false
      }

      return false // 阻止自动上传
    }

    const handleUploadSubmit = async () => {
      try {
        await uploadFormRef.value.validate()

        if (fileList.value.length === 0) {
          message.error('请选择要上传的文件')
          return
        }

        uploadLoading.value = true

        const formData = new FormData()
        formData.append('file', fileList.value[0].originFileObj)
        formData.append('category', uploadForm.category)
        if (uploadForm.description) {
          formData.append('description', uploadForm.description)
        }

        await mediaAPI.uploadFile(formData)
        message.success('文件上传成功')

        handleUploadCancel()
        refreshData()
      } catch (error) {
        console.error('文件上传失败:', error)
        message.error('文件上传失败')
      } finally {
        uploadLoading.value = false
      }
    }

    const handleUploadCancel = () => {
      uploadModalVisible.value = false
      fileList.value = []
      Object.assign(uploadForm, {
        category: 'general',
        description: ''
      })
      if (uploadFormRef.value) {
        uploadFormRef.value.resetFields()
      }
    }

    // 编辑相关方法
    const handleEdit = (record) => {
      currentEditId.value = record.id
      editForm.category = record.category
      editForm.description = record.description || ''
      editModalVisible.value = true
    }

    const handleEditSubmit = async () => {
      try {
        await editFormRef.value.validate()
        editLoading.value = true

        await mediaAPI.updateMediaFile(currentEditId.value, {
          category: editForm.category,
          description: editForm.description
        })

        message.success('文件信息更新成功')
        handleEditCancel()
        loadMediaFiles()
      } catch (error) {
        console.error('更新文件信息失败:', error)
        message.error('更新文件信息失败')
      } finally {
        editLoading.value = false
      }
    }

    const handleEditCancel = () => {
      editModalVisible.value = false
      currentEditId.value = null
      Object.assign(editForm, {
        category: '',
        description: ''
      })
      if (editFormRef.value) {
        editFormRef.value.resetFields()
      }
    }

    // 查看文件
    const handleView = (record) => {
      viewFile.value = record
      viewModalVisible.value = true
    }

    // 下载文件
    const handleDownload = async (record) => {
      try {
        const response = await mediaAPI.downloadFile(record.id)

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', record.original_filename)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)

        message.success('文件下载成功')
      } catch (error) {
        console.error('文件下载失败:', error)
        message.error('文件下载失败')
      }
    }

    // 删除文件
    const handleDelete = (record) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除文件"${record.original_filename}"吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            await mediaAPI.deleteMediaFile(record.id)
            message.success('文件删除成功')
            loadMediaFiles()
          } catch (error) {
            console.error('删除文件失败:', error)
            message.error('删除文件失败')
          }
        }
      })
    }

    // 批量删除
    const handleBatchDelete = () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请选择要删除的文件')
        return
      }

      Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${selectedRowKeys.value.length} 个文件吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            batchDeleteLoading.value = true

            // 使用批量删除API
            await mediaAPI.batchDeleteFiles(selectedRowKeys.value)

            message.success(`成功删除 ${selectedRowKeys.value.length} 个文件`)
            clearSelection()
            loadMediaFiles()
          } catch (error) {
            console.error('批量删除失败:', error)
            message.error('批量删除失败')
          } finally {
            batchDeleteLoading.value = false
          }
        }
      })
    }

    const clearSelection = () => {
      selectedRowKeys.value = []
    }

    // 工具函数
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const formatDate = (dateString) => {
      return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
    }

    const getMediaTypeText = (type) => {
      const typeMap = {
        image: '图片',
        video: '视频',
        document: '文档',
        other: '其他'
      }
      return typeMap[type] || '未知'
    }

    const getCategoryText = (category) => {
      const categoryMap = {
        avatar: '头像',
        timeline_image: '时间线图片',
        timeline_video: '时间线视频',
        general: '通用文件'
      }
      return categoryMap[category] || '未知'
    }

    const getCategoryColor = (category) => {
      const colorMap = {
        avatar: 'blue',
        timeline_image: 'green',
        timeline_video: 'orange',
        general: 'default'
      }
      return colorMap[category] || 'default'
    }

    // 生命周期
    onMounted(() => {
      loadMediaFiles()
      loadMediaStats()
    })

    return {
      // 响应式数据
      loading,
      uploadLoading,
      editLoading,
      batchDeleteLoading,
      mediaFiles,
      mediaStats,
      selectedRowKeys,
      searchForm,
      uploadModalVisible,
      uploadFormRef,
      fileList,
      uploadForm,
      editModalVisible,
      editFormRef,
      editForm,
      viewModalVisible,
      viewFile,

      // 配置
      columns,
      uploadRules,
      editRules,
      paginationConfig,
      rowSelection,

      // 方法
      refreshData,
      handleSearch,
      resetSearch,
      handleTableChange,
      showUploadModal,
      beforeUpload,
      handleUploadSubmit,
      handleUploadCancel,
      handleEdit,
      handleEditSubmit,
      handleEditCancel,
      handleView,
      handleDownload,
      handleDelete,
      handleBatchDelete,
      clearSelection,

      // 工具函数
      formatFileSize,
      formatDate,
      getMediaTypeText,
      getCategoryText,
      getCategoryColor
    }
  }
}
</script>

<style scoped>
.admin-media {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.header-left h2 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 20px;
  font-weight: 600;
}

.stats-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-card {
  margin-bottom: 16px;
}

.search-form {
  padding: 8px 0;
}

.search-item {
  display: flex;
  flex-direction: column;
}

.search-label {
  margin-bottom: 8px;
  color: #262626;
  font-weight: 500;
  font-size: 14px;
}

.batch-actions {
  margin-bottom: 16px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.batch-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #1890ff;
  font-weight: 500;
}

.media-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 40px;
  border-radius: 4px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
}

.video-preview,
.document-preview,
.other-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.filename-cell {
  display: flex;
  flex-direction: column;
}

.filename {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-info {
  color: #8c8c8c;
  font-size: 12px;
}

.date-cell {
  color: #595959;
  font-size: 13px;
}

.file-detail {
  text-align: center;
}

.file-preview {
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.video-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.file-info-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-end;
  }

  .stats-info {
    justify-content: flex-start;
  }

  .search-form .ant-col {
    margin-bottom: 16px;
  }

  .batch-info {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

/* 上传组件样式 */
:deep(.ant-upload-drag) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s;
}

:deep(.ant-upload-drag:hover) {
  border-color: #1890ff;
}

:deep(.ant-upload-drag-icon) {
  font-size: 48px;
  color: #1890ff;
}

:deep(.ant-upload-text) {
  font-size: 16px;
  color: #262626;
  margin: 16px 0 8px;
}

:deep(.ant-upload-hint) {
  color: #8c8c8c;
  font-size: 14px;
}
</style>
