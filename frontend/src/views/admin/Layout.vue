<template>
  <a-layout style="min-height: 100vh" class="admin-layout" :class="{ 'mobile-menu-open': mobileMenuOpen }">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      theme="dark"
    >
      <div class="admin-logo">
        <h3 v-if="!collapsed">数字生命馆</h3>
        <h3 v-else>数字</h3>
      </div>

      <a-menu
        v-model:selectedKeys="selectedKeys"
        theme="dark"
        mode="inline"
        @click="handleMenuClick"
      >
        <a-menu-item key="dashboard">
          <template #icon><DashboardOutlined /></template>
          <span>仪表盘</span>
        </a-menu-item>

        <a-menu-item key="categories">
          <template #icon><AppstoreOutlined /></template>
          <span>分类管理</span>
        </a-menu-item>

        <a-menu-item key="entities">
          <template #icon><UserOutlined /></template>
          <span>实体管理</span>
        </a-menu-item>

        <a-menu-item key="timelines">
          <template #icon><ClockCircleOutlined /></template>
          <span>时间线管理</span>
        </a-menu-item>

        <a-menu-item key="media">
          <template #icon><FileImageOutlined /></template>
          <span>媒体管理</span>
        </a-menu-item>

        <a-menu-item key="statistics">
          <template #icon><BarChartOutlined /></template>
          <span>数据统计</span>
        </a-menu-item>

        <a-menu-item key="ai-create">
          <template #icon><RobotOutlined /></template>
          <span>AI创建</span>
        </a-menu-item>
        <a-menu-item key="about-us">
          <template #icon><InfoCircleOutlined /></template>
          <span>关于我们</span>
        </a-menu-item>
        <a-menu-item key="friend-links">
          <template #icon><LinkOutlined /></template>
          <span>友情链接</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>

    <!-- 主内容区 -->
    <a-layout>
      <!-- 顶部导航 -->
      <a-layout-header class="admin-header">
        <div class="header-left">
          <a-button
            type="text"
            @click="handleMenuToggle"
            class="trigger"
          >
            <template #icon>
              <MenuUnfoldOutlined v-if="collapsed || isMobile" />
              <MenuFoldOutlined v-else />
            </template>
          </a-button>
        </div>

        <div class="header-right">
          <a-dropdown>
            <a-button type="text" class="user-info">
              <template #icon><UserOutlined /></template>
              {{ currentUser?.username || '管理员' }}
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleLogout">
                  <template #icon><LogoutOutlined /></template>
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 内容区域 -->
      <a-layout-content class="admin-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </a-layout-content>
    </a-layout>

    <!-- 移动端遮罩层 -->
    <div class="mobile-overlay" @click="closeMobileMenu"></div>
  </a-layout>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  DashboardOutlined,
  AppstoreOutlined,
  UserOutlined,
  ClockCircleOutlined,
  FileImageOutlined,
  BarChartOutlined,
  RobotOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  DownOutlined,
  LogoutOutlined,
  InfoCircleOutlined,
  LinkOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useAuthStore } from '../../stores/auth'

export default {
  name: 'AdminLayout',
  components: {
    DashboardOutlined,
    AppstoreOutlined,
    UserOutlined,
    ClockCircleOutlined,
    FileImageOutlined,
    BarChartOutlined,
    MenuUnfoldOutlined,
    MenuFoldOutlined,
    DownOutlined,
    LogoutOutlined,
    RobotOutlined,
    InfoCircleOutlined,
    LinkOutlined
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const authStore = useAuthStore()

    // 响应式数据
    const collapsed = ref(false)
    const selectedKeys = ref(['dashboard'])
    const mobileMenuOpen = ref(false)
    const isMobile = ref(false)

    // 计算属性
    const currentUser = computed(() => authStore.user)

    // 方法
    const handleMenuClick = ({ key }) => {
      router.push(`/admin/${key === 'dashboard' ? '' : key}`)
    }

    const handleLogout = async () => {
      try {
        await authStore.logout()
        message.success('退出登录成功')
        router.push('/admin/login')
      } catch (error) {
        console.error('退出登录失败:', error)
        message.error('退出登录失败')
      }
    }

    // 移动端菜单控制
    const handleMenuToggle = () => {
      if (isMobile.value) {
        mobileMenuOpen.value = !mobileMenuOpen.value
      } else {
        collapsed.value = !collapsed.value
      }
    }

    const closeMobileMenu = () => {
      mobileMenuOpen.value = false
    }

    // 检测屏幕尺寸
    const checkScreenSize = () => {
      isMobile.value = window.innerWidth <= 768
      if (!isMobile.value) {
        mobileMenuOpen.value = false
      }
    }

    // 根据路由更新选中的菜单项
    const updateSelectedKeys = () => {
      const path = route.path
      if (path === '/admin' || path === '/admin/') {
        selectedKeys.value = ['dashboard']
      } else {
        const key = path.replace('/admin/', '')
        selectedKeys.value = [key]
      }
    }

    // 监听路由变化
    watch(() => route.path, updateSelectedKeys, { immediate: true })

    onMounted(() => {
      updateSelectedKeys()
      checkScreenSize()
      window.addEventListener('resize', checkScreenSize)
    })

    return {
      collapsed,
      selectedKeys,
      currentUser,
      mobileMenuOpen,
      isMobile,
      handleMenuClick,
      handleLogout,
      handleMenuToggle,
      closeMobileMenu
    }
  }
}
</script>

<style scoped>
.admin-logo {
  height: 32px;
  margin: 16px;
  color: white;
  text-align: center;
}

.admin-logo h3 {
  color: white;
  margin: 0;
  font-size: 16px;
}

.admin-header {
  background: #fff;
  padding: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
  padding-left: 16px;
}

.trigger {
  font-size: 18px;
  /* line-height: 64px; */
  /* padding: 0 24px; */
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.header-right {
  display: flex;
  align-items: center;
  padding-right: 24px;
}

.user-info {
  height: 64px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-content {
  margin: 24px;
  background: #fff;
  border-radius: 6px;
  min-height: calc(100vh - 112px);
}

.content-wrapper {
  padding: 24px;
}

/* ========== 响应式设计 ========== */
/* 平板设备 */
@media (max-width: 1024px) {
  .admin-content {
    margin: 16px;
  }

  .content-wrapper {
    padding: 16px;
  }

  .header-left .trigger {
    margin-right: 8px;
  }
}

/* 移动设备 */
@media (max-width: 768px) {
  .admin-layout :deep(.ant-layout-sider) {
    position: fixed !important;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1001;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .admin-layout :deep(.ant-layout-sider.ant-layout-sider-collapsed) {
    transform: translateX(-100%);
  }

  .admin-layout.mobile-menu-open :deep(.ant-layout-sider) {
    transform: translateX(0);
  }

  .admin-layout :deep(.ant-layout-content) {
    margin-left: 0 !important;
  }

  .admin-header {
    padding: 0 var(--mobile-padding);
    height: var(--header-height-mobile);
  }

  .header-left .trigger {
    height: var(--mobile-button-height);
    width: var(--mobile-button-height);
    margin-right: 0;
  }

  .user-info {
    height: var(--header-height-mobile);
    padding: 0 8px;
    font-size: 14px;
  }

  .admin-content {
    margin: var(--mobile-margin);
    min-height: calc(100vh - var(--header-height-mobile) - 24px);
  }

  .content-wrapper {
    padding: var(--mobile-padding);
  }

  .admin-logo {
    padding: var(--mobile-padding);
    text-align: center;
  }

  .admin-logo h3 {
    font-size: 16px;
    margin: 0;
  }
}

/* 超小屏幕 */
@media (max-width: 480px) {
  .admin-header {
    padding: 0 12px;
  }

  .admin-content {
    margin: 8px;
  }

  .content-wrapper {
    padding: 12px;
  }

  .user-info {
    padding: 0 6px;
    font-size: 13px;
  }

  .user-info span {
    display: none;
  }
}

/* 移动端遮罩层 */
@media (max-width: 768px) {
  .mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .admin-layout.mobile-menu-open .mobile-overlay {
    opacity: 1;
    visibility: visible;
  }
}
</style>
