<template>
  <div class="category-page">
    <a-breadcrumb style="margin: 16px 0">
      <a-breadcrumb-item>
        <router-link to="/">首页</router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>分类浏览</a-breadcrumb-item>
      <a-breadcrumb-item v-if="currentCategory">{{ currentCategory.name }}</a-breadcrumb-item>
    </a-breadcrumb>

    <div class="category-content">
      <!-- 分类信息 -->
      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
      </div>
      <div v-else-if="currentCategory" class="category-header">
        <h1>{{ currentCategory.name }}</h1>
        <p v-if="currentCategory.description" class="category-description">
          {{ currentCategory.description }}
        </p>
        <div class="category-stats">
          <a-statistic-countdown
            :value="Date.now() + 1000 * 60 * 60 * 24 * 2"
            format="HH:mm:ss"
            style="display: none;"
          />
          <a-tag color="blue">权重: {{ currentCategory.sort_order || 0 }}</a-tag>
          <a-tag color="green">状态: {{ currentCategory.is_active ? '启用' : '禁用' }}</a-tag>
        </div>
      </div>
      <div v-else class="error-state">
        <a-result
          status="404"
          title="分类不存在"
          sub-title="抱歉，您访问的分类不存在或已被删除。"
        >
          <template #extra>
            <a-button type="primary" @click="$router.push('/')">
              返回首页
            </a-button>
          </template>
        </a-result>
      </div>

      <!-- 实体列表 -->
      <div v-if="currentCategory" class="entities-section">
        <EntityList
          :preset-filters="{ category_id: parseInt($route.params.id) }"
          :show-filters="true"
          :show-pagination="true"
        />
      </div>
    </div>
  </div>
</template>

<script>
import EntityList from '../components/EntityList.vue'
import { useCategoriesStore, useAppStore } from '../stores'
import { computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'

export default {
  name: 'Category',
  components: {
    EntityList
  },
  setup() {
    const route = useRoute()
    const categoriesStore = useCategoriesStore()
    const appStore = useAppStore()

    // 计算属性
    const currentCategory = computed(() => categoriesStore.currentCategory)
    const loading = computed(() => categoriesStore.loading)

    // 方法
    const loadCategory = async (id) => {
      try {
        await categoriesStore.fetchCategory(parseInt(id))
        if (categoriesStore.currentCategory) {
          appStore.setPageTitle(`${categoriesStore.currentCategory.name} - 分类浏览`)
        }
      } catch (error) {
        console.error('加载分类失败:', error)
      }
    }

    // 监听路由参数变化
    watch(
      () => route.params.id,
      (newId) => {
        if (newId) {
          loadCategory(newId)
        }
      },
      { immediate: true }
    )

    // 组件挂载时加载数据
    onMounted(() => {
      const categoryId = route.params.id
      if (categoryId) {
        loadCategory(categoryId)
      }
    })

    return {
      currentCategory,
      loading
    }
  }
}
</script>

<style scoped>
.category-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: var(--space-xl);
}

.category-page :deep(.ant-breadcrumb) {
  margin: var(--space-lg) 0;
  padding: var(--space-lg) var(--space-2xl);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-xs);
}

.category-page :deep(.ant-breadcrumb a) {
  color: var(--primary-color);
  transition: var(--transition-colors);
}

.category-page :deep(.ant-breadcrumb a:hover) {
  color: var(--primary-hover);
}

.category-content {
  max-width: 1200px;
  margin: 0 auto;
  background: var(--bg-primary);
  padding: var(--space-4xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-card);
}

.loading-container {
  text-align: center;
  padding: var(--space-5xl) 0;
}

.category-header {
  margin-bottom: var(--space-4xl);
  padding-bottom: var(--space-3xl);
  border-bottom: 1px solid var(--border-primary);
  text-align: center;
}

.category-header h1 {
  color: var(--text-primary);
  font-size: var(--font-4xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-lg);
  position: relative;
}

.category-header h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

.category-description {
  color: var(--text-secondary);
  font-size: var(--font-lg);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.category-stats {
  display: flex;
  justify-content: center;
  gap: var(--space-lg);
  flex-wrap: wrap;
}

.category-stats :deep(.ant-tag) {
  border-radius: var(--radius-md);
  padding: var(--space-xs) var(--space-lg);
  font-weight: var(--font-medium);
  border: none;
}

.entities-section {
  margin-top: var(--space-4xl);
}

.error-state {
  text-align: center;
  padding: var(--space-5xl) 0;
}

.error-state :deep(.ant-result-title) {
  color: var(--text-primary);
}

.error-state :deep(.ant-result-subtitle) {
  color: var(--text-secondary);
}

.error-state :deep(.ant-btn-primary) {
  background: var(--primary-color);
  border-color: var(--primary-color);
  border-radius: var(--radius-md);
}

.error-state :deep(.ant-btn-primary:hover) {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-page {
    padding: var(--space-lg);
  }

  .category-content {
    padding: var(--space-2xl);
  }

  .category-header h1 {
    font-size: var(--font-3xl);
  }

  .category-description {
    font-size: var(--font-base);
  }

  .category-stats {
    gap: var(--space-sm);
  }
}
</style>
