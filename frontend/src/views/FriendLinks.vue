<template>
  <div class="friend-links-page">
    <!-- 返回按钮 -->
    <div class="back-button-container">
      <a-button type="text" class="back-button" @click="goBack">
        <ArrowLeftOutlined />
        返回首页
      </a-button>
    </div>

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <LinkOutlined class="title-icon" />
          友情链接
        </h1>
        <p class="page-description">感谢以下网站的支持与合作</p>
      </div>
    </div>

    <!-- 友情链接内容 -->
    <div class="links-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <a-spin size="large" tip="加载中..." />
      </div>

      <!-- 友情链接列表 -->
      <div v-else-if="activeFriendLinks.length > 0" class="links-grid">
        <div
          v-for="link in activeFriendLinks"
          :key="link.id"
          class="link-card"
          @click="openLink(link.url)"
        >
          <div class="link-icon">
            <img
              v-if="link.icon_url"
              :src="link.icon_url"
              :alt="link.name"
              @error="handleImageError"
            />
            <LinkOutlined v-else class="default-icon" />
          </div>
          <div class="link-info">
            <h3 class="link-name">{{ link.name }}</h3>
            <p v-if="link.description" class="link-description">{{ link.description }}</p>
            <div class="link-url">{{ formatUrl(link.url) }}</div>
          </div>
          <div class="link-arrow">
            <ArrowRightOutlined />
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-container">
        <a-empty
          description="暂无友情链接"
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { LinkOutlined, ArrowRightOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue'
import { Empty, message } from 'ant-design-vue'
import { useFriendLinksStore } from '@/stores/friend-links'

// 路由
const router = useRouter()

// 状态管理
const friendLinksStore = useFriendLinksStore()
const { activeFriendLinks, loading } = storeToRefs(friendLinksStore)

// 返回首页
const goBack = () => {
  router.push('/')
}

// 页面加载时获取友情链接
onMounted(() => {
  friendLinksStore.fetchActiveFriendLinks()
})

// 打开链接
const openLink = (url) => {
  if (url) {
    // 确保URL包含协议
    const fullUrl = url.startsWith('http') ? url : `https://${url}`
    window.open(fullUrl, '_blank', 'noopener,noreferrer')
  }
}

// 格式化URL显示
const formatUrl = (url) => {
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`)
    return urlObj.hostname
  } catch {
    return url
  }
}

// 图片加载错误处理
const handleImageError = (event) => {
  event.target.style.display = 'none'
  event.target.nextElementSibling.style.display = 'flex'
}
</script>

<script>
import { storeToRefs } from 'pinia'
export default {
  name: 'FriendLinks'
}
</script>

<style scoped>
.friend-links-page {
  min-height: 100vh;
  background: var(--gradient-bg-hero);
  padding: 40px 20px;
  position: relative;
}

.back-button-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-base);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  transition: var(--transition-all);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.back-button:hover {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateX(-2px);
}

.page-header {
  text-align: center;
  margin-bottom: 60px;
}

.header-content {
  max-width: 600px;
  margin: 0 auto;
}

.page-title {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.title-icon {
  font-size: 2.5rem;
}

.page-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.links-container {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  padding: 0 20px;
}

.link-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  cursor: pointer;
  transition: var(--transition-all);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-primary);
}

.link-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-primary-lg);
  border-color: var(--primary-color);
}

.link-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  flex-shrink: 0;
}

.link-icon img {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  object-fit: cover;
}

.default-icon {
  font-size: 24px;
  color: var(--text-tertiary);
}

.link-info {
  flex: 1;
  min-width: 0;
}

.link-name {
  font-size: var(--font-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.link-description {
  font-size: var(--font-base);
  color: var(--text-secondary);
  margin: 0 0 4px 0;
  line-height: var(--line-height-normal);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.link-url {
  font-size: var(--font-sm);
  color: var(--text-tertiary);
  font-family: monospace;
}

.link-arrow {
  color: var(--primary-color);
  font-size: 16px;
  opacity: 0.7;
  transition: var(--transition-all);
}

.link-card:hover .link-arrow {
  opacity: 1;
  transform: translateX(4px);
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  margin: 0 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .friend-links-page {
    padding: 20px 10px;
  }

  .back-button-container {
    top: 15px;
    left: 15px;
  }

  .back-button {
    padding: 6px 12px;
    font-size: 13px;
  }

  .page-title {
    font-size: 2rem;
  }

  .title-icon {
    font-size: 1.8rem;
  }

  .page-description {
    font-size: 1rem;
  }

  .links-grid {
    grid-template-columns: 1fr;
    padding: 0 10px;
  }

  .link-card {
    padding: 16px;
  }
}
</style>
