<template>
  <div class="entity-timeline-page">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <!-- 时间线展示 -->
    <TimeLines
      v-else-if="entity"
      :entity-name="entity.name"
      :entity-subtitle="getEntityTypeLabel(entity.type || entity.entity_type).toUpperCase()"
      :timeline-data="entityTimelines"
      :default-image="entity.avatar || entity.cover_image"
    />
    
    <!-- 错误状态 -->
    <div v-else class="error-state">
      <a-result
        status="404"
        title="实体不存在"
        sub-title="抱歉，您访问的实体不存在或已被删除。"
      >
        <template #extra>
          <a-button type="primary" @click="$router.push('/')">
            返回首页
          </a-button>
        </template>
      </a-result>
    </div>
  </div>
</template>

<script>
import TimeLines from '../components/timeLines.vue'
import { useEntitiesStore, useTimelinesStore, useAppStore } from '../stores'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

export default {
  name: 'EntityTimeline',
  components: {
    TimeLines
  },
  setup() {
    const route = useRoute()
    const entitiesStore = useEntitiesStore()
    const timelinesStore = useTimelinesStore()
    const appStore = useAppStore()

    // 计算属性
    const entity = computed(() => entitiesStore.currentEntity)
    const loading = computed(() => entitiesStore.loading || timelinesStore.loading)
    const entityTimelines = computed(() => timelinesStore.entityTimelines)

    // 方法
    const loadEntity = async (id) => {
      try {
        await entitiesStore.fetchEntity(parseInt(id))
        if (entitiesStore.currentEntity) {
          appStore.setPageTitle(`${entitiesStore.currentEntity.name} - 时间线`)
          // 加载该实体的时间线
          await loadEntityTimelines(parseInt(id))
        }
      } catch (error) {
        console.error('加载实体失败:', error)
      }
    }

    const loadEntityTimelines = async (entityId) => {
      try {
        await timelinesStore.fetchTimelinesByEntity(entityId)
      } catch (error) {
        console.error('加载时间线失败:', error)
      }
    }

    const getEntityTypeLabel = (type) => {
      const labelMap = {
        person: '人物',
        event: '事件',
        enterprise: '企业'
      }
      return labelMap[type] || '未知'
    }

    // 组件挂载时加载数据
    onMounted(() => {
      const entityId = route.params.id
      if (entityId) {
        loadEntity(entityId)
      }
    })

    return {
      entity,
      loading,
      entityTimelines,
      getEntityTypeLabel
    }
  }
}
</script>

<style scoped>
.entity-timeline-page {
  min-height: 100vh;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #f5f5f5;
}

.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #f5f5f5;
}
</style>
