<template>
  <div class="about-page">
    <!-- 返回按钮 -->
    <div class="back-button-container">
      <a-button type="text" class="back-button" @click="goBack">
        <ArrowLeftOutlined />
        返回首页
      </a-button>
    </div>

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <InfoCircleOutlined class="title-icon" />
          关于我们
        </h1>
        <p class="page-description">了解数字生命馆的故事与使命</p>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-container">
      <div class="content-card">
        <div v-if="loading" class="loading-container">
          <a-spin size="large" tip="加载中..." />
        </div>
        <div v-else class="content-wrapper">
          <div class="markdown-content">
            <MdPreview v-model="aboutUs.content" previewOnly />
          </div>
          <div v-if="aboutUs.donation_image_url" class="donation-section">
            <div class="donation-header">
              <h3>支持我们</h3>
              <p>如果您觉得数字生命馆对您有帮助，欢迎打赏支持</p>
            </div>
            <div class="donation-image">
              <img :src="aboutUs.donation_image_url" alt="打赏码" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { InfoCircleOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';
import { aboutAPI } from '../api/about';
import { MdPreview } from 'md-editor-v3';
import 'md-editor-v3/lib/style.css';

export default {
  name: 'About',
  components: {
    MdPreview,
    InfoCircleOutlined,
    ArrowLeftOutlined,
  },
  setup() {
    const router = useRouter();
    const aboutUs = ref({
      content: '',
      donation_image_url: '',
    });
    const loading = ref(true);

    const goBack = () => {
      router.push('/');
    };

    onMounted(async () => {
      try {
        const response = await aboutAPI.getAboutUs();
        // The `response` is already the data object due to the axios interceptor.
        if (response) {
          aboutUs.value = response;
        }
      } catch (error) {
        console.error('获取关于我们信息失败:', error);
      } finally {
        loading.value = false;
      }
    });

    return {
      aboutUs,
      loading,
      goBack,
    };
  },
};
</script>

<style scoped>
.about-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: var(--space-4xl) var(--space-xl);
  position: relative;
}

.back-button-container {
  position: absolute;
  top: var(--space-2xl);
  left: var(--space-2xl);
  z-index: 10;
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  color: var(--text-secondary);
  font-size: var(--font-base);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  transition: var(--transition-all);
  background: var(--bg-primary);
  box-shadow: var(--shadow-sm);
}

.back-button:hover {
  color: var(--primary-color);
  background: var(--bg-primary);
  box-shadow: var(--shadow-md);
  transform: translateX(-2px);
}

.page-header {
  text-align: center;
  margin-bottom: var(--space-5xl);
}

.header-content {
  max-width: 600px;
  margin: 0 auto;
}

.page-title {
  font-size: var(--font-4xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-lg);
}

.title-icon {
  font-size: var(--font-3xl);
  color: var(--primary-color);
}

.page-description {
  font-size: var(--font-lg);
  color: var(--text-secondary);
  margin: 0;
}

.content-container {
  max-width: 800px;
  margin: 0 auto;
}

.content-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-card);
  overflow: hidden;
}

.loading-container {
  text-align: center;
  padding: var(--space-5xl);
}

.content-wrapper {
  padding: var(--space-4xl);
}

.markdown-content {
  line-height: var(--line-height-relaxed);
  color: var(--text-primary);
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3) {
  color: var(--text-primary);
  margin-top: var(--space-3xl);
  margin-bottom: var(--space-lg);
}

.markdown-content :deep(p) {
  margin-bottom: var(--space-lg);
  color: var(--text-secondary);
}

.donation-section {
  margin-top: var(--space-5xl);
  padding-top: var(--space-4xl);
  border-top: 1px solid var(--border-primary);
  text-align: center;
}

.donation-header h3 {
  font-size: var(--font-2xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.donation-header p {
  color: var(--text-secondary);
  margin-bottom: var(--space-3xl);
}

.donation-image {
  display: flex;
  justify-content: center;
}

.donation-image img {
  max-width: 300px;
  height: auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition-transform);
}

.donation-image img:hover {
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .about-page {
    padding: var(--space-2xl) var(--space-lg);
  }

  .back-button-container {
    top: var(--space-lg);
    left: var(--space-lg);
  }

  .back-button {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--font-sm);
  }

  .page-title {
    font-size: var(--font-3xl);
  }

  .title-icon {
    font-size: var(--font-2xl);
  }

  .content-wrapper {
    padding: var(--space-2xl);
  }

  .donation-image img {
    max-width: 250px;
  }
}
</style>
