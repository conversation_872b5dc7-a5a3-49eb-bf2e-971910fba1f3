<template>
  <div class="not-found-page">
    <div class="not-found-content">
      <a-result
        status="404"
        title="404"
        sub-title="抱歉，您访问的页面不存在。"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" @click="goHome">
              <HomeOutlined />
              返回首页
            </a-button>
            <a-button @click="goBack">
              <ArrowLeftOutlined />
              返回上页
            </a-button>
          </a-space>
        </template>
      </a-result>
    </div>
  </div>
</template>

<script>
import { HomeOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue'

export default {
  name: 'NotFound',
  components: {
    HomeOutlined,
    ArrowLeftOutlined
  },
  methods: {
    goHome() {
      this.$router.push('/')
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.not-found-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl);
}

.not-found-content {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-card);
  padding: var(--space-5xl);
  max-width: 600px;
  width: 100%;
  text-align: center;
}

.not-found-content :deep(.ant-result-title) {
  color: var(--text-primary);
  font-weight: var(--font-bold);
}

.not-found-content :deep(.ant-result-subtitle) {
  color: var(--text-secondary);
  font-size: var(--font-lg);
}

.not-found-content :deep(.ant-btn-primary) {
  background: var(--primary-color);
  border-color: var(--primary-color);
  border-radius: var(--radius-md);
  height: 40px;
  padding: 0 var(--space-xl);
  font-weight: var(--font-medium);
}

.not-found-content :deep(.ant-btn-primary:hover) {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-1px);
}

.not-found-content :deep(.ant-btn) {
  border-radius: var(--radius-md);
  height: 40px;
  padding: 0 var(--space-xl);
  font-weight: var(--font-medium);
  transition: var(--transition-all);
}

.not-found-content :deep(.ant-btn:hover) {
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .not-found-page {
    padding: var(--space-lg);
  }

  .not-found-content {
    padding: var(--space-3xl);
  }

  .not-found-content :deep(.ant-result-subtitle) {
    font-size: var(--font-base);
  }
}
</style>
