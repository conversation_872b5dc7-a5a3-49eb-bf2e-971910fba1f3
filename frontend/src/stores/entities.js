import { defineStore } from 'pinia'
import { entitiesAPI } from '../api/entities'
import { message } from 'ant-design-vue'

export const useEntitiesStore = defineStore('entities', {
  state: () => ({
    // 实体列表
    entities: [],
    // 当前实体
    currentEntity: null,
    // 搜索结果
    searchResults: [],
    // 加载状态
    loading: false,
    // 搜索加载状态
    searchLoading: false,
    // 分页信息
    pagination: {
      page: 1,
      size: 10,
      total: 0
    },
    // 搜索分页信息
    searchPagination: {
      page: 1,
      size: 10,
      total: 0
    },
    // 筛选条件
    filters: {
      category_id: null,
      entity_type: null,
      search: ''
    }
  }),

  getters: {
    // 获取实体类型选项
    entityTypeOptions: () => [
      { label: '人物', value: 'person' },
      { label: '事件', value: 'event' },
      { label: '企业', value: 'enterprise' }
    ],
    
    // 根据ID获取实体
    getEntityById: (state) => (id) => 
      state.entities.find(entity => entity.id === id),
    
    // 是否有实体数据
    hasEntities: (state) => state.entities.length > 0,
    
    // 是否有搜索结果
    hasSearchResults: (state) => state.searchResults.length > 0
  },

  actions: {
    // 获取实体列表
    async fetchEntities(params = {}) {
      this.loading = true
      try {
        const requestParams = {
          page: params.page || this.pagination.page,
          size: params.size || this.pagination.size,
          ...this.filters,
          ...params
        }

        const response = await entitiesAPI.getEntities(requestParams)

        this.entities = response.items || response
        if (response.total !== undefined) {
          this.pagination.total = response.total
          this.pagination.page = requestParams.page
          this.pagination.size = requestParams.size
        }

        return response
      } catch (error) {
        message.error('获取实体列表失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取实体详情
    async fetchEntity(id) {
      this.loading = true
      try {
        const entity = await entitiesAPI.getEntity(id)
        this.currentEntity = entity
        return entity
      } catch (error) {
        message.error('获取实体详情失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 搜索实体
    async searchEntities(params = {}) {
      this.searchLoading = true
      try {
        const requestParams = {
          page: params.page || this.searchPagination.page,
          size: params.size || this.searchPagination.size,
          ...params
        }

        const response = await entitiesAPI.searchEntities(requestParams)

        this.searchResults = response.items || response
        if (response.total !== undefined) {
          this.searchPagination.total = response.total
          this.searchPagination.page = requestParams.page
          this.searchPagination.size = requestParams.size
        }

        return response
      } catch (error) {
        message.error('搜索失败')
        throw error
      } finally {
        this.searchLoading = false
      }
    },

    // 创建实体
    async createEntity(data) {
      this.loading = true
      try {
        const entity = await entitiesAPI.createEntity(data)
        this.entities.unshift(entity)
        this.pagination.total += 1
        message.success('创建实体成功')
        return entity
      } catch (error) {
        message.error('创建实体失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 更新实体
    async updateEntity(id, data) {
      this.loading = true
      try {
        const entity = await entitiesAPI.updateEntity(id, data)
        const index = this.entities.findIndex(e => e.id === id)
        if (index !== -1) {
          this.entities[index] = entity
        }
        if (this.currentEntity?.id === id) {
          this.currentEntity = entity
        }
        return entity
      } catch (error) {
        message.error('更新实体失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 删除实体
    async deleteEntity(id) {
      this.loading = true
      try {
        await entitiesAPI.deleteEntity(id)
        this.entities = this.entities.filter(e => e.id !== id)
        this.pagination.total -= 1
        if (this.currentEntity?.id === id) {
          this.currentEntity = null
        }
        message.success('删除实体成功')
      } catch (error) {
        message.error('删除实体失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 点赞实体
    async likeEntity(id, userInfo = {}) {
      try {
        const result = await entitiesAPI.likeEntity(id, userInfo)
        // 更新实体的点赞数
        const entity = this.entities.find(e => e.id === id)
        if (entity) {
          entity.like_count = result.like_count || result.likes_count
          entity.likes_count = result.like_count || result.likes_count
        }
        if (this.currentEntity?.id === id) {
          this.currentEntity.like_count = result.like_count || result.likes_count
          this.currentEntity.likes_count = result.like_count || result.likes_count
        }
        message.success('点赞成功')
        return result
      } catch (error) {
        message.error('点赞失败')
        throw error
      }
    },

    // 送花给实体
    async flowerEntity(id, userInfo = {}) {
      try {
        const result = await entitiesAPI.flowerEntity(id, userInfo)
        // 更新实体的送花数
        const entity = this.entities.find(e => e.id === id)
        if (entity) {
          entity.flower_count = result.flower_count || result.flowers_count
          entity.flowers_count = result.flower_count || result.flowers_count
        }
        if (this.currentEntity?.id === id) {
          this.currentEntity.flower_count = result.flower_count || result.flowers_count
          this.currentEntity.flowers_count = result.flower_count || result.flowers_count
        }
        message.success('送花成功')
        return result
      } catch (error) {
        message.error('送花失败')
        throw error
      }
    },

    // 设置筛选条件
    setFilters(filters) {
      this.filters = { ...this.filters, ...filters }
    },

    // 设置分页
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 设置搜索分页
    setSearchPagination(pagination) {
      this.searchPagination = { ...this.searchPagination, ...pagination }
    },

    // 重置状态
    resetState() {
      this.entities = []
      this.currentEntity = null
      this.searchResults = []
      this.loading = false
      this.searchLoading = false
      this.pagination = {
        page: 1,
        size: 10,
        total: 0
      }
      this.searchPagination = {
        page: 1,
        size: 10,
        total: 0
      }
      this.filters = {
        category_id: null,
        entity_type: null,
        search: ''
      }
    }
  }
})
