import { defineStore } from 'pinia'
import { categoriesAPI } from '../api/categories'
import { message } from 'ant-design-vue'

export const useCategoriesStore = defineStore('categories', {
  state: () => ({
    // 分类列表
    categories: [],
    // 当前分类
    currentCategory: null,
    // 加载状态
    loading: false,
    // 分页信息
    pagination: {
      page: 1,
      size: 10,
      total: 0
    }
  }),

  getters: {
    // 获取分类选项（用于下拉选择）
    categoryOptions: (state) => 
      state.categories.map(cat => ({
        label: cat.name,
        value: cat.id
      })),
    
    // 根据ID获取分类
    getCategoryById: (state) => (id) => 
      state.categories.find(cat => cat.id === id),
    
    // 是否有分类数据
    hasCategories: (state) => state.categories.length > 0
  },

  actions: {
    // 获取分类列表
    async fetchCategories(params = {}) {
      this.loading = true
      try {
        const requestParams = {
          page: params.page || this.pagination.page,
          size: params.size || this.pagination.size,
          ...params
        }

        const response = await categoriesAPI.getCategories(requestParams)

        this.categories = response.items || response
        if (response.total !== undefined) {
          this.pagination.total = response.total
          this.pagination.page = requestParams.page
          this.pagination.size = requestParams.size
        }

        return response
      } catch (error) {
        message.error('获取分类列表失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取分类详情
    async fetchCategory(id) {
      this.loading = true
      try {
        const category = await categoriesAPI.getCategory(id)
        this.currentCategory = category
        return category
      } catch (error) {
        message.error('获取分类详情失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 创建分类
    async createCategory(data) {
      this.loading = true
      try {
        const category = await categoriesAPI.createCategory(data)
        this.categories.unshift(category)
        this.pagination.total += 1
        message.success('创建分类成功')
        return category
      } catch (error) {
        message.error('创建分类失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 更新分类
    async updateCategory(id, data) {
      this.loading = true
      try {
        const category = await categoriesAPI.updateCategory(id, data)
        const index = this.categories.findIndex(cat => cat.id === id)
        if (index !== -1) {
          this.categories[index] = category
        }
        if (this.currentCategory?.id === id) {
          this.currentCategory = category
        }
        // message.success('更新分类成功')
        return category
      } catch (error) {
        message.error('更新分类失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 删除分类
    async deleteCategory(id) {
      this.loading = true
      try {
        await categoriesAPI.deleteCategory(id)
        this.categories = this.categories.filter(cat => cat.id !== id)
        this.pagination.total -= 1
        if (this.currentCategory?.id === id) {
          this.currentCategory = null
        }
        message.success('删除分类成功')
      } catch (error) {
        message.error('删除分类失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 设置分页
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 重置状态
    resetState() {
      this.categories = []
      this.currentCategory = null
      this.loading = false
      this.pagination = {
        page: 1,
        size: 10,
        total: 0
      }
    }
  }
})
