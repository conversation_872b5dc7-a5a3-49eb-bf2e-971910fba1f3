import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { friendLinksAPI } from '@/api/friend-links'
import { message } from 'ant-design-vue'

export const useFriendLinksStore = defineStore('friendLinks', () => {
  // 状态
  const friendLinks = ref([])
  const activeFriendLinks = ref([])
  const loading = ref(false)
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
  })
  const filters = ref({
    search: '',
    is_active: null,
    sort_by: 'sort_order',
    sort_order: 'desc'
  })

  // 计算属性
  const totalCount = computed(() => pagination.value.total)
  const hasData = computed(() => friendLinks.value.length > 0)

  // 获取启用的友情链接（公开接口）
  const fetchActiveFriendLinks = async () => {
    try {
      loading.value = true
      const response = await friendLinksAPI.getActiveFriendLinks()
      activeFriendLinks.value = response.data || response
    } catch (error) {
      console.error('获取友情链接失败:', error)
      message.error('获取友情链接失败')
    } finally {
      loading.value = false
    }
  }

  // 获取友情链接列表（管理员）
  const fetchFriendLinks = async (params = {}) => {
    try {
      loading.value = true
      const requestParams = {
        page: pagination.value.current,
        size: pagination.value.pageSize,
        ...filters.value,
        ...params
      }
      
      const response = await friendLinksAPI.getFriendLinks(requestParams)
      const data = response.data || response
      
      friendLinks.value = data.items || []
      pagination.value.total = data.total || 0
      pagination.value.current = data.page || 1
      pagination.value.pageSize = data.size || 10
    } catch (error) {
      console.error('获取友情链接列表失败:', error)
      message.error('获取友情链接列表失败')
    } finally {
      loading.value = false
    }
  }

  // 创建友情链接
  const createFriendLink = async (friendLinkData) => {
    try {
      loading.value = true
      const response = await friendLinksAPI.createFriendLink(friendLinkData)
      message.success('友情链接创建成功')
      await fetchFriendLinks()
      return response.data || response
    } catch (error) {
      console.error('创建友情链接失败:', error)
      message.error(error.response?.data?.detail || '创建友情链接失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新友情链接
  const updateFriendLink = async (id, friendLinkData) => {
    try {
      loading.value = true
      const response = await friendLinksAPI.updateFriendLink(id, friendLinkData)
      message.success('友情链接更新成功')
      await fetchFriendLinks()
      return response.data || response
    } catch (error) {
      console.error('更新友情链接失败:', error)
      message.error(error.response?.data?.detail || '更新友情链接失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除友情链接
  const deleteFriendLink = async (id) => {
    try {
      loading.value = true
      await friendLinksAPI.deleteFriendLink(id)
      message.success('友情链接删除成功')
      await fetchFriendLinks()
    } catch (error) {
      console.error('删除友情链接失败:', error)
      message.error(error.response?.data?.detail || '删除友情链接失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 设置分页
  const setPagination = (page, pageSize) => {
    pagination.value.current = page
    pagination.value.pageSize = pageSize
  }

  // 设置筛选条件
  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
    pagination.value.current = 1 // 重置到第一页
  }

  // 重置筛选条件
  const resetFilters = () => {
    filters.value = {
      search: '',
      is_active: null,
      sort_by: 'sort_order',
      sort_order: 'desc'
    }
    pagination.value.current = 1
  }

  // 清空数据
  const clearFriendLinks = () => {
    friendLinks.value = []
    activeFriendLinks.value = []
    pagination.value.total = 0
  }

  return {
    // 状态
    friendLinks,
    activeFriendLinks,
    loading,
    pagination,
    filters,
    
    // 计算属性
    totalCount,
    hasData,
    
    // 方法
    fetchActiveFriendLinks,
    fetchFriendLinks,
    createFriendLink,
    updateFriendLink,
    deleteFriendLink,
    setPagination,
    setFilters,
    resetFilters,
    clearFriendLinks
  }
})
