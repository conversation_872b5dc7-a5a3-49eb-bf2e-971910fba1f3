import { defineStore } from 'pinia'
import { timelinesAPI } from '../api/timelines'
import { message } from 'ant-design-vue'

export const useTimelinesStore = defineStore('timelines', {
  state: () => ({
    // 时间线列表
    timelines: [],
    // 当前时间线
    currentTimeline: null,
    // 实体的时间线列表
    entityTimelines: [],
    // 加载状态
    loading: false,
    // 分页信息
    pagination: {
      page: 1,
      size: 10,
      total: 0
    },
    // 筛选条件
    filters: {
      entity_id: null,
      search: '',
      start_date: null,
      end_date: null
    }
  }),

  getters: {
    // 根据ID获取时间线
    getTimelineById: (state) => (id) => 
      state.timelines.find(timeline => timeline.id === id),
    
    // 是否有时间线数据
    hasTimelines: (state) => state.timelines.length > 0,
    
    // 是否有实体时间线数据
    hasEntityTimelines: (state) => state.entityTimelines.length > 0,
    
    // 按日期排序的时间线
    sortedTimelines: (state) => 
      [...state.timelines].sort((a, b) => new Date(b.event_date) - new Date(a.event_date)),
    
    // 按日期排序的实体时间线
    sortedEntityTimelines: (state) => 
      [...state.entityTimelines].sort((a, b) => new Date(b.event_date) - new Date(a.event_date))
  },

  actions: {
    // 获取时间线列表
    async fetchTimelines(params = {}) {
      this.loading = true
      try {
        const requestParams = {
          page: params.page || this.pagination.page,
          size: params.size || this.pagination.size,
          ...this.filters,
          ...params
        }

        const response = await timelinesAPI.getTimelines(requestParams)

        this.timelines = response.items || response
        if (response.total !== undefined) {
          this.pagination.total = response.total
          this.pagination.page = requestParams.page
          this.pagination.size = requestParams.size
        }

        return response
      } catch (error) {
        message.error('获取时间线列表失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 根据实体ID获取时间线
    async fetchTimelinesByEntity(entityId, params = {}) {
      this.loading = true
      try {
        const requestParams = {
          page: params.page || this.pagination.page,
          size: params.size || this.pagination.size,
          ...params
        }

        const response = await timelinesAPI.getTimelinesByEntity(entityId, requestParams)

        console.log('📊 时间线API响应:', response);
        this.entityTimelines = response.items || response
        console.log('📊 设置entityTimelines:', this.entityTimelines);
        if (response.total !== undefined) {
          this.pagination.total = response.total
          this.pagination.page = requestParams.page
          this.pagination.size = requestParams.size
        }

        return response
      } catch (error) {
        message.error('获取实体时间线失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取时间线详情
    async fetchTimeline(id) {
      this.loading = true
      try {
        const timeline = await timelinesAPI.getTimeline(id)
        this.currentTimeline = timeline
        return timeline
      } catch (error) {
        message.error('获取时间线详情失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 创建时间线
    async createTimeline(data) {
      this.loading = true
      try {
        const timeline = await timelinesAPI.createTimeline(data)
        this.timelines.unshift(timeline)
        this.entityTimelines.unshift(timeline)
        this.pagination.total += 1
        message.success('创建时间线成功')
        return timeline
      } catch (error) {
        message.error('创建时间线失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 更新时间线
    async updateTimeline(id, data) {
      this.loading = true
      try {
        const timeline = await timelinesAPI.updateTimeline(id, data)
        
        // 更新时间线列表
        const index = this.timelines.findIndex(t => t.id === id)
        if (index !== -1) {
          this.timelines[index] = timeline
        }
        
        // 更新实体时间线列表
        const entityIndex = this.entityTimelines.findIndex(t => t.id === id)
        if (entityIndex !== -1) {
          this.entityTimelines[entityIndex] = timeline
        }
        
        // 更新当前时间线
        if (this.currentTimeline?.id === id) {
          this.currentTimeline = timeline
        }
        return timeline
      } catch (error) {
        message.error('更新时间线失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 删除时间线
    async deleteTimeline(id) {
      this.loading = true
      try {
        await timelinesAPI.deleteTimeline(id)
        
        // 从时间线列表中移除
        this.timelines = this.timelines.filter(t => t.id !== id)
        this.entityTimelines = this.entityTimelines.filter(t => t.id !== id)
        this.pagination.total -= 1
        
        // 清除当前时间线
        if (this.currentTimeline?.id === id) {
          this.currentTimeline = null
        }
        
        message.success('删除时间线成功')
      } catch (error) {
        message.error('删除时间线失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取时间线统计
    async fetchTimelineStats(params = {}) {
      try {
        const stats = await timelinesAPI.getTimelineStats(params)
        return stats
      } catch (error) {
        message.error('获取时间线统计失败')
        throw error
      }
    },

    // 设置筛选条件
    setFilters(filters) {
      this.filters = { ...this.filters, ...filters }
    },

    // 设置分页
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 清除实体时间线
    clearEntityTimelines() {
      this.entityTimelines = []
    },

    // 清空实体时间线数据
    clearEntityTimelines() {
      this.entityTimelines = []
    },

    // 重置状态
    resetState() {
      this.timelines = []
      this.currentTimeline = null
      this.entityTimelines = []
      this.loading = false
      this.pagination = {
        page: 1,
        size: 10,
        total: 0
      }
      this.filters = {
        entity_id: null,
        search: '',
        start_date: null,
        end_date: null
      }
    }
  }
})
