import { defineStore } from 'pinia'
import { authAPI } from '../api/auth'
import { message } from 'ant-design-vue'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    // 用户信息
    user: null,
    // 认证token
    token: localStorage.getItem('admin_token') || null,
    // 刷新token
    refreshToken: localStorage.getItem('admin_refresh_token') || null,
    // 登录状态
    isAuthenticated: false,
    // 加载状态
    loading: false
  }),

  getters: {
    // 是否已登录
    isLoggedIn: (state) => !!state.token && !!state.user,
    // 用户名
    username: (state) => state.user?.username || '',
    // 用户角色
    userRole: (state) => state.user?.role || 'guest'
  },

  actions: {
    // 登录
    async login(credentials) {
      this.loading = true
      try {
        const response = await authAPI.login(credentials)

        // 保存token
        this.token = response.access_token
        this.refreshToken = response.refresh_token

        // 保存到localStorage
        localStorage.setItem('admin_token', this.token)
        localStorage.setItem('admin_refresh_token', this.refreshToken)

        // 获取用户信息
        await this.getCurrentUser()

        return response
      } catch (error) {
        message.error('登录失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    // 登出
    async logout() {
      try {
        if (this.token) {
          await authAPI.logout()
        }
      } catch (error) {
        console.error('登出请求失败:', error)
      } finally {
        // 清除状态
        this.user = null
        this.token = null
        this.refreshToken = null
        this.isAuthenticated = false
        
        // 清除localStorage
        localStorage.removeItem('admin_token')
        localStorage.removeItem('admin_refresh_token')
        
        message.success('已退出登录')
      }
    },

    // 获取当前用户信息
    async getCurrentUser() {
      if (!this.token) return null
      
      try {
        const user = await authAPI.getCurrentUser()
        this.user = user
        this.isAuthenticated = true
        return user
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 如果token无效，清除认证状态
        this.logout()
        return null
      }
    },

    // 刷新token
    async refreshAccessToken() {
      if (!this.refreshToken) {
        this.logout()
        return false
      }

      try {
        const response = await authAPI.refreshToken(this.refreshToken)
        this.token = response.access_token
        localStorage.setItem('admin_token', this.token)
        return true
      } catch (error) {
        console.error('刷新token失败:', error)
        this.logout()
        return false
      }
    },

    // 初始化认证状态
    async initAuth() {
      const token = localStorage.getItem('admin_token')
      const refreshToken = localStorage.getItem('admin_refresh_token')

      if (token) {
        this.token = token
        this.refreshToken = refreshToken

        try {
          // 尝试获取用户信息验证token有效性
          await this.getCurrentUser()
          this.isAuthenticated = true
        } catch (error) {
          console.error('Token验证失败，清除认证状态:', error)
          // Token无效，清除认证状态
          this.logout()
        }
      }
    }
  }
})
