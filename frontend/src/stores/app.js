import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 应用标题
    title: import.meta.env.VITE_APP_TITLE || '数字生命馆',
    // 当前页面标题
    pageTitle: '',
    // 全局加载状态
    globalLoading: false,
    // 侧边栏折叠状态（管理后台）
    sidebarCollapsed: false,
    // 移动端检测
    isMobile: false,
    // 主题设置
    theme: {
      primaryColor: '#ff4d4f',
      mode: 'light' // light | dark
    },
    // 面包屑导航
    breadcrumbs: [],
    // 全局配置
    config: {
      // API基础URL
      apiBaseUrl: import.meta.env.VITE_API_BASE_URL || '/api',
      // 文件上传大小限制（MB）
      maxFileSize: 10,
      // 支持的文件类型
      allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'application/pdf'],
      // 分页默认大小
      defaultPageSize: 12
    }
  }),

  getters: {
    // 完整的页面标题
    fullTitle: (state) => {
      return state.pageTitle ? `${state.pageTitle} - ${state.title}` : state.title
    },
    
    // 是否为暗色主题
    isDarkMode: (state) => state.theme.mode === 'dark',
    
    // 当前设备类型
    deviceType: (state) => state.isMobile ? 'mobile' : 'desktop'
  },

  actions: {
    // 设置页面标题
    setPageTitle(title) {
      this.pageTitle = title
      // 同时更新document.title
      document.title = this.fullTitle
    },

    // 设置全局加载状态
    setGlobalLoading(loading) {
      this.globalLoading = loading
    },

    // 切换侧边栏折叠状态
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },

    // 设置侧边栏状态
    setSidebarCollapsed(collapsed) {
      this.sidebarCollapsed = collapsed
    },

    // 检测移动端
    checkMobile() {
      this.isMobile = window.innerWidth <= 768
    },

    // 切换主题模式
    toggleTheme() {
      this.theme.mode = this.theme.mode === 'light' ? 'dark' : 'light'
      this.applyThemeToDOM()
      this.saveThemeToStorage()
    },

    // 设置主题
    setTheme(theme) {
      this.theme = { ...this.theme, ...theme }
      this.applyThemeToDOM()
      this.saveThemeToStorage()
    },

    // 应用主题到DOM
    applyThemeToDOM() {
      const html = document.documentElement
      const body = document.body

      // 设置data-theme属性
      html.setAttribute('data-theme', this.theme.mode)
      body.setAttribute('data-theme', this.theme.mode)

      // 更新meta theme-color
      let themeColorMeta = document.querySelector('meta[name="theme-color"]')
      if (!themeColorMeta) {
        themeColorMeta = document.createElement('meta')
        themeColorMeta.name = 'theme-color'
        document.head.appendChild(themeColorMeta)
      }

      // 根据主题设置颜色
      const themeColor = this.theme.mode === 'dark' ? '#1f1f1f' : '#ffffff'
      themeColorMeta.content = themeColor
    },

    // 保存主题到本地存储
    saveThemeToStorage() {
      localStorage.setItem('app_theme', JSON.stringify(this.theme))
    },

    // 从本地存储加载主题
    loadThemeFromStorage() {
      const savedTheme = localStorage.getItem('app_theme')
      if (savedTheme) {
        try {
          this.theme = { ...this.theme, ...JSON.parse(savedTheme) }
        } catch (error) {
          console.error('加载主题设置失败:', error)
        }
      }
      // 应用主题到DOM
      this.applyThemeToDOM()
    },

    // 设置面包屑导航
    setBreadcrumbs(breadcrumbs) {
      this.breadcrumbs = breadcrumbs
    },

    // 添加面包屑项
    addBreadcrumb(breadcrumb) {
      this.breadcrumbs.push(breadcrumb)
    },

    // 清除面包屑
    clearBreadcrumbs() {
      this.breadcrumbs = []
    },

    // 更新配置
    updateConfig(config) {
      this.config = { ...this.config, ...config }
    },

    // 初始化应用
    initApp() {
      // 加载主题设置
      this.loadThemeFromStorage()
      
      // 检测移动端
      this.checkMobile()
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        this.checkMobile()
      })
      
      // 设置初始页面标题
      document.title = this.fullTitle
    }
  }
})
