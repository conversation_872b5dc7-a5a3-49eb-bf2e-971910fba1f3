import request from './request'

// 生命实体相关API
export const entitiesAPI = {
  // 获取实体列表
  getEntities(params = {}) {
    return request.get('/life-entities/', { params })
  },

  // 获取实体详情
  getEntity(id) {
    return request.get(`/life-entities/${id}`)
  },

  // 创建实体（需要管理员权限）
  createEntity(data) {
    return request.post('/life-entities/', data)
  },

  // 更新实体（需要管理员权限）
  updateEntity(id, data) {
    return request.put(`/life-entities/${id}`, data)
  },

  // 删除实体（需要管理员权限）
  deleteEntity(id) {
    return request.delete(`/life-entities/${id}`)
  },

  // 用户互动 - 点赞
  likeEntity(id, data) {
    return request.post(`/life-entities/${id}/like`, data)
  },

  // 用户互动 - 送花
  flowerEntity(id, data) {
    return request.post(`/life-entities/${id}/flower`, data)
  },

  // 搜索实体
  searchEntities(params = {}) {
    return request.get('/life-entities/', { params })
  }
}

export default entitiesAPI
