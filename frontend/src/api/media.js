import request from './request'

// 媒体文件相关API
export const mediaAPI = {
  // 获取媒体文件列表
  getMediaFiles(params = {}) {
    return request.get('/media/', { params })
  },

  // 获取媒体文件详情
  getMediaFile(id) {
    return request.get(`/media/${id}`)
  },

  // 上传文件（需要管理员权限）
  uploadFile(formData, onUploadProgress) {
    return request.post('/media/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress
    })
  },

  // 更新媒体文件信息（需要管理员权限）
  updateMediaFile(id, data) {
    return request.put(`/media/${id}`, data)
  },

  // 删除媒体文件（需要管理员权限）
  deleteMediaFile(id) {
    return request.delete(`/media/${id}`)
  },

  // 下载文件
  downloadFile(id) {
    return request.get(`/media/${id}/download`, {
      responseType: 'blob'
    })
  },

  // 获取媒体统计
  getMediaStats() {
    return request.get('/media/stats/summary')
  },

  // 批量删除媒体文件（需要管理员权限）
  batchDeleteFiles(ids) {
    return request.post('/media/batch-delete', { ids })
  }
}

export default mediaAPI
