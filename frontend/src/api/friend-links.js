import request from './request'

// 友情链接API服务
export const friendLinksAPI = {
  // 获取启用的友情链接（公开接口）
  getActiveFriendLinks() {
    return request.get('/friend-links/active')
  },

  // 获取友情链接列表（管理员）
  getFriendLinks(params = {}) {
    return request.get('/friend-links/', { params })
  },

  // 获取友情链接详情（管理员）
  getFriendLink(id) {
    return request.get(`/friend-links/${id}`)
  },

  // 创建友情链接（管理员）
  createFriendLink(data) {
    return request.post('/friend-links/', data)
  },

  // 更新友情链接（管理员）
  updateFriendLink(id, data) {
    return request.put(`/friend-links/${id}`, data)
  },

  // 删除友情链接（管理员）
  deleteFriendLink(id) {
    return request.delete(`/friend-links/${id}`)
  },

  // 获取友情链接统计（管理员）
  getFriendLinksStats() {
    return request.get('/friend-links/stats')
  }
}

export default friendLinksAPI
