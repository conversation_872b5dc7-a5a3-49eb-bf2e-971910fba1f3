/**
 * AI时间线相关API
 */
import request from './request'

/**
 * 测试AI时间线接口
 */
export const testAITimeline = () => {
  return request({
    url: '/ai-timeline/test',
    method: 'GET'
  })
}

/**
 * AI时间线批量创建
 * @param {Object} data - 创建数据
 * @param {Object} data.content - AI返回的内容
 * @param {Array} data.content.output - 时间线事件列表
 * @param {string} data.content.things - 实体名称
 * @param {number} data.category_id - 分类ID
 * @param {string} data.entity_type - 实体类型
 */
export const createAITimeline = (data) => {
  return request({
    url: '/ai-timeline/batch-create',
    method: 'POST',
    data
  })
}

/**
 * 获取工作流配置
 * @param {number} categoryId - 分类ID
 * @returns {Object} 工作流配置对象
 */
const getWorkflowConfig = (categoryId) => {
  // 如果分类ID为4（新闻），使用新闻工作流
  if (categoryId === 4) {
    return {
      token: 'cztei_lazG84nvl6K0G64Cgnk3iu23diMOBA4nckiXmutjjguXSkXVS1RWl9AdH6RqDhgO5',
      workflowId: '7533516378521255951',
      baseURL: 'https://api.coze.cn'
    }
  }

  // 默认工作流配置
  return {
    token: 'pat_R9XGKxgBswwl4vNYlXGZfvPuPbBivrKntHoubzjbWoJaRf2hL0yxxAJiGI7d3hs5',
    workflowId: '7532050475337678900',
    baseURL: 'https://api.coze.cn'
  }
}

/**
 * 调用Coze工作流生成时间线
 * @param {string} entityName - 实体名称
 * @param {number} categoryId - 分类ID（可选，默认使用默认工作流）
 */
export const generateTimelineWithCoze = async (entityName, categoryId = null) => {
  try {
    const { CozeAPI } = await import('@coze/api')
    const config = getWorkflowConfig(categoryId)

    const apiClient = new CozeAPI({
      token: config.token,
      baseURL: config.baseURL,
      allowPersonalAccessTokenInBrowser: true
    })

    const response = await apiClient.workflows.runs.create({
      workflow_id: config.workflowId,
      parameters: {
        input: entityName
      }
    })

    return response
  } catch (error) {
    console.error('调用Coze API失败:', error)
    throw error
  }
}

/**
 * 处理Coze流式响应
 * @param {string} entityName - 实体名称
 * @param {number} categoryId - 分类ID（用于选择工作流）
 * @param {Function} onProgress - 进度回调
 * @param {Function} onComplete - 完成回调
 * @param {Function} onError - 错误回调
 */
export const generateTimelineStream = async (entityName, categoryId, onProgress, onComplete, onError) => {
  try {
    const { CozeAPI } = await import('@coze/api')
    const config = getWorkflowConfig(categoryId)

    const apiClient = new CozeAPI({
      token: config.token,
      baseURL: config.baseURL,
      allowPersonalAccessTokenInBrowser: true
    })

    if (onProgress) {
      const workflowType = categoryId === 4 ? '新闻工作流' : '默认工作流'
      onProgress(`正在启动AI工作流（${workflowType}）...`)
    }

    // 创建工作流运行
    const runResponse = await apiClient.workflows.runs.create({
      workflow_id: config.workflowId,
      parameters: {
        input: entityName
      }
    })

    if (onProgress) {
      onProgress('工作流已启动，正在生成中...')
    }

    // 检查响应格式
    console.log('Coze API 响应:', runResponse)

    // 如果直接返回了结果（同步执行）
    if (runResponse.code === 0 && runResponse.data) {
      if (onProgress) {
        onProgress('AI生成完成，正在解析结果...')
      }

      try {
        // 解析返回的JSON字符串
        const result = JSON.parse(runResponse.data)
        if (onComplete) {
          onComplete(result)
        }
        return
      } catch (parseError) {
        console.error('解析AI结果失败:', parseError)
        throw new Error('AI返回结果格式不正确')
      }
    }

    // 如果是异步执行，获取运行ID
    const runId = runResponse.data?.id || runResponse.id
    if (!runId) {
      throw new Error('无法获取工作流运行ID，可能是同步执行已完成')
    }

    // 轮询获取结果
    const pollResult = async () => {
      try {
        let attempts = 0
        const maxAttempts = 30 // 最多轮询30次，每次2秒，总共1分钟

        const poll = async () => {
          attempts++

          if (onProgress) {
            onProgress(`正在获取AI生成结果... (${attempts}/${maxAttempts})`)
          }

          try {
            // 获取工作流运行结果
            const resultResponse = await apiClient.workflows.runs.retrieve({
              workflow_id: config.workflowId,
              run_id: runId
            })

            const runData = resultResponse.data

            // 检查运行状态
            if (runData.status === 'completed') {
              // 工作流完成，解析结果
              if (onProgress) {
                onProgress('AI生成完成，正在解析结果...')
              }

              // 从输出中提取结果
              const output = runData.output
              if (output && output.content) {
                const result = JSON.parse(output.content)
                if (onComplete) {
                  onComplete(result)
                }
                return
              } else {
                throw new Error('AI返回结果格式不正确')
              }
            } else if (runData.status === 'failed') {
              throw new Error('AI工作流执行失败: ' + (runData.error_message || '未知错误'))
            } else if (runData.status === 'running' || runData.status === 'pending') {
              // 继续轮询
              if (attempts < maxAttempts) {
                setTimeout(poll, 2000) // 2秒后再次轮询
              } else {
                throw new Error('AI生成超时，请稍后重试')
              }
            } else {
              throw new Error('未知的工作流状态: ' + runData.status)
            }
          } catch (error) {
            if (attempts < maxAttempts && error.message.includes('timeout')) {
              // 网络超时，继续重试
              setTimeout(poll, 2000)
            } else {
              throw error
            }
          }
        }

        // 开始轮询
        poll()

      } catch (error) {
        if (onError) {
          onError(error)
        }
      }
    }

    // 开始轮询结果
    pollResult()

  } catch (error) {
    console.error('生成时间线失败:', error)
    if (onError) {
      onError(error)
    }
  }
}
