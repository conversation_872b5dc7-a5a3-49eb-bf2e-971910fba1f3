import request from './request'

// 时间线相关API
export const timelinesAPI = {
  // 获取时间线列表
  getTimelines(params = {}) {
    return request.get('/timelines/', { params })
  },

  // 根据实体ID获取时间线
  getTimelinesByEntity(entityId, params = {}) {
    return request.get(`/timelines/entity/${entityId}`, { params })
  },

  // 获取时间线详情
  getTimeline(id) {
    return request.get(`/timelines/${id}`)
  },

  // 创建时间线（需要管理员权限）
  createTimeline(data) {
    return request.post('/timelines/', data)
  },

  // 更新时间线（需要管理员权限）
  updateTimeline(id, data) {
    return request.put(`/timelines/${id}`, data)
  },

  // 删除时间线（需要管理员权限）
  deleteTimeline(id) {
    return request.delete(`/timelines/${id}`)
  },

  // 获取时间线统计
  getTimelineStats(params = {}) {
    return request.get('/timelines/stats', { params })
  }
}

export default timelinesAPI
