import request from './request'

// 用户行为统计相关API
export const statisticsAPI = {
  // 获取用户行为列表（管理员）
  getUserActions(params = {}) {
    return request.get('/user-actions/', { params })
  },

  // 获取实体统计
  getEntityStats(entityId) {
    return request.get(`/user-actions/entity/${entityId}/stats`)
  },

  // 获取全局统计
  getGlobalStats() {
    return request.get('/user-actions/stats')
  },

  // 获取行为趋势
  getActionTrends(params = {}) {
    return request.get('/user-actions/trends', { params })
  },

  // 获取访客统计
  getVisitorStats(params = {}) {
    return request.get('/user-actions/visitors', { params })
  },

  // 获取热门内容
  getPopularContent(params = {}) {
    return request.get('/user-actions/popular', { params })
  },

  // 获取仪表盘综合统计数据
  getDashboardStats() {
    return request.get('/user-actions/stats/dashboard')
  }
}

export default statisticsAPI
