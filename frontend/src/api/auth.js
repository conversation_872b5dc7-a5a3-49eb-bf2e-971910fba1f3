import request from './request'

// 认证相关API
export const authAPI = {
  // 管理员登录
  login(credentials) {
    return request.post('/auth/login', credentials)
  },

  // 刷新token
  refreshToken(refreshToken) {
    return request.post('/auth/refresh', { refresh_token: refreshToken })
  },

  // 获取当前用户信息
  getCurrentUser() {
    return request.get('/auth/me')
  },

  // 登出
  logout() {
    return request.post('/auth/logout')
  }
}

export default authAPI
