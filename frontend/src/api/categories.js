import request from './request'

// 分类相关API
export const categoriesAPI = {
  // 获取分类列表
  getCategories(params = {}) {
    return request.get('/categories/', { params })
  },

  // 获取分类详情
  getCategory(id) {
    return request.get(`/categories/${id}`)
  },

  // 创建分类（需要管理员权限）
  createCategory(data) {
    return request.post('/categories/', data)
  },

  // 更新分类（需要管理员权限）
  updateCategory(id, data) {
    return request.put(`/categories/${id}`, data)
  },

  // 删除分类（需要管理员权限）
  deleteCategory(id) {
    return request.delete(`/categories/${id}`)
  }
}

export default categoriesAPI
