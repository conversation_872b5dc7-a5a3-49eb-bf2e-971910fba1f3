// API模块统一导出
export { default as request } from './request'
export { authAPI } from './auth'
export { categoriesAPI } from './categories'
export { entitiesAPI } from './entities'
export { timelinesAPI } from './timelines'
export { statisticsAPI } from './statistics'
export { statisticsAPI as userActionsAPI } from './statistics'
export { mediaAPI } from './media'
export { aboutAPI } from './about'
export { friendLinksAPI } from './friend-links'

// 导入所有API模块
import { authAPI } from './auth'
import { categoriesAPI } from './categories'
import { entitiesAPI } from './entities'
import { timelinesAPI } from './timelines'
import { statisticsAPI } from './statistics'
import { mediaAPI } from './media'
import { aboutAPI } from './about'
import { friendLinksAPI } from './friend-links'

// 统一的API对象
export const API = {
  auth: authAPI,
  categories: categoriesAPI,
  entities: entitiesAPI,
  timelines: timelinesAPI,
  statistics: statisticsAPI,
  media: mediaAPI,
  about: aboutAPI,
  friendLinks: friendLinksAPI
}

export default API
