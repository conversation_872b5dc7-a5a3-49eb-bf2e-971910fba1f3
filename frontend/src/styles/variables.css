/* 数字生命馆 - 全局设计变量 */
/* 基于Home.vue的设计风格制定统一的设计系统 */
/* 支持白天/黑夜主题切换 */

:root {
  /* ========== 色彩系统 ========== */
  /* 主题色 */
  --primary-color: #ff4d4f;
  --primary-hover: #ff7875;
  --primary-light: #ffa39e;
  --primary-bg: rgba(255, 77, 79, 0.1);
  
  /* 中性色 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --text-disabled: #bfbfbf;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;
  --bg-quaternary: #fafbfc;
  
  /* 边框色 */
  --border-primary: #f0f0f0;
  --border-secondary: #e8e8e8;
  --border-tertiary: #d9d9d9;
  
  /* 状态色 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --info-color: #1890ff;
  
  /* ========== 渐变系统 ========== */
  /* 主题渐变 */
  --gradient-primary: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  --gradient-primary-light: linear-gradient(135deg, rgba(255, 77, 79, 0.8) 0%, rgba(255, 120, 117, 0.8) 100%);
  
  /* 背景渐变 */
  --gradient-bg-primary: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  --gradient-bg-secondary: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  --gradient-bg-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* 装饰渐变 */
  --gradient-decoration: radial-gradient(circle, rgba(255, 77, 79, 0.03) 0%, transparent 70%);
  --gradient-shimmer: linear-gradient(90deg, #ff4d4f 0%, #ff7875 50%, #ffa39e 100%);
  
  /* ========== 圆角系统 ========== */
  --radius-xs: 4px;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-full: 50%;
  
  /* ========== 阴影系统 ========== */
  /* 基础阴影 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.04);
  --shadow-sm: 0 1px 4px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 8px 32px rgba(0, 0, 0, 0.16);
  
  /* 主题阴影 */
  --shadow-primary: 0 4px 16px rgba(255, 77, 79, 0.12);
  --shadow-primary-lg: 0 8px 32px rgba(255, 77, 79, 0.12);
  
  /* 特殊阴影 */
  --shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.04);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-card-hover: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05);
  
  /* ========== 字体系统 ========== */
  /* 字体大小 */
  --font-xs: 11px;
  --font-sm: 12px;
  --font-base: 14px;
  --font-lg: 16px;
  --font-xl: 18px;
  --font-2xl: 20px;
  --font-3xl: 24px;
  --font-4xl: 32px;
  
  /* 字体权重 */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.5;
  --line-height-loose: 1.6;
  
  /* ========== 间距系统 ========== */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 20px;
  --space-2xl: 24px;
  --space-3xl: 32px;
  --space-4xl: 40px;
  --space-5xl: 48px;
  
  /* ========== 动画系统 ========== */
  /* 过渡时间 */
  --transition-fast: 0.15s;
  --transition-normal: 0.2s;
  --transition-slow: 0.3s;
  
  /* 缓动函数 */
  --ease-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 常用过渡 */
  --transition-all: all var(--transition-normal) var(--ease-out);
  --transition-colors: color var(--transition-normal) var(--ease-out), 
                       background-color var(--transition-normal) var(--ease-out), 
                       border-color var(--transition-normal) var(--ease-out);
  --transition-transform: transform var(--transition-normal) var(--ease-out);
  
  /* ========== Z-index系统 ========== */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* ========== 布局系统 ========== */
  /* 容器宽度 */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* 响应式断点 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* 头部高度 */
  --header-height: 64px;
  --header-height-mobile: 56px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 80px;
  --sidebar-mobile-width: 280px;

  /* 移动端专用尺寸 */
  --mobile-padding: 16px;
  --mobile-margin: 12px;
  --mobile-button-height: 44px;
  --mobile-input-height: 40px;
  --mobile-card-padding: 16px;
  --mobile-modal-padding: 20px;
}

/* ========== 全局重置和基础样式 ========== */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  line-height: var(--line-height-normal);
}

/* ========== 通用工具类 ========== */
.gradient-bg {
  background: var(--gradient-bg-primary);
}

.gradient-primary {
  background: var(--gradient-primary);
}

.shadow-card {
  box-shadow: var(--shadow-card);
}

.shadow-card-hover {
  box-shadow: var(--shadow-card-hover);
}

.transition-all {
  transition: var(--transition-all);
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.bg-primary {
  background-color: var(--bg-primary);
}

.bg-secondary {
  background-color: var(--bg-secondary);
}

.border-primary {
  border-color: var(--border-primary);
}

.rounded-md {
  border-radius: var(--radius-md);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-xl {
  border-radius: var(--radius-xl);
}

/* ========== 响应式工具类 ========== */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

.mobile-padding {
  padding: var(--mobile-padding);
}

.mobile-margin {
  margin: var(--mobile-margin);
}

.mobile-button {
  height: var(--mobile-button-height);
  min-height: var(--mobile-button-height);
}

.mobile-input {
  height: var(--mobile-input-height);
}

.mobile-card {
  padding: var(--mobile-card-padding);
}

/* 响应式显示工具类 */
@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }

  .desktop-only {
    display: none;
  }

  .mobile-hidden {
    display: none !important;
  }

  .mobile-flex {
    display: flex !important;
  }

  .mobile-block {
    display: block !important;
  }
}

/* 触摸友好设计 */
@media (hover: none) and (pointer: coarse) {
  .touch-friendly {
    min-height: 44px;
    min-width: 44px;
  }

  .touch-button {
    padding: 12px 16px;
    min-height: 44px;
  }
}

/* ========== 暗色主题变量 ========== */
[data-theme="dark"] {
  /* ========== 色彩系统 - 暗色主题 ========== */
  /* 主题色保持不变 */
  --primary-color: #ff4d4f;
  --primary-hover: #ff7875;
  --primary-light: #ffa39e;
  --primary-bg: rgba(255, 77, 79, 0.15);

  /* 中性色 - 暗色版本 */
  --text-primary: #ffffff;
  --text-secondary: #d9d9d9;
  --text-tertiary: #8c8c8c;
  --text-disabled: #595959;

  /* 背景色 - 暗色版本 */
  --bg-primary: #1f1f1f;
  --bg-secondary: #141414;
  --bg-tertiary: #262626;
  --bg-quaternary: #1a1a1a;

  /* 边框色 - 暗色版本 */
  --border-primary: #303030;
  --border-secondary: #434343;
  --border-tertiary: #595959;

  /* 状态色保持不变 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --info-color: #1890ff;

  /* ========== 渐变系统 - 暗色主题 ========== */
  /* 主题渐变保持红色 */
  --gradient-primary: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  --gradient-primary-light: linear-gradient(135deg, rgba(255, 77, 79, 0.6) 0%, rgba(255, 120, 117, 0.6) 100%);

  /* 背景渐变 - 暗色版本 */
  --gradient-bg-primary: linear-gradient(135deg, #1f1f1f 0%, #1a1a1a 100%);
  --gradient-bg-secondary: linear-gradient(135deg, #262626 0%, #1f1f1f 100%);
  --gradient-bg-hero: linear-gradient(135deg, #2d1b69 0%, #11101d 100%);

  /* 装饰渐变 - 暗色版本 */
  --gradient-decoration: radial-gradient(circle, rgba(255, 77, 79, 0.05) 0%, transparent 70%);
  --gradient-shimmer: linear-gradient(90deg, #ff4d4f 0%, #ff7875 50%, #ffa39e 100%);

  /* ========== 阴影系统 - 暗色主题 ========== */
  /* 基础阴影 - 更深的阴影 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 8px 32px rgba(0, 0, 0, 0.6);

  /* 主题阴影 */
  --shadow-primary: 0 4px 16px rgba(255, 77, 79, 0.2);
  --shadow-primary-lg: 0 8px 32px rgba(255, 77, 79, 0.2);

  /* 特殊阴影 */
  --shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.4);
  --shadow-card-hover: 0 4px 20px rgba(0, 0, 0, 0.4), 0 1px 3px rgba(0, 0, 0, 0.4);
}

/* ========== 主题切换动画 ========== */
* {
  transition: background-color var(--transition-normal) var(--ease-out),
              color var(--transition-normal) var(--ease-out),
              border-color var(--transition-normal) var(--ease-out),
              box-shadow var(--transition-normal) var(--ease-out);
}
