<template>
  <div class="media-selector">
    <!-- 当前选中的媒体预览 -->
    <div v-if="currentValue" class="current-media">
      <div class="media-preview">
        <a-image
          v-if="isImage(currentValue)"
          :src="currentValue"
          :width="previewSize.width"
          :height="previewSize.height"
          style="border-radius: 6px; object-fit: cover"
          :preview="{ src: currentValue }"
        />
        <div v-else-if="isVideo(currentValue)" class="video-preview">
          <video
            :src="currentValue"
            :width="previewSize.width"
            :height="previewSize.height"
            style="border-radius: 6px; object-fit: cover"
            controls
          />
        </div>
        <div v-else class="url-preview">
          <LinkOutlined style="font-size: 24px; color: #1890ff" />
          <div class="url-text">{{ currentValue }}</div>
        </div>
      </div>
      <div class="media-actions">
        <a-button size="small" @click="handleChange">
          <template #icon><EditOutlined /></template>
          更换
        </a-button>
        <a-button size="small" danger @click="handleClear">
          <template #icon><DeleteOutlined /></template>
          清除
        </a-button>
      </div>
    </div>

    <!-- 选择媒体按钮 -->
    <div v-else class="select-trigger">
      <a-button type="dashed" block @click="handleChange" class="select-button">
        <template #icon><PlusOutlined /></template>
        {{ placeholder || '选择媒体文件' }}
      </a-button>
    </div>

    <!-- 媒体选择模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="900px"
      :footer="null"
      @cancel="handleModalCancel"
    >
      <div class="media-selector-modal">
        <!-- 选择方式切换 -->
        <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
          <a-tab-pane key="upload" tab="上传文件">
            <div class="upload-section">
              <a-upload-dragger
                v-model:file-list="fileList"
                :before-upload="beforeUpload"
                :multiple="false"
                :show-upload-list="true"
                :accept="acceptTypes"
                @change="handleUploadChange"
              >
                <p class="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p class="ant-upload-hint">
                  {{ uploadHint }}
                </p>
              </a-upload-dragger>

              <div v-if="uploadForm.category" class="upload-options">
                <a-form layout="vertical">
                  <a-form-item label="文件分类">
                    <a-select
                      v-model:value="uploadForm.category"
                      placeholder="请选择文件分类"
                    >
                      <a-select-option value="avatar">头像</a-select-option>
                      <a-select-option value="timeline_image">时间线图片</a-select-option>
                      <a-select-option value="timeline_video">时间线视频</a-select-option>
                      <a-select-option value="general">通用文件</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="文件描述">
                    <a-textarea
                      v-model:value="uploadForm.description"
                      placeholder="请输入文件描述（可选）"
                      :rows="2"
                      :maxlength="200"
                      show-count
                    />
                  </a-form-item>
                </a-form>
              </div>

              <div class="upload-actions">
                <a-button
                  type="primary"
                  @click="handleUploadSubmit"
                  :loading="uploadLoading"
                  :disabled="fileList.length === 0"
                >
                  <template #icon><UploadOutlined /></template>
                  上传并选择
                </a-button>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane key="url" tab="输入URL">
            <div class="url-section">
              <a-form layout="vertical">
                <a-form-item label="媒体文件URL">
                  <a-input
                    v-model:value="urlForm.url"
                    placeholder="请输入图片或视频的URL地址"
                    @pressEnter="handleUrlSubmit"
                  >
                    <template #prefix>
                      <LinkOutlined style="color: #bfbfbf" />
                    </template>
                  </a-input>
                </a-form-item>
              </a-form>

              <!-- URL预览 -->
              <div v-if="urlForm.url && isValidUrl(urlForm.url)" class="url-preview-section">
                <div class="preview-title">预览：</div>
                <div class="preview-content">
                  <a-image
                    v-if="isImage(urlForm.url)"
                    :src="urlForm.url"
                    :width="200"
                    :height="150"
                    style="border-radius: 6px; object-fit: cover"
                    :preview="{ src: urlForm.url }"
                  />
                  <div v-else-if="isVideo(urlForm.url)" class="video-preview">
                    <video
                      :src="urlForm.url"
                      width="200"
                      height="150"
                      style="border-radius: 6px; object-fit: cover"
                      controls
                    />
                  </div>
                  <div v-else class="url-display">
                    <LinkOutlined style="font-size: 24px; color: #1890ff" />
                    <div class="url-text">{{ urlForm.url }}</div>
                  </div>
                </div>
              </div>

              <div class="url-actions">
                <a-button
                  type="primary"
                  @click="handleUrlSubmit"
                  :disabled="!urlForm.url || !isValidUrl(urlForm.url)"
                >
                  <template #icon><CheckOutlined /></template>
                  确认选择
                </a-button>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane key="library" tab="从媒体库选择">
            <div class="library-section">
              <!-- 搜索筛选 -->
              <div class="library-search">
                <a-row :gutter="16" align="middle">
                  <a-col :span="12">
                    <a-input
                      v-model:value="librarySearch.search"
                      placeholder="搜索文件名"
                      allow-clear
                      @pressEnter="loadLibraryFiles"
                    >
                      <template #prefix>
                        <SearchOutlined style="color: #bfbfbf" />
                      </template>
                    </a-input>
                  </a-col>
                  <a-col :span="8">
                    <a-select
                      v-model:value="librarySearch.media_type"
                      placeholder="文件类型"
                      allow-clear
                      @change="loadLibraryFiles"
                    >
                      <a-select-option value="image">图片</a-select-option>
                      <a-select-option value="video">视频</a-select-option>
                      <a-select-option value="document">文档</a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="4">
                    <a-button @click="loadLibraryFiles" :loading="libraryLoading">
                      <template #icon><SearchOutlined /></template>
                      搜索
                    </a-button>
                  </a-col>
                </a-row>
              </div>

              <!-- 文件网格 -->
              <div class="library-grid" v-loading="libraryLoading">
                <div
                  v-for="file in libraryFiles"
                  :key="file.id"
                  class="library-item"
                  :class="{ selected: selectedLibraryFile?.id === file.id }"
                  @click="handleLibrarySelect(file)"
                >
                  <div class="library-preview">
                    <a-image
                      v-if="file.media_type === 'image'"
                      :src="file.file_url"
                      :width="120"
                      :height="90"
                      style="border-radius: 4px; object-fit: cover"
                      :preview="false"
                    />
                    <div v-else-if="file.media_type === 'video'" class="video-thumb">
                      <VideoCameraOutlined style="font-size: 32px; color: #fa8c16" />
                    </div>
                    <div v-else class="file-thumb">
                      <FileOutlined style="font-size: 32px; color: #8c8c8c" />
                    </div>
                  </div>
                  <div class="library-info">
                    <div class="file-name">{{ file.original_filename }}</div>
                    <div class="file-size">{{ formatFileSize(file.file_size) }}</div>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div class="library-pagination">
                <a-pagination
                  v-model:current="libraryPagination.current"
                  v-model:page-size="libraryPagination.pageSize"
                  :total="libraryPagination.total"
                  :show-size-changer="false"
                  :show-quick-jumper="false"
                  size="small"
                  @change="loadLibraryFiles"
                />
              </div>

              <div class="library-actions">
                <a-button
                  type="primary"
                  @click="handleLibrarySubmit"
                  :disabled="!selectedLibraryFile"
                >
                  <template #icon><CheckOutlined /></template>
                  选择此文件
                </a-button>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  LinkOutlined,
  InboxOutlined,
  UploadOutlined,
  CheckOutlined,
  SearchOutlined,
  VideoCameraOutlined,
  FileOutlined
} from '@ant-design/icons-vue'
import { mediaAPI } from '../api'

export default {
  name: 'MediaSelector',
  components: {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    LinkOutlined,
    InboxOutlined,
    UploadOutlined,
    CheckOutlined,
    SearchOutlined,
    VideoCameraOutlined,
    FileOutlined
  },
  props: {
    // 当前值
    value: {
      type: String,
      default: ''
    },
    // 占位符文本
    placeholder: {
      type: String,
      default: '选择媒体文件'
    },
    // 媒体类型限制
    mediaType: {
      type: String,
      default: 'all', // all, image, video
      validator: (value) => ['all', 'image', 'video'].includes(value)
    },
    // 预览尺寸
    previewSize: {
      type: Object,
      default: () => ({ width: 120, height: 90 })
    },
    // 默认分类
    defaultCategory: {
      type: String,
      default: 'general'
    }
  },
  emits: ['update:value', 'change'],
  setup(props, { emit }) {
    // 响应式数据
    const modalVisible = ref(false)
    const activeTab = ref('upload')
    const uploadLoading = ref(false)
    const libraryLoading = ref(false)

    const fileList = ref([])
    const selectedLibraryFile = ref(null)

    // 表单数据
    const uploadForm = reactive({
      category: props.defaultCategory,
      description: ''
    })

    const urlForm = reactive({
      url: ''
    })

    const librarySearch = reactive({
      search: '',
      media_type: props.mediaType === 'all' ? undefined : props.mediaType
    })

    const libraryFiles = ref([])
    const libraryPagination = reactive({
      current: 1,
      pageSize: 12,
      total: 0
    })

    // 计算属性
    const currentValue = computed(() => props.value)

    const modalTitle = computed(() => {
      const typeMap = {
        image: '选择图片',
        video: '选择视频',
        all: '选择媒体文件'
      }
      return typeMap[props.mediaType] || '选择媒体文件'
    })

    const acceptTypes = computed(() => {
      switch (props.mediaType) {
        case 'image':
          return 'image/*'
        case 'video':
          return 'video/*'
        default:
          return 'image/*,video/*'
      }
    })

    const uploadHint = computed(() => {
      switch (props.mediaType) {
        case 'image':
          return '支持 JPG、PNG、GIF 等图片格式，单个文件不超过10MB'
        case 'video':
          return '支持 MP4、AVI、MOV 等视频格式，单个文件不超过100MB'
        default:
          return '支持图片、视频等格式，单个文件不超过100MB'
      }
    })

    // 方法
    const isImage = (url) => {
      if (!url) return false
      const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
      const lowerUrl = url.toLowerCase()
      return imageExts.some(ext => lowerUrl.includes(ext)) ||
             lowerUrl.startsWith('data:image/') ||
             url.includes('/image/')
    }

    const isVideo = (url) => {
      if (!url) return false
      const videoExts = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']
      const lowerUrl = url.toLowerCase()
      return videoExts.some(ext => lowerUrl.includes(ext)) ||
             lowerUrl.startsWith('data:video/') ||
             url.includes('/video/')
    }

    const isValidUrl = (url) => {
      if (!url) return false
      try {
        new URL(url)
        return true
      } catch {
        return url.startsWith('/') || url.startsWith('data:')
      }
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const handleChange = () => {
      modalVisible.value = true
      // 重置表单
      urlForm.url = ''
      fileList.value = []
      selectedLibraryFile.value = null
      // 如果有当前值且是URL，预填到URL表单
      if (currentValue.value && !currentValue.value.includes('/uploads/')) {
        activeTab.value = 'url'
        urlForm.url = currentValue.value
      }
    }

    const handleClear = () => {
      emit('update:value', '')
      emit('change', '')
    }

    const handleModalCancel = () => {
      modalVisible.value = false
    }

    const handleTabChange = (key) => {
      if (key === 'library') {
        loadLibraryFiles()
      }
    }

    // 上传相关方法
    const beforeUpload = (file) => {
      // 文件类型验证
      let isValidType = false
      if (props.mediaType === 'image') {
        isValidType = file.type.startsWith('image/')
      } else if (props.mediaType === 'video') {
        isValidType = file.type.startsWith('video/')
      } else {
        isValidType = file.type.startsWith('image/') || file.type.startsWith('video/')
      }

      if (!isValidType) {
        message.error('不支持的文件类型')
        return false
      }

      // 文件大小验证
      const maxSize = props.mediaType === 'video' ? 100 : 10
      const isLtMaxSize = file.size / 1024 / 1024 < maxSize
      if (!isLtMaxSize) {
        message.error(`文件大小不能超过${maxSize}MB`)
        return false
      }

      return false // 阻止自动上传
    }

    const handleUploadChange = (info) => {
      // 文件列表变化处理
    }

    const handleUploadSubmit = async () => {
      if (fileList.value.length === 0) {
        message.error('请选择要上传的文件')
        return
      }

      try {
        uploadLoading.value = true

        const formData = new FormData()
        formData.append('file', fileList.value[0].originFileObj)
        formData.append('category', uploadForm.category)
        if (uploadForm.description) {
          formData.append('description', uploadForm.description)
        }

        const response = await mediaAPI.uploadFile(formData)

        if (response.file && response.file.file_url) {
          const fileUrl = response.file.file_url
          emit('update:value', fileUrl)
          emit('change', fileUrl)
          modalVisible.value = false
          message.success('文件上传成功')
        } else {
          message.error('上传失败，未获取到文件URL')
        }
      } catch (error) {
        console.error('文件上传失败:', error)
        message.error('文件上传失败')
      } finally {
        uploadLoading.value = false
      }
    }

    // URL相关方法
    const handleUrlSubmit = () => {
      if (!urlForm.url || !isValidUrl(urlForm.url)) {
        message.error('请输入有效的URL地址')
        return
      }

      emit('update:value', urlForm.url)
      emit('change', urlForm.url)
      modalVisible.value = false
      message.success('URL设置成功')
    }

    // 媒体库相关方法
    const loadLibraryFiles = async () => {
      try {
        libraryLoading.value = true
        const params = {
          page: libraryPagination.current,
          size: libraryPagination.pageSize,
          ...librarySearch
        }

        // 过滤空值
        Object.keys(params).forEach(key => {
          if (params[key] === undefined || params[key] === '') {
            delete params[key]
          }
        })

        const response = await mediaAPI.getMediaFiles(params)
        libraryFiles.value = response.items || []
        libraryPagination.total = response.total || 0
      } catch (error) {
        console.error('加载媒体库失败:', error)
        message.error('加载媒体库失败')
      } finally {
        libraryLoading.value = false
      }
    }

    const handleLibrarySelect = (file) => {
      selectedLibraryFile.value = file
    }

    const handleLibrarySubmit = () => {
      if (!selectedLibraryFile.value) {
        message.error('请选择一个文件')
        return
      }

      const fileUrl = selectedLibraryFile.value.file_url
      emit('update:value', fileUrl)
      emit('change', fileUrl)
      modalVisible.value = false
      message.success('文件选择成功')
    }

    // 监听props.value变化，当外部重置表单时同步更新组件状态
    watch(() => props.value, (newValue) => {
      // 当value被外部清空时，重置组件内部状态
      if (!newValue) {
        fileList.value = []
        selectedLibraryFile.value = null
        urlForm.url = ''
        uploadForm.description = ''
        uploadForm.category = props.defaultCategory
      }
    })

    return {
      // 响应式数据
      modalVisible,
      activeTab,
      uploadLoading,
      libraryLoading,
      fileList,
      selectedLibraryFile,
      uploadForm,
      urlForm,
      librarySearch,
      libraryFiles,
      libraryPagination,

      // 计算属性
      currentValue,
      modalTitle,
      acceptTypes,
      uploadHint,

      // 方法
      isImage,
      isVideo,
      isValidUrl,
      formatFileSize,
      handleChange,
      handleClear,
      handleModalCancel,
      handleTabChange,
      beforeUpload,
      handleUploadChange,
      handleUploadSubmit,
      handleUrlSubmit,
      loadLibraryFiles,
      handleLibrarySelect,
      handleLibrarySubmit
    }
  }
}
</script>

<style scoped>
.media-selector {
  width: 100%;
}

/* 当前媒体预览 */
.current-media {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}

.media-preview {
  flex-shrink: 0;
}

.video-preview video {
  display: block;
}

.url-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  min-width: 200px;
}

.url-text {
  color: #595959;
  font-size: 13px;
  word-break: break-all;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.media-actions {
  display: flex;
  gap: 8px;
}

/* 选择触发器 */
.select-trigger {
  width: 100%;
}

.select-button {
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  color: #8c8c8c;
  border-style: dashed;
  transition: all 0.3s;
}

.select-button:hover {
  border-color: #1890ff;
  color: #1890ff;
}

/* 模态框内容 */
.media-selector-modal {
  min-height: 400px;
}

/* 上传区域 */
.upload-section {
  padding: 16px 0;
}

.upload-options {
  margin-top: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.upload-actions {
  margin-top: 16px;
  text-align: center;
}

/* URL区域 */
.url-section {
  padding: 16px 0;
}

.url-preview-section {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.preview-title {
  margin-bottom: 12px;
  font-weight: 500;
  color: #262626;
}

.preview-content {
  display: flex;
  justify-content: center;
}

.url-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.url-actions {
  margin-top: 16px;
  text-align: center;
}

/* 媒体库区域 */
.library-section {
  padding: 16px 0;
}

.library-search {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.library-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.library-item {
  display: flex;
  flex-direction: column;
  padding: 8px;
  border: 2px solid transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fafafa;
}

.library-item:hover {
  border-color: #1890ff;
  background: #e6f7ff;
}

.library-item.selected {
  border-color: #1890ff;
  background: #e6f7ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.library-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 90px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 8px;
}

.video-thumb,
.file-thumb {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.library-info {
  text-align: center;
}

.file-name {
  font-size: 12px;
  color: #262626;
  margin-bottom: 4px;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 11px;
  color: #8c8c8c;
}

.library-pagination {
  margin: 16px 0;
  text-align: center;
}

.library-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .current-media {
    flex-direction: column;
    align-items: flex-start;
  }

  .library-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
  }

  .library-search .ant-row {
    flex-direction: column;
    gap: 12px;
  }
}

/* 上传组件样式覆盖 */
:deep(.ant-upload-drag) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s;
}

:deep(.ant-upload-drag:hover) {
  border-color: #1890ff;
}

:deep(.ant-upload-drag-icon) {
  font-size: 48px;
  color: #1890ff;
}

:deep(.ant-upload-text) {
  font-size: 16px;
  color: #262626;
  margin: 16px 0 8px;
}

:deep(.ant-upload-hint) {
  color: #8c8c8c;
  font-size: 14px;
}
</style>
