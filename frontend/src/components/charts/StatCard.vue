<template>
  <div class="stat-card" :class="{ 'loading': loading }">
    <div class="stat-card-header">
      <div class="stat-icon" :style="{ backgroundColor: color }">
        <component :is="icon" />
      </div>
      <div class="stat-info">
        <div class="stat-title">{{ title }}</div>
        <div class="stat-value">
          <a-statistic 
            :value="value" 
            :precision="precision"
            :suffix="suffix"
            :prefix="prefix"
            :loading="loading"
          />
        </div>
      </div>
    </div>
    
    <div class="stat-card-footer" v-if="trend !== undefined">
      <div class="trend" :class="trendClass">
        <component :is="trendIcon" />
        <span>{{ Math.abs(trend) }}%</span>
        <span class="trend-text">{{ trendText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { 
  ArrowUpOutlined, 
  ArrowDownOutlined, 
  MinusOutlined 
} from '@ant-design/icons-vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    default: 0
  },
  precision: {
    type: Number,
    default: 0
  },
  suffix: {
    type: String,
    default: ''
  },
  prefix: {
    type: String,
    default: ''
  },
  icon: {
    type: [String, Object],
    required: true
  },
  color: {
    type: String,
    default: '#1890ff'
  },
  trend: {
    type: Number,
    default: undefined
  },
  trendText: {
    type: String,
    default: '较昨日'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const trendClass = computed(() => {
  if (props.trend > 0) return 'trend-up'
  if (props.trend < 0) return 'trend-down'
  return 'trend-flat'
})

const trendIcon = computed(() => {
  if (props.trend > 0) return ArrowUpOutlined
  if (props.trend < 0) return ArrowDownOutlined
  return MinusOutlined
})
</script>

<style scoped>
.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-card.loading {
  opacity: 0.7;
}

.stat-card-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20px;
}

.stat-info {
  flex: 1;
}

.stat-title {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.stat-card-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.trend-flat {
  color: #8c8c8c;
}

.trend-text {
  color: #8c8c8c;
  margin-left: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-card {
    padding: 16px;
  }
  
  .stat-card-header {
    gap: 12px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .stat-value {
    font-size: 20px;
  }
}
</style>
