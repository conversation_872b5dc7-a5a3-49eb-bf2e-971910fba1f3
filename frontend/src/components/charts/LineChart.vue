<template>
  <div class="line-chart-container">
    <div class="chart-header" v-if="title">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="chart-actions" v-if="showActions">
        <a-button size="small" @click="refreshData">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>
    </div>
    
    <div class="chart-wrapper" :style="{ height: height + 'px' }">
      <v-chart 
        class="chart" 
        :option="chartOption" 
        :loading="loading"
        autoresize
        @click="handleChartClick"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { ReloadOutlined } from '@ant-design/icons-vue'

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Line<PERSON>hart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
])

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  data: {
    type: Array,
    default: () => []
  },
  xAxisKey: {
    type: String,
    default: 'date'
  },
  yAxisKeys: {
    type: Array,
    default: () => ['value']
  },
  seriesNames: {
    type: Array,
    default: () => ['数据']
  },
  colors: {
    type: Array,
    default: () => ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1']
  },
  height: {
    type: Number,
    default: 300
  },
  smooth: {
    type: Boolean,
    default: true
  },
  showArea: {
    type: Boolean,
    default: false
  },
  showActions: {
    type: Boolean,
    default: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['refresh', 'chartClick'])

const chartOption = computed(() => {
  if (!props.data || props.data.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  const xAxisData = props.data.map(item => item[props.xAxisKey])
  const series = props.yAxisKeys.map((key, index) => ({
    name: props.seriesNames[index] || key,
    type: 'line',
    data: props.data.map(item => item[key] || 0),
    smooth: props.smooth,
    symbol: 'circle',
    symbolSize: 6,
    lineStyle: {
      width: 2,
      color: props.colors[index % props.colors.length]
    },
    itemStyle: {
      color: props.colors[index % props.colors.length]
    },
    areaStyle: props.showArea ? {
      opacity: 0.3,
      color: props.colors[index % props.colors.length]
    } : null
  }))

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function(params) {
        let result = `<div style="margin-bottom: 4px;">${params[0].axisValue}</div>`
        params.forEach(param => {
          result += `<div style="display: flex; align-items: center; margin-bottom: 2px;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
            <span style="margin-right: 8px;">${param.seriesName}:</span>
            <span style="font-weight: bold;">${param.value}</span>
          </div>`
        })
        return result
      }
    },
    legend: {
      data: props.seriesNames,
      top: 10,
      right: 20
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: series,
    dataZoom: props.data.length > 10 ? [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100,
        height: 20,
        bottom: 10
      }
    ] : null
  }
})

const refreshData = () => {
  emit('refresh')
}

const handleChartClick = (params) => {
  emit('chartClick', params)
}
</script>

<style scoped>
.line-chart-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-wrapper {
  width: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .line-chart-container {
    padding: 16px;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .chart-title {
    font-size: 14px;
  }
}
</style>
