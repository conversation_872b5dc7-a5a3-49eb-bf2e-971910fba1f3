<template>
  <div class="pie-chart-container">
    <div class="chart-header" v-if="title">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="chart-actions" v-if="showActions">
        <a-button size="small" @click="refreshData">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>
    </div>
    
    <div class="chart-wrapper" :style="{ height: height + 'px' }">
      <v-chart 
        class="chart" 
        :option="chartOption" 
        :loading="loading"
        autoresize
        @click="handleChartClick"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { ReloadOutlined } from '@ant-design/icons-vue'

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Pie<PERSON><PERSON>,
  TitleComponent,
  TooltipComponent,
  LegendComponent
])

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  data: {
    type: Array,
    default: () => []
  },
  nameKey: {
    type: String,
    default: 'name'
  },
  valueKey: {
    type: String,
    default: 'value'
  },
  colors: {
    type: Array,
    default: () => ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16']
  },
  height: {
    type: Number,
    default: 300
  },
  radius: {
    type: Array,
    default: () => ['40%', '70%']
  },
  showLabels: {
    type: Boolean,
    default: true
  },
  showLegend: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['refresh', 'chartClick'])

const chartOption = computed(() => {
  if (!props.data || props.data.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  const seriesData = props.data.map((item, index) => ({
    name: item[props.nameKey],
    value: item[props.valueKey] || 0,
    itemStyle: {
      color: props.colors[index % props.colors.length]
    }
  }))

  const total = seriesData.reduce((sum, item) => sum + item.value, 0)

  return {
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const percent = ((params.value / total) * 100).toFixed(1)
        return `<div style="margin-bottom: 4px;">${params.name}</div>
                <div style="display: flex; align-items: center;">
                  <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
                  <span style="margin-right: 8px;">数量:</span>
                  <span style="font-weight: bold; margin-right: 8px;">${params.value}</span>
                  <span style="color: #666;">(${percent}%)</span>
                </div>`
      }
    },
    legend: props.showLegend ? {
      orient: 'vertical',
      left: 'left',
      top: 'center',
      data: seriesData.map(item => item.name),
      textStyle: {
        color: '#666'
      }
    } : null,
    series: [
      {
        type: 'pie',
        radius: props.radius,
        center: props.showLegend ? ['60%', '50%'] : ['50%', '50%'],
        data: seriesData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: props.showLabels ? {
          show: true,
          formatter: function(params) {
            const percent = ((params.value / total) * 100).toFixed(1)
            return `${params.name}\n${percent}%`
          },
          color: '#666',
          fontSize: 12
        } : {
          show: false
        },
        labelLine: props.showLabels ? {
          show: true
        } : {
          show: false
        }
      }
    ]
  }
})

const refreshData = () => {
  emit('refresh')
}

const handleChartClick = (params) => {
  emit('chartClick', params)
}
</script>

<style scoped>
.pie-chart-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-wrapper {
  width: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pie-chart-container {
    padding: 16px;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .chart-title {
    font-size: 14px;
  }
}
</style>
