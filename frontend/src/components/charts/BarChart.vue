<template>
  <div class="bar-chart-container">
    <div class="chart-header" v-if="title">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="chart-actions" v-if="showActions">
        <a-button size="small" @click="refreshData">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>
    </div>
    
    <div class="chart-wrapper" :style="{ height: height + 'px' }">
      <v-chart 
        class="chart" 
        :option="chartOption" 
        :loading="loading"
        autoresize
        @click="handleChartClick"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { ReloadOutlined } from '@ant-design/icons-vue'

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
])

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  data: {
    type: Array,
    default: () => []
  },
  xAxisKey: {
    type: String,
    default: 'name'
  },
  yAxisKey: {
    type: String,
    default: 'value'
  },
  colors: {
    type: Array,
    default: () => ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1']
  },
  height: {
    type: Number,
    default: 300
  },
  horizontal: {
    type: Boolean,
    default: false
  },
  showValues: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['refresh', 'chartClick'])

const chartOption = computed(() => {
  if (!props.data || props.data.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  const xAxisData = props.data.map(item => item[props.xAxisKey])
  const seriesData = props.data.map((item, index) => ({
    value: item[props.yAxisKey] || 0,
    itemStyle: {
      color: props.colors[index % props.colors.length]
    }
  }))

  const baseOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const param = params[0]
        return `<div style="margin-bottom: 4px;">${param.axisValue}</div>
                <div style="display: flex; align-items: center;">
                  <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 2px; margin-right: 8px;"></span>
                  <span style="font-weight: bold;">${param.value}</span>
                </div>`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    series: [{
      type: 'bar',
      data: seriesData,
      barWidth: '60%',
      label: props.showValues ? {
        show: true,
        position: props.horizontal ? 'right' : 'top',
        color: '#666',
        fontSize: 12
      } : null,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }

  if (props.horizontal) {
    // 水平柱状图
    baseOption.xAxis = {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    }
    baseOption.yAxis = {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisLabel: {
        color: '#666'
      }
    }
  } else {
    // 垂直柱状图
    baseOption.xAxis = {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisLabel: {
        color: '#666',
        rotate: xAxisData.some(item => item.length > 4) ? 45 : 0
      }
    }
    baseOption.yAxis = {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    }
  }

  // 如果数据太多，添加数据缩放
  if (props.data.length > 10) {
    baseOption.dataZoom = [
      {
        type: 'inside',
        start: 0,
        end: 100,
        orient: props.horizontal ? 'vertical' : 'horizontal'
      },
      {
        start: 0,
        end: 100,
        height: props.horizontal ? null : 20,
        width: props.horizontal ? 20 : null,
        bottom: props.horizontal ? null : 10,
        right: props.horizontal ? 10 : null,
        orient: props.horizontal ? 'vertical' : 'horizontal'
      }
    ]
  }

  return baseOption
})

const refreshData = () => {
  emit('refresh')
}

const handleChartClick = (params) => {
  emit('chartClick', params)
}
</script>

<style scoped>
.bar-chart-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-wrapper {
  width: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bar-chart-container {
    padding: 16px;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .chart-title {
    font-size: 14px;
  }
}
</style>
