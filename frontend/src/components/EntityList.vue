<template>
  <div class="entity-list">
    <!-- 筛选器 -->
    <div class="filters" v-if="showFilters">
      <a-row :gutter="16" align="middle">
        <a-col :span="6">
          <a-select
            v-model:value="filters.category_id"
            placeholder="选择分类"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option
              v-for="category in categories"
              :key="category.id"
              :value="category.id"
            >
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filters.entity_type"
            placeholder="选择类型"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="person">人物</a-select-option>
            <a-select-option value="event">事件</a-select-option>
            <a-select-option value="enterprise">企业</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="8">
          <a-input-search
            v-model:value="filters.search"
            placeholder="搜索实体..."
            @search="handleSearch"
            @change="handleSearchChange"
          />
        </a-col>
        <a-col :span="4">
          <a-button @click="handleReset">重置</a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>

    <!-- 实体列表 -->
    <div v-else-if="entities.length > 0" class="entities-grid">
      <a-row :gutter="[16, 16]">
        <a-col
          v-for="entity in entities"
          :key="entity.id"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          :xl="6"
        >
          <EntityCard :entity="entity" />
        </a-col>
      </a-row>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <a-empty
        :description="emptyDescription"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      >
        <template #extra>
          <a-button type="primary" @click="handleReset">
            重新加载
          </a-button>
        </template>
      </a-empty>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination && entities.length > 0" class="pagination-container">
      <a-pagination
        v-model:current="pagination.current"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
        @change="handlePageChange"
        @show-size-change="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<script>
import { Empty } from 'ant-design-vue'
import EntityCard from './EntityCard.vue'
import { useEntitiesStore, useCategoriesStore } from '../stores'
import { computed, onMounted, reactive, watch } from 'vue'

export default {
  name: 'EntityList',
  components: {
    EntityCard
  },
  props: {
    // 是否显示筛选器
    showFilters: {
      type: Boolean,
      default: true
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true
    },
    // 限制显示数量（用于首页等场景）
    limit: {
      type: Number,
      default: null
    },
    // 预设筛选条件
    presetFilters: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const entitiesStore = useEntitiesStore()
    const categoriesStore = useCategoriesStore()

    // 响应式数据
    const filters = reactive({
      category_id: null,
      entity_type: null,
      search: '',
      ...props.presetFilters
    })

    // 计算属性
    const entities = computed(() => entitiesStore.entities)
    const loading = computed(() => entitiesStore.loading)
    const pagination = computed(() => entitiesStore.pagination)
    const categories = computed(() => categoriesStore.categories)
    
    const emptyDescription = computed(() => {
      if (filters.search) {
        return `未找到包含"${filters.search}"的实体`
      }
      if (filters.category_id || filters.entity_type) {
        return '当前筛选条件下没有实体'
      }
      return '暂无实体数据'
    })

    // 方法
    const loadData = async () => {
      const params = { ...filters }
      if (props.limit) {
        params.size = props.limit
      }
      await entitiesStore.fetchEntities(params)
    }

    const loadCategories = async () => {
      if (!categoriesStore.hasCategories) {
        await categoriesStore.fetchCategories()
      }
    }

    const handleFilterChange = () => {
      entitiesStore.setFilters(filters)
      entitiesStore.setPagination({ current: 1 })
      loadData()
    }

    const handleSearch = (value) => {
      filters.search = value
      handleFilterChange()
    }

    const handleSearchChange = (e) => {
      if (!e.target.value) {
        handleFilterChange()
      }
    }

    const handleReset = () => {
      Object.assign(filters, {
        category_id: null,
        entity_type: null,
        search: '',
        ...props.presetFilters
      })
      entitiesStore.setFilters(filters)
      entitiesStore.setPagination({ current: 1 })
      loadData()
    }

    const handlePageChange = (page, pageSize) => {
      entitiesStore.setPagination({ current: page, pageSize })
      loadData()
    }

    const handlePageSizeChange = (current, size) => {
      entitiesStore.setPagination({ current: 1, pageSize: size })
      loadData()
    }

    // 监听预设筛选条件变化
    watch(
      () => props.presetFilters,
      (newFilters) => {
        Object.assign(filters, newFilters)
        handleFilterChange()
      },
      { deep: true }
    )

    // 组件挂载时加载数据
    onMounted(() => {
      loadCategories()
      loadData()
    })

    return {
      Empty,
      entities,
      loading,
      pagination,
      categories,
      filters,
      emptyDescription,
      handleFilterChange,
      handleSearch,
      handleSearchChange,
      handleReset,
      handlePageChange,
      handlePageSizeChange
    }
  }
}
</script>

<style scoped>
.entity-list {
  width: 100%;
}

.filters {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.loading-container {
  text-align: center;
  padding: 50px 0;
}

.entities-grid {
  margin-bottom: 24px;
}

.empty-state {
  text-align: center;
  padding: 50px 0;
}

.pagination-container {
  text-align: center;
  padding: 24px 0;
}
</style>
