<template>
  <div class="timeline-container">
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <div v-else-if="timelineItems.length > 0" class="timeline">
      <a-timeline>
        <a-timeline-item
          v-for="item in timelineItems"
          :key="item.id"
          :color="getTimelineColor(item)"
        >
          <template #dot>
            <div class="timeline-dot">
              <CalendarOutlined />
            </div>
          </template>
          
          <div class="timeline-item">
            <div class="timeline-header">
              <h3 class="timeline-title">{{ item.title }}</h3>
              <span class="timeline-date">{{ formatDate(item.event_date) }}</span>
            </div>
            
            <div class="timeline-content">
              <p v-if="item.description" class="timeline-description">
                {{ item.description }}
              </p>
              
              <!-- 媒体内容 -->
              <div v-if="item.image_url || item.video_url" class="timeline-media">
                <div v-if="item.image_url" class="timeline-image">
                  <img :src="item.image_url" :alt="item.title" @click="previewImage(item.image_url)" />
                </div>
                <div v-if="item.video_url" class="timeline-video">
                  <video :src="item.video_url" controls preload="metadata">
                    您的浏览器不支持视频播放
                  </video>
                </div>
              </div>
            </div>
            
            <div class="timeline-footer">
              <a-space>
                <span class="timeline-meta">
                  <ClockCircleOutlined />
                  {{ formatDateTime(item.created_at) }}
                </span>
                <a-button v-if="showEditButton" type="link" size="small" @click="editTimeline(item)">
                  <EditOutlined />
                  编辑
                </a-button>
              </a-space>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
      
      <!-- 分页 -->
      <div v-if="showPagination && pagination.total > pagination.pageSize" class="pagination-container">
        <a-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
          @change="handlePageChange"
          @show-size-change="handlePageSizeChange"
        />
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-state">
      <a-empty
        description="暂无时间线数据"
      >
        <template #extra>
          <a-button v-if="showAddButton" type="primary" @click="addTimeline">
            <PlusOutlined />
            添加时间线
          </a-button>
        </template>
      </a-empty>
    </div>
    
    <!-- 图片预览 -->
    <a-modal
      v-model:open="imagePreviewVisible"
      :footer="null"
      :width="800"
      centered
    >
      <img :src="previewImageUrl" style="width: 100%" />
    </a-modal>
  </div>
</template>

<script>
import { 
  CalendarOutlined, 
  ClockCircleOutlined, 
  EditOutlined, 
  PlusOutlined 
} from '@ant-design/icons-vue'

export default {
  name: 'Timeline',
  components: {
    CalendarOutlined,
    ClockCircleOutlined,
    EditOutlined,
    PlusOutlined
  },
  props: {
    // 时间线数据
    timelineItems: {
      type: Array,
      default: () => []
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 分页信息
    pagination: {
      type: Object,
      default: () => ({
        current: 1,
        pageSize: 10,
        total: 0
      })
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true
    },
    // 是否显示编辑按钮
    showEditButton: {
      type: Boolean,
      default: false
    },
    // 是否显示添加按钮
    showAddButton: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      imagePreviewVisible: false,
      previewImageUrl: ''
    }
  },
  methods: {
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    },
    
    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    },
    
    // 获取时间线颜色
    getTimelineColor(item) {
      // 可以根据不同条件返回不同颜色
      if (item.is_important) return 'red'
      if (item.event_date && new Date(item.event_date) > new Date()) return 'blue'
      return 'green'
    },
    
    // 预览图片
    previewImage(imageUrl) {
      this.previewImageUrl = imageUrl
      this.imagePreviewVisible = true
    },
    
    // 编辑时间线
    editTimeline(item) {
      this.$emit('edit', item)
    },
    
    // 添加时间线
    addTimeline() {
      this.$emit('add')
    },
    
    // 分页变化
    handlePageChange(page, pageSize) {
      this.$emit('page-change', { page, pageSize })
    },
    
    // 页面大小变化
    handlePageSizeChange(current, size) {
      this.$emit('page-size-change', { current, size })
    }
  }
}
</script>

<style scoped>
.timeline-container {
  width: 100%;
}

.loading-container {
  text-align: center;
  padding: 50px 0;
}

.timeline {
  margin: 24px 0;
}

.timeline-dot {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.timeline-item {
  padding-left: 16px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.timeline-title {
  margin: 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.timeline-date {
  color: #1890ff;
  font-weight: 500;
  white-space: nowrap;
  margin-left: 16px;
}

.timeline-content {
  margin-bottom: 16px;
}

.timeline-description {
  color: #595959;
  line-height: 1.6;
  margin-bottom: 16px;
}

.timeline-media {
  margin-top: 12px;
}

.timeline-image {
  margin-bottom: 12px;
}

.timeline-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.timeline-image img:hover {
  transform: scale(1.02);
}

.timeline-video {
  margin-bottom: 12px;
}

.timeline-video video {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.timeline-footer {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.timeline-meta {
  color: #8c8c8c;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-container {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.empty-state {
  text-align: center;
  padding: 50px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .timeline-date {
    margin-left: 0;
    margin-top: 8px;
  }
  
  .timeline-title {
    font-size: 16px;
  }
}
</style>
