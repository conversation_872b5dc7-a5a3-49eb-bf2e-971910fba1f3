<template>
  <a-card
    hoverable
    class="entity-card"
    @click="handleClick"
  >
    <!-- 卡片封面 -->
    <template #cover>
      <div class="card-cover">
        <img
          v-if="entity.avatar || entity.cover_image"
          :src="entity.avatar || entity.cover_image"
          :alt="entity.name"
          class="cover-image"
        />
        <div v-else class="default-cover">
          <component :is="getEntityIcon(entity.type || entity.entity_type)" class="entity-icon" />
        </div>
        <div class="entity-type-badge">
          {{ getEntityTypeLabel(entity.type || entity.entity_type) }}
        </div>
      </div>
    </template>

    <!-- 卡片内容 -->
    <a-card-meta>
      <template #title>
        <div class="card-title">
          {{ entity.name }}
        </div>
      </template>
      <template #description>
        <div class="card-description">
          {{ entity.description || '暂无描述' }}
        </div>
      </template>
    </a-card-meta>

    <!-- 卡片底部操作区 -->
    <template #actions>
      <span class="action-item" @click.stop="handleView">
        <EyeOutlined />
        {{ entity.view_count || entity.views_count || 0 }}
      </span>
      <span class="action-item" @click.stop="handleLike">
        <HeartOutlined :class="{ liked: isLiked }" />
        {{ entity.like_count || entity.likes_count || 0 }}
      </span>
      <span class="action-item" @click.stop="handleFlower">
        <GiftOutlined :class="{ flowered: isFlowered }" />
        {{ entity.flower_count || entity.flowers_count || 0 }}
      </span>
    </template>

    <!-- 分类标签 -->
    <div class="category-tag" v-if="entity.category">
      <a-tag color="blue">{{ entity.category.name }}</a-tag>
    </div>
  </a-card>
</template>

<script>
import { 
  EyeOutlined, 
  HeartOutlined, 
  GiftOutlined,
  UserOutlined,
  CalendarOutlined,
  BankOutlined
} from '@ant-design/icons-vue'
import { useEntitiesStore } from '../stores/entities'
import { message } from 'ant-design-vue'

export default {
  name: 'EntityCard',
  components: {
    EyeOutlined,
    HeartOutlined,
    GiftOutlined,
    UserOutlined,
    CalendarOutlined,
    BankOutlined
  },
  props: {
    entity: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isLiked: false,
      isFlowered: false
    }
  },
  setup() {
    const entitiesStore = useEntitiesStore()
    return {
      entitiesStore
    }
  },
  methods: {
    // 获取实体类型图标
    getEntityIcon(type) {
      const iconMap = {
        person: 'UserOutlined',
        event: 'CalendarOutlined',
        enterprise: 'BankOutlined'
      }
      return iconMap[type] || 'UserOutlined'
    },

    // 获取实体类型标签
    getEntityTypeLabel(type) {
      const labelMap = {
        person: '人物',
        event: '事件',
        enterprise: '企业'
      }
      return labelMap[type] || '未知'
    },

    // 点击卡片
    handleClick() {
      this.$router.push(`/entity/${this.entity.id}`)
    },

    // 查看详情
    handleView() {
      this.$router.push(`/entity/${this.entity.id}`)
    },

    // 点赞
    async handleLike() {
      try {
        const userInfo = {
          ip_address: 'unknown',
          user_agent: navigator.userAgent
        }
        await this.entitiesStore.likeEntity(this.entity.id, userInfo)
        this.isLiked = true
        setTimeout(() => {
          this.isLiked = false
        }, 2000)
      } catch (error) {
        console.error('点赞失败:', error)
      }
    },

    // 送花
    async handleFlower() {
      try {
        const userInfo = {
          ip_address: 'unknown',
          user_agent: navigator.userAgent
        }
        await this.entitiesStore.flowerEntity(this.entity.id, userInfo)
        this.isFlowered = true
        setTimeout(() => {
          this.isFlowered = false
        }, 2000)
      } catch (error) {
        console.error('送花失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.entity-card {
  height: 100%;
  transition: all 0.3s ease;
}

.entity-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-cover {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.entity-icon {
  font-size: 48px;
  color: #1890ff;
}

.entity-type-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.card-title {
  font-weight: 600;
  color: #262626;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-description {
  color: #8c8c8c;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.action-item {
  color: #8c8c8c;
  cursor: pointer;
  transition: color 0.3s ease;
}

.action-item:hover {
  color: #1890ff;
}

.action-item .liked {
  color: #ff4d4f;
}

.action-item .flowered {
  color: #faad14;
}

.category-tag {
  position: absolute;
  top: 8px;
  left: 8px;
}
</style>
