<template>
  <div ref="timelineContainerRef" class="timeline-container" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <div class="timeline-header">
      <h2 class="timeline-header__title">{{ entityName }}</h2>
      <h3 class="timeline-header__subtitle">{{ entitySubtitle }}</h3>


    </div>
    <div class="timeline">
      <div 
        v-for="(item, index) in timelineItems" 
        :key="index" 
        :ref="el => { if (el) timelineItemRefs[index] = el }"
        class="timeline-item" 
        :data-text="item.data_text || item.dataText || entitySubtitle"
        :class="{ 'timeline-item--active': activeIndex === index }"
      >
        <div class="timeline__content">
          <img class="timeline__img" :src="item.image_url || item.imgSrc || defaultImage" :alt="item.title">
          <h2 class="timeline__content-title">{{ formatDate(item.event_date) || item.title }}</h2>
          <p class="timeline__content-desc" v-html="item.description || item.desc"></p>
        </div>
      </div>
    </div>

    <!-- 固定定位的用户互动按钮 -->
    <div class="fixed-actions">
      <button class="fixed-action-btn like-btn" @click="handleLike" :disabled="likeLoading" title="点赞">
        <HeartOutlined class="action-icon" />
        <span class="action-count">{{ entityData?.like_count || entityData?.likes_count || 0 }}</span>
      </button>
      <button class="fixed-action-btn flower-btn" @click="handleFlower" :disabled="flowerLoading" title="送花">
        <!-- <FlowerOutlined class="action-icon" /> -->
        <svg class="action-icon" t="1753759796148"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4579" width="20" height="20"><path d="M778.666667 416m-34.133334 0a34.133333 34.133333 0 1 0 68.266667 0 34.133333 34.133333 0 1 0-68.266667 0Z" fill="#666666" p-id="4580"></path><path d="M925.866667 657.066667c-4.266667-4.266667-8.533333-8.533333-14.933334-8.533334l-12.8-2.133333c-93.866667-10.666667-264.533333-32-375.466666 168.533333v-162.133333h21.333333c4.266667 0 8.533333 0 12.8-2.133333 81.066667-10.666667 151.466667-51.2 202.666667-117.333334 6.4-8.533333 6.4-23.466667-4.266667-29.866666-8.533333-6.4-23.466667-6.4-29.866667 4.266666-44.8 57.6-106.666667 93.866667-177.066666 102.4h-93.866667c-83.2-10.666667-155.733333-59.733333-200.533333-138.666666-53.333333-91.733333-66.133333-211.2-32-296.533334 0-2.133333 2.133333-4.266667 4.266666-4.266666h8.533334c46.933333 0 91.733333 8.533333 132.266666 27.733333l17.066667 8.533333 8.533333-17.066666c29.866667-53.333333 64-96 100.266667-121.6l2.133333-2.133334h6.4l2.133334 2.133334c36.266667 27.733333 68.266667 66.133333 100.266666 123.733333l8.533334 17.066667 17.066666-8.533334c40.533333-19.2 87.466667-27.733333 136.533334-27.733333h8.533333c2.133333 0 4.266667 2.133333 4.266667 4.266667 17.066667 38.4 23.466667 85.333333 19.2 134.4 0 12.8 8.533333 21.333333 19.2 23.466666 10.666667 0 21.333333-8.533333 23.466666-19.2 4.266667-55.466667-4.266667-108.8-21.333333-153.6-6.4-17.066667-23.466667-29.866667-42.666667-29.866666H768c-46.933333 0-93.866667 8.533333-136.533333 23.466666-29.866667-53.333333-64-91.733333-100.266667-119.466666-2.133333-2.133333-4.266667-4.266667-6.4-4.266667-12.8-10.666667-29.866667-12.8-44.8-6.4l-12.8 6.4c-38.4 27.733333-72.533333 68.266667-102.4 119.466667-40.533333-14.933333-85.333333-23.466667-132.266667-23.466667h-8.533333c-19.2 0-36.266667 10.666667-42.666667 29.866667-40.533333 98.133333-25.6 232.533333 34.133334 334.933333 53.333333 89.6 134.4 145.066667 230.4 157.866667 4.266667 0 8.533333 2.133333 12.8 2.133333h21.333333v185.6c-110.933333-160-264.533333-142.933333-354.133333-132.266667l-12.8 2.133334c-6.4 0-10.666667 4.266667-14.933334 8.533333-4.266667 4.266667-4.266667 10.666667-2.133333 17.066667 44.8 162.133333 117.333333 202.666667 384 204.8v42.666666c0 12.8 8.533333 21.333333 21.333333 21.333334s21.333333-8.533333 21.333334-21.333334v-100.266666h2.133333c283.733333 0 360.533333-38.4 405.333333-204.8 0-6.4 0-12.8-4.266666-19.2zM142.933333 746.666667c89.6-10.666667 228.266667-19.2 322.133334 149.333333-232.533333-2.133333-285.866667-34.133333-322.133334-149.333333z m416 91.733333c72.533333-130.133333 168.533333-153.6 251.733334-153.6 25.6 0 49.066667 2.133333 70.4 4.266667-36.266667 113.066667-89.6 145.066667-322.133334 149.333333z" fill="#666666" p-id="4581"></path></svg>
        <span class="action-count">{{ entityData?.flower_count || entityData?.flowers_count || 0 }}</span>
      </button>
      <button class="fixed-action-btn share-btn" @click="handleShare" title="分享">
        <ShareAltOutlined class="action-icon" style="margin-bottom: 0px;height: 20px;" />
      </button>
      <div class="fixed-action-btn view-btn" title="浏览量">
        <EyeOutlined class="action-icon" />
        <span class="action-count">{{ entityData?.view_count || entityData?.views_count || 0 }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from 'vue';
import { HeartOutlined, ShareAltOutlined, EyeOutlined, StarOutlined } from '@ant-design/icons-vue';

// Props
const props = defineProps({
  // 实体名称
  entityName: {
    type: String,
    default: '时间线'
  },
  // 实体副标题
  entitySubtitle: {
    type: String,
    default: 'TIMELINE'
  },
  // 实体描述
  entityDescription: {
    type: String,
    default: ''
  },
  // 实体数据
  entityData: {
    type: Object,
    default: () => ({})
  },
  // 时间线数据
  timelineData: {
    type: Array,
    default: () => []
  },
  // 默认图片
  defaultImage: {
    type: String,
    default: 'https://4kwallpapers.com/images/walls/thumbs_3t/23231.jpg'
  }
});

// Emits
const emit = defineEmits(['like', 'flower', 'share']);

const timelineContainerRef = ref(null);
const timelineItemRefs = ref([]);
const activeIndex = ref(0);
const backgroundImage = ref('');

// 用户互动状态
const likeLoading = ref(false);
const flowerLoading = ref(false);

// 格式化日期函数
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.getFullYear().toString();
};

// 处理时间线数据
const timelineItems = computed(() => {
  console.log('📊 原始时间线数据:', props.timelineData);

  if (props.timelineData && props.timelineData.length > 0) {
    const mappedItems = props.timelineData.map(item => ({
      dataText: props.entitySubtitle,
      imgSrc: item.image_url || props.defaultImage,
      imgAlt: item.title,
      title: formatDate(item.event_date),
      desc: item.content || item.description || '暂无描述', // 使用content字段
      // 保留原始数据
      ...item
    }));
    console.log('🔄 映射后的时间线数据:', mappedItems);
    return mappedItems;
  }

  // 默认数据（保持原有的示例数据作为fallback）
  console.log('⚠️ 使用默认时间线数据');
  return [];
});

// 监听数据变化，更新背景图
watch(() => props.timelineData, (newData) => {
  console.log('📊 时间线数据变化:', newData);
  if (timelineItems.value.length > 0) {
    activeIndex.value = 0;
    backgroundImage.value = timelineItems.value[0].imgSrc;
    console.log('🖼️ 设置背景图:', backgroundImage.value);
  } else {
    // 如果没有数据，使用默认图片
    backgroundImage.value = props.defaultImage;
    console.log('🖼️ 使用默认背景图:', backgroundImage.value);
  }
}, { immediate: true });

const handleScroll = () => {
    // 使用容器的scrollTop而不是window.scrollY
    const pos = timelineContainerRef.value ? timelineContainerRef.value.scrollTop : 0;
    const containerHeight = timelineContainerRef.value ? timelineContainerRef.value.clientHeight : 0;
    const items = timelineItemRefs.value;
    console.log('🔄 容器滚动事件触发 - 滚动位置:', pos, '容器高度:', containerHeight, '元素数量:', items?.length || 0);

    if (!items || items.length === 0) {
        console.log('❌ 没有找到timeline元素或元素数量为0');
        return;
    }

    const itemLength = items.length;
    const scrollBottom = pos + containerHeight;

    // 获取容器的总高度
    const containerScrollHeight = timelineContainerRef.value ? timelineContainerRef.value.scrollHeight : 0;

    // 检查是否滚动到底部附近（距离底部小于100px时激活最后一个元素）
    if (scrollBottom >= containerScrollHeight - 100) {
        console.log(`🎯 滚动到底部，激活最后一个元素 (${itemLength - 1})`);
        if (activeIndex.value !== itemLength - 1) {
            activeIndex.value = itemLength - 1;
            backgroundImage.value = timelineItems.value[itemLength - 1].imgSrc;
            console.log('🖼️ 背景图切换到:', backgroundImage.value);
        }
        return;
    }

    // 遍历所有元素，找到当前应该激活的元素
    for (let i = 0; i < itemLength; i++) {
        const item = items[i];
        if (!item) {
            console.log(`❌ 元素 ${i} 为空`);
            continue;
        }

        const itemTop = item.offsetTop;
        const itemHeight = item.offsetHeight;
        const itemCenter = itemTop + itemHeight / 2;
        const itemBottom = itemTop + itemHeight;

        console.log(`📍 元素 ${i}: offsetTop=${itemTop}, height=${itemHeight}, center=${itemCenter}, bottom=${itemBottom}, 当前滚动=${pos}, 视口底部=${scrollBottom}`);

        // 判断元素是否在视口中心区域
        // 当滚动位置超过元素中心点时，激活该元素
        if (pos + containerHeight / 2 >= itemCenter && pos + containerHeight / 2 < itemBottom + itemHeight / 2) {
            console.log(`🎯 激活元素 ${i} (基于中心点检测)`);
            if (activeIndex.value !== i) {
                activeIndex.value = i;
                backgroundImage.value = timelineItems.value[i].imgSrc;
                console.log('🖼️ 背景图切换到:', backgroundImage.value);
            }
            break; // 找到激活元素后退出循环
        }
    }

    // 特殊处理：如果滚动位置很小，确保第一个元素被激活
    if (pos < 100 && activeIndex.value !== 0) {
        console.log('🎯 滚动到顶部，激活第一个元素');
        activeIndex.value = 0;
        backgroundImage.value = timelineItems.value[0].imgSrc;
        console.log('🖼️ 背景图切换到:', backgroundImage.value);
    }
};

onMounted(() => {
    // Set initial state
    if (timelineItems.value.length > 0) {
        activeIndex.value = 0;
        backgroundImage.value = timelineItems.value[0].imgSrc;
        console.log('🚀 初始化完成 - 数据项数量:', timelineItems.value.length);
        console.log('🖼️ 初始背景图:', backgroundImage.value);
    } else {
        // 如果没有数据，使用默认图片
        backgroundImage.value = props.defaultImage;
        console.log('🖼️ 使用默认背景图:', backgroundImage.value);
    }

    // 监听容器的滚动事件，而不是window的滚动事件
    if (timelineContainerRef.value) {
        timelineContainerRef.value.addEventListener('scroll', handleScroll);
        console.log('👂 容器滚动事件监听器已添加');
    }

    // Use nextTick to ensure refs are populated before calculating positions
    nextTick(() => {
        console.log('🔍 检查元素引用 - timelineItemRefs数量:', timelineItemRefs.value.length);
        timelineItemRefs.value.forEach((ref, index) => {
            if (ref) {
                console.log(`✅ 元素 ${index}: offsetTop=${ref.offsetTop}, height=${ref.offsetHeight}`);
            } else {
                console.log(`❌ 元素 ${index}: 引用为空`);
            }
        });
        handleScroll();
    });
});

onUnmounted(() => {
    // 移除容器的滚动事件监听器
    if (timelineContainerRef.value) {
        timelineContainerRef.value.removeEventListener('scroll', handleScroll);
    }
});

// 用户互动方法
const handleLike = async () => {
  likeLoading.value = true;
  try {
    emit('like');
  } finally {
    likeLoading.value = false;
  }
};

const handleFlower = async () => {
  flowerLoading.value = true;
  try {
    emit('flower');
  } finally {
    flowerLoading.value = false;
  }
};

const handleShare = () => {
  emit('share');
};

</script>

<style>
@import url('https://fonts.googleapis.com/css?family=Cardo|Pathway+Gothic+One');

/* 重置全局样式，确保test页面能正常滚动 */
html, body {
    margin: 0 !important;
    padding: 0 !important;
    height: auto !important;
    min-height: 100vh !important;
    display: block !important;
    place-items: unset !important;
}

#app {
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    text-align: left !important;
    height: auto !important;
}

/* Timeline Styles */
.timeline {
    display: flex;
    margin: 0 auto;
    flex-wrap: wrap;
    flex-direction: column;
    max-width: 700px;
    position: relative;
}

.timeline:before {
    position: absolute;
    left: 50%;
    width: 2px;
    height: 100%;
    margin-left: -1px;
    content: "";
    background: rgba(255, 255, 255, .07);
}

@media only screen and (max-width: 767px) {
    .timeline:before {
        left: 40px;
    }
}

.timeline__content-title {
    font-weight: normal;
    font-size: 66px;
    margin: -10px 0 0 0;
    transition: .4s;
    padding: 0 10px;
    box-sizing: border-box;
    font-family: 'Pathway Gothic One', sans-serif;
    color: #fff;
}

.timeline__content-desc {
    margin: 0;
    font-size: 15px;
    box-sizing: border-box;
    color: rgba(255, 255, 255, .7);
    font-family: Cardo;
    font-weight: normal;
    line-height: 25px;
}

.timeline-item {
    padding: 40px 0;
    opacity: .3;
    filter: blur(2px);
    transition: .5s;
    box-sizing: border-box;
    width: calc(50% - 40px);
    display: flex;
    position: relative;
    transform: translateY(-80px);
}

.timeline-item:before {
    content: attr(data-text);
    letter-spacing: 3px;
    width: 100%;
    position: absolute;
    color: rgba(255, 255, 255, .5);
    font-size: 13px;
    font-family: 'Pathway Gothic One', sans-serif;
    border-left: 2px solid rgba(255, 255, 255, .5);
    top: 70%;
    margin-top: -5px;
    padding-left: 15px;
    opacity: 0;
    right: calc(-100% - 39px);
}

.timeline-item:nth-child(even) {
    align-self: flex-end;
}

.timeline-item:nth-child(even):before {
    right: auto;
    text-align: right;
    left: calc(-100% - 39px);
    padding-left: 0;
    border-left: none;
    border-right: 2px solid rgba(255, 255, 255, .5);
    padding-right: 15px;
}

.timeline-item--active {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0px);
}

.timeline-item--active:before {
    top: 50%;
    transition: .3s all .2s;
    opacity: 1;
}

.timeline-item--active .timeline__content-title {
    margin: -50px 0 20px 0;
}

@media only screen and (max-width: 767px) {
    .timeline-item {
        align-self: baseline !important;
        width: 100%;
        padding: 0 30px 150px 80px;
    }

    .timeline-item:before {
        left: 10px !important;
        padding: 0 !important;
        top: 50px;
        text-align: center !important;
        width: 60px;
        border: none !important;
    }

    .timeline-item:last-child {
        padding-bottom: 40px;
    }
}

.timeline__img {
    max-width: 100%;
    box-shadow: 0 10px 15px rgba(0, 0, 0, .4);
}

.timeline-container {
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    padding: 80px 0;
    transition: .3s ease 0s;
    background-attachment: fixed;
    background-size: cover;
    /* 启用容器滚动 */
    overflow: auto;
    display: block;
}

.timeline-container:before {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(99, 99, 99, 0.8);
    content: "";
}

.timeline-header {
    width: 100%;
    text-align: center;
    margin-bottom: 80px;
    position: relative;
}

.timeline-header__title {
    color: #fff;
    font-size: 46px;
    font-family: Cardo;
    font-weight: normal;
    margin: 0;
}

.timeline-header__subtitle {
    color: rgba(255, 255, 255, .5);
    font-family: 'Pathway Gothic One', sans-serif;
    font-size: 16px;
    letter-spacing: 5px;
    margin: 10px 0 0 0;
    font-weight: normal;
}

/* 固定定位的用户互动按钮 */
.fixed-actions {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    z-index: 1000;
}

.fixed-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 56px;
    height: 56px;
    background: rgba(255, 255, 255, 0.95);
    border: none;
    border-radius: 50%;
    color: #666;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
}

.fixed-action-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.fixed-action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.fixed-action-btn .action-icon {
    font-size: 20px;
    line-height: 1;
    margin-bottom: 2px;
}

.fixed-action-btn .action-count {
    font-size: 10px;
    font-weight: 600;
    margin-top: 2px;
    line-height: 1;
}

.like-btn:hover {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.flower-btn:hover {
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
    color: white;
    box-shadow: 0 8px 25px rgba(255, 154, 158, 0.4);
}

.share-btn:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.view-btn {
    cursor: default;
    background: rgba(108, 117, 125, 0.95);
    color: white;
}

.view-btn:hover {
    transform: none;
    background: rgba(108, 117, 125, 0.95);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .fixed-actions {
        bottom: 20px;
        right: 20px;
        gap: 10px;
    }

    .fixed-action-btn {
        width: 48px;
        height: 48px;
    }

    .fixed-action-btn .action-icon {
        font-size: 16px;
    }

    .fixed-action-btn .action-count {
        font-size: 9px;
    }
}

.demo-footer {
    padding: 60px 0;
    text-align: center;
    background-color: #222; /* Added a background for visibility */
}

.demo-footer a {
    color: #999;
    display: inline-block;
    font-family: Cardo;
}
</style>
