<template>
  <div class="top-search-bar">
    <div class="search-container">
      <a-input-search
        v-model:value="searchValue"
        placeholder="搜索我的数字生命...名称 日期"
        class="search-input"
        size="large"
        @search="handleSearch"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input-search>
    </div>
    
    <div class="actions-container">
      <!-- 通知 -->
      <div class="action-item notification">
        <a-badge :count="notificationCount" :offset="[10, 0]">
          <BellOutlined class="action-icon" />
        </a-badge>
      </div>
      
      <!-- 设置 -->
      <div class="action-item">
        <SettingOutlined class="action-icon" />
      </div>
      
      <!-- 用户信息 -->
      <a-dropdown placement="bottomRight">
        <div class="user-info">
          <a-avatar :src="userAvatar" :size="32">
            <template #icon><UserOutlined /></template>
          </a-avatar>
          <span class="username">{{ username }}</span>
          <DownOutlined class="dropdown-icon" />
        </div>
        <template #overlay>
          <a-menu>
            <a-menu-item key="profile">
              <UserOutlined />
              个人资料
            </a-menu-item>
            <a-menu-item key="settings">
              <SettingOutlined />
              设置
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="logout">
              <LogoutOutlined />
              退出登录
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
import {
  SearchOutlined,
  BellOutlined,
  SettingOutlined,
  UserOutlined,
  DownOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'TopSearchBar',
  components: {
    SearchOutlined,
    BellOutlined,
    SettingOutlined,
    UserOutlined,
    DownOutlined,
    LogoutOutlined
  },
  setup() {
    const router = useRouter()
    const searchValue = ref('')
    const notificationCount = ref(1)
    const username = ref('李明')
    const userAvatar = ref('')

    const handleSearch = (value) => {
      if (value.trim()) {
        router.push({ path: '/search', query: { q: value } })
      }
    }

    return {
      searchValue,
      notificationCount,
      username,
      userAvatar,
      handleSearch
    }
  }
}
</script>

<style scoped>
.top-search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-container {
  flex: 1;
  max-width: 600px;
  margin-right: 24px;
}

.search-input {
  border-radius: 20px;
}

.search-input :deep(.ant-input) {
  border-radius: 20px;
  padding-left: 16px;
}

.search-input :deep(.ant-input-search-button) {
  border-radius: 0 20px 20px 0;
}

.actions-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-item {
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.action-item:hover {
  background-color: #f5f5f5;
}

.action-icon {
  font-size: 18px;
  color: #666;
}

.notification {
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.username {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.dropdown-icon {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-search-bar {
    padding: 8px 16px;
  }
  
  .search-container {
    margin-right: 16px;
  }
  
  .username {
    display: none;
  }
  
  .actions-container {
    gap: 12px;
  }
}
</style>
