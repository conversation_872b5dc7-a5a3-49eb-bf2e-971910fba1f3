{"name": "digital-life-museum-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@coze/api": "^1.3.5", "ant-design-vue": "^4.0.0", "axios": "^1.5.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "marked": "^16.1.1", "md-editor-v3": "^5.8.3", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-echarts": "^7.0.3", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "eslint": "^8.47.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.2", "vite": "^4.4.9"}}