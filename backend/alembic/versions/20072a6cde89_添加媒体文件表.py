"""添加媒体文件表

Revision ID: 20072a6cde89
Revises: 01d2b28e4c7d
Create Date: 2025-07-28 17:18:42.166242

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '20072a6cde89'
down_revision: Union[str, None] = '01d2b28e4c7d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('media_files',
    sa.Column('id', sa.Integer(), nullable=False, comment='主键'),
    sa.Column('filename', sa.String(length=255), nullable=False, comment='文件名'),
    sa.Column('original_filename', sa.String(length=255), nullable=False, comment='原始文件名'),
    sa.Column('file_path', sa.String(length=500), nullable=False, comment='文件路径'),
    sa.Column('file_size', sa.BigInteger(), nullable=False, comment='文件大小（字节）'),
    sa.Column('mime_type', sa.String(length=100), nullable=False, comment='MIME类型'),
    sa.Column('media_type', sa.Enum('IMAGE', 'VIDEO', 'DOCUMENT', 'OTHER', name='mediatype'), nullable=False, comment='媒体类型'),
    sa.Column('category', sa.Enum('AVATAR', 'TIMELINE_IMAGE', 'TIMELINE_VIDEO', 'GENERAL', name='mediacategory'), nullable=True, comment='媒体分类'),
    sa.Column('description', sa.Text(), nullable=True, comment='文件描述'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_media_files_id'), 'media_files', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_media_files_id'), table_name='media_files')
    op.drop_table('media_files')
    # ### end Alembic commands ###
