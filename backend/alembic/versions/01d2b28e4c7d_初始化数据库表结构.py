"""初始化数据库表结构

Revision ID: 01d2b28e4c7d
Revises: 
Create Date: 2025-07-28 16:21:37.912868

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '01d2b28e4c7d'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('categories',
    sa.Column('id', sa.Integer(), nullable=False, comment='主键'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='分类名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='分类描述'),
    sa.Column('sort_order', sa.Integer(), nullable=True, comment='排序权重'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_categories_id'), 'categories', ['id'], unique=False)
    op.create_index(op.f('ix_categories_name'), 'categories', ['name'], unique=False)
    op.create_table('life_entities',
    sa.Column('id', sa.Integer(), nullable=False, comment='主键'),
    sa.Column('category_id', sa.Integer(), nullable=False, comment='分类ID'),
    sa.Column('type', sa.Enum('PERSON', 'EVENT', 'ENTERPRISE', name='entitytype'), nullable=False, comment='类型（person/event/enterprise）'),
    sa.Column('name', sa.String(length=200), nullable=False, comment='名称'),
    sa.Column('avatar', sa.String(length=500), nullable=True, comment='头像/图标URL'),
    sa.Column('summary', sa.Text(), nullable=True, comment='简介'),
    sa.Column('description', sa.Text(), nullable=True, comment='详细描述'),
    sa.Column('birth_date', sa.DateTime(), nullable=True, comment='开始时间'),
    sa.Column('death_date', sa.DateTime(), nullable=True, comment='结束时间'),
    sa.Column('view_count', sa.Integer(), nullable=True, comment='浏览次数'),
    sa.Column('like_count', sa.Integer(), nullable=True, comment='点赞数'),
    sa.Column('flower_count', sa.Integer(), nullable=True, comment='送花数'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['category_id'], ['categories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_life_entities_id'), 'life_entities', ['id'], unique=False)
    op.create_index(op.f('ix_life_entities_name'), 'life_entities', ['name'], unique=False)
    op.create_table('timelines',
    sa.Column('id', sa.Integer(), nullable=False, comment='主键'),
    sa.Column('entity_id', sa.Integer(), nullable=False, comment='实体ID'),
    sa.Column('title', sa.String(length=200), nullable=False, comment='标题'),
    sa.Column('content', sa.Text(), nullable=True, comment='详细内容'),
    sa.Column('event_date', sa.DateTime(), nullable=False, comment='事件时间'),
    sa.Column('image_url', sa.String(length=500), nullable=False, comment='图片URL（必选）'),
    sa.Column('video_url', sa.String(length=500), nullable=True, comment='视频URL（可选）'),
    sa.Column('sort_order', sa.Integer(), nullable=True, comment='排序权重'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['entity_id'], ['life_entities.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_timelines_id'), 'timelines', ['id'], unique=False)
    op.create_table('user_actions',
    sa.Column('id', sa.Integer(), nullable=False, comment='主键'),
    sa.Column('entity_id', sa.Integer(), nullable=False, comment='实体ID'),
    sa.Column('action_type', sa.Enum('VIEW', 'LIKE', 'FLOWER', name='actiontype'), nullable=False, comment='行为类型（view/like/flower）'),
    sa.Column('ip_address', sa.String(length=45), nullable=True, comment='IP地址'),
    sa.Column('user_agent', sa.Text(), nullable=True, comment='用户代理'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.ForeignKeyConstraint(['entity_id'], ['life_entities.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_actions_id'), 'user_actions', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_actions_id'), table_name='user_actions')
    op.drop_table('user_actions')
    op.drop_index(op.f('ix_timelines_id'), table_name='timelines')
    op.drop_table('timelines')
    op.drop_index(op.f('ix_life_entities_name'), table_name='life_entities')
    op.drop_index(op.f('ix_life_entities_id'), table_name='life_entities')
    op.drop_table('life_entities')
    op.drop_index(op.f('ix_categories_name'), table_name='categories')
    op.drop_index(op.f('ix_categories_id'), table_name='categories')
    op.drop_table('categories')
    # ### end Alembic commands ###
