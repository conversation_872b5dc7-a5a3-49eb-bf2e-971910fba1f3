"""添加关于我们模块

Revision ID: 07d732faa3ff
Revises: 20072a6cde89
Create Date: 2025-07-31 16:01:23.139948

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '07d732faa3ff'
down_revision: Union[str, None] = '20072a6cde89'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('about_us',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('donation_image_url', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_about_us_id'), 'about_us', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_about_us_id'), table_name='about_us')
    op.drop_table('about_us')
    # ### end Alembic commands ###
