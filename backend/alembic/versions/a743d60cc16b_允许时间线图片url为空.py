"""允许时间线图片URL为空

Revision ID: a743d60cc16b
Revises: 760d7d94dc0e
Create Date: 2025-08-01 17:06:10.876569

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a743d60cc16b'
down_revision: Union[str, None] = '760d7d94dc0e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('timelines', 'image_url',
               existing_type=mysql.VARCHAR(length=500),
               nullable=True,
               comment='图片URL（可选）',
               existing_comment='图片URL（必选）')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('timelines', 'image_url',
               existing_type=mysql.VARCHAR(length=500),
               nullable=False,
               comment='图片URL（必选）',
               existing_comment='图片URL（可选）')
    # ### end Alembic commands ###
