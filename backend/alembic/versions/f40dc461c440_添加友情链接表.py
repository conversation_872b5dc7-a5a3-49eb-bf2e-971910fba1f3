"""添加友情链接表

Revision ID: f40dc461c440
Revises: 07d732faa3ff
Create Date: 2025-08-01 09:40:08.950881

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f40dc461c440'
down_revision: Union[str, None] = '07d732faa3ff'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('friend_links',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='友情链接名称'),
    sa.Column('url', sa.String(length=500), nullable=False, comment='友情链接URL'),
    sa.Column('description', sa.Text(), nullable=True, comment='友情链接描述'),
    sa.Column('icon_url', sa.String(length=500), nullable=True, comment='友情链接图标URL'),
    sa.Column('sort_order', sa.Integer(), nullable=True, comment='排序权重，数字越大越靠前'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_friend_links_id'), 'friend_links', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_friend_links_id'), table_name='friend_links')
    op.drop_table('friend_links')
    # ### end Alembic commands ###
