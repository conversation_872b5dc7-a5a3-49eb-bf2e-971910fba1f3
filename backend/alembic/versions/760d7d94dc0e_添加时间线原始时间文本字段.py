"""添加时间线原始时间文本字段

Revision ID: 760d7d94dc0e
Revises: f40dc461c440
Create Date: 2025-08-01 16:54:18.263948

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '760d7d94dc0e'
down_revision: Union[str, None] = 'f40dc461c440'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('timelines', sa.Column('original_time_text', sa.String(length=100), nullable=True, comment='原始时间文本'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('timelines', 'original_time_text')
    # ### end Alembic commands ###
