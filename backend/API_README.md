# 数字生命馆 API 接口文档

## 服务器信息
- **基础URL**: `http://localhost:8000`
- **API前缀**: `/api`
- **文档地址**: `http://localhost:8000/docs` (开发环境)
- **健康检查**: `http://localhost:8000/health`

## 认证说明
- 管理员接口需要JWT认证
- 在请求头中添加: `Authorization: Bearer <token>`
- 默认管理员账户: `admin` / `admin123`

## 接口列表

### 1. 系统接口

#### 1.1 健康检查
```
GET /health
```
**响应示例**:
```json
{
  "status": "healthy",
  "service": "数字生命馆 API",
  "timestamp": **********
}
```

#### 1.2 API信息
```
GET /api
```

### 2. 认证接口 (`/api/auth`)

#### 2.1 管理员登录
```
POST /api/auth/login
```
**请求体**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```
**响应**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "user": {
    "username": "admin",
    "role": "admin"
  }
}
```

#### 2.2 刷新Token
```
POST /api/auth/refresh
```
**请求体**:
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

#### 2.3 获取当前用户信息
```
GET /api/auth/me
```
**需要认证**: ✅

#### 2.4 登出
```
POST /api/auth/logout
```
**需要认证**: ✅

### 3. 分类管理接口 (`/api/categories`)

#### 3.1 获取分类列表
```
GET /api/categories/
```
**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页大小 (默认: 10, 最大: 100)
- `search`: 搜索关键词

**响应示例**:
```json
{
  "items": [
    {
      "id": 1,
      "name": "历史人物",
      "description": "重要历史人物记录",
      "sort_order": 1,
      "is_active": true,
      "created_at": "2025-01-01T00:00:00",
      "updated_at": "2025-01-01T00:00:00"
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10,
  "pages": 1
}
```

#### 3.2 获取分类详情
```
GET /api/categories/{id}
```

#### 3.3 创建分类
```
POST /api/categories/
```
**需要认证**: ✅
**请求体**:
```json
{
  "name": "新分类",
  "description": "分类描述",
  "sort_order": 1,
  "is_active": true
}
```

#### 3.4 更新分类
```
PUT /api/categories/{id}
```
**需要认证**: ✅

#### 3.5 删除分类
```
DELETE /api/categories/{id}
```
**需要认证**: ✅

### 4. 生命实体接口 (`/api/life-entities`)

#### 4.1 获取实体列表
```
GET /api/life-entities/
```
**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页大小 (默认: 10)
- `search`: 搜索关键词
- `category_id`: 分类ID筛选
- `entity_type`: 实体类型 (`person`, `event`, `enterprise`)

#### 4.2 获取实体详情
```
GET /api/life-entities/{id}
```
**说明**: 自动增加浏览次数

#### 4.3 搜索实体
```
GET /api/life-entities/search
```
**查询参数**: 同获取实体列表

#### 4.4 创建实体
```
POST /api/life-entities/
```
**需要认证**: ✅
**请求体**:
```json
{
  "name": "实体名称",
  "description": "实体描述",
  "entity_type": "person",
  "category_id": 1,
  "birth_date": "1990-01-01",
  "death_date": null,
  "cover_image": "http://example.com/image.jpg"
}
```

#### 4.5 更新实体
```
PUT /api/life-entities/{id}
```
**需要认证**: ✅

#### 4.6 删除实体
```
DELETE /api/life-entities/{id}
```
**需要认证**: ✅

#### 4.7 用户互动 - 点赞
```
POST /api/life-entities/{id}/like
```
**请求体**:
```json
{
  "ip_address": "***********",
  "user_agent": "Mozilla/5.0..."
}
```

#### 4.8 用户互动 - 送花
```
POST /api/life-entities/{id}/flower
```
**请求体**: 同点赞

### 5. 时间线接口 (`/api/timelines`)

#### 5.1 获取时间线列表
```
GET /api/timelines/
```
**查询参数**:
- `page`: 页码
- `size`: 每页大小
- `search`: 搜索关键词
- `entity_id`: 实体ID筛选
- `start_date`: 开始日期
- `end_date`: 结束日期

#### 5.2 根据实体获取时间线
```
GET /api/timelines/entity/{entity_id}
```

#### 5.3 获取时间线详情
```
GET /api/timelines/{id}
```

#### 5.4 创建时间线
```
POST /api/timelines/
```
**需要认证**: ✅
**请求体**:
```json
{
  "title": "时间线标题",
  "description": "时间线描述",
  "event_date": "2025-01-01",
  "entity_id": 1,
  "image_url": "http://example.com/image.jpg",
  "video_url": "http://example.com/video.mp4"
}
```

#### 5.5 更新时间线
```
PUT /api/timelines/{id}
```
**需要认证**: ✅

#### 5.6 删除时间线
```
DELETE /api/timelines/{id}
```
**需要认证**: ✅

#### 5.7 时间线统计
```
GET /api/timelines/stats
```
**需要认证**: ✅

### 6. 用户行为统计接口 (`/api/user-actions`)

#### 6.1 获取用户行为列表
```
GET /api/user-actions/
```
**需要认证**: ✅

#### 6.2 获取实体统计
```
GET /api/user-actions/entity/{entity_id}/stats
```

#### 6.3 获取全局统计
```
GET /api/user-actions/stats
```

#### 6.4 获取行为趋势
```
GET /api/user-actions/trends
```

#### 6.5 获取访客统计
```
GET /api/user-actions/visitors
```

#### 6.6 获取热门内容
```
GET /api/user-actions/popular
```

### 7. 媒体文件接口 (`/api/media`)

#### 7.1 获取媒体文件列表
```
GET /api/media/
```
**需要认证**: ✅

#### 7.2 上传文件
```
POST /api/media/upload
```
**需要认证**: ✅
**请求**: `multipart/form-data`
- `file`: 文件
- `title`: 文件标题 (可选)
- `description`: 文件描述 (可选)

#### 7.3 获取媒体文件详情
```
GET /api/media/{id}
```

#### 7.4 更新媒体文件信息
```
PUT /api/media/{id}
```
**需要认证**: ✅

#### 7.5 删除媒体文件
```
DELETE /api/media/{id}
```
**需要认证**: ✅

#### 7.6 下载文件
```
GET /api/media/{id}/download
```

#### 7.7 媒体统计
```
GET /api/media/stats
```
**需要认证**: ✅

#### 7.8 批量删除
```
POST /api/media/batch-delete
```
**需要认证**: ✅
**请求体**:
```json
{
  "ids": [1, 2, 3]
}
```

## 错误响应格式

所有错误响应都遵循以下格式:
```json
{
  "detail": "错误描述信息"
}
```

常见HTTP状态码:
- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 数据验证失败
- `500`: 服务器内部错误

## 前端对接注意事项

1. **CORS配置**: 后端已配置允许 `http://localhost:3000` 和 `http://localhost:5173`
2. **代理配置**: 前端Vite已配置将 `/api` 代理到 `http://localhost:8000`
3. **认证Token**: 存储在 `localStorage` 中的 `admin_token`
4. **错误处理**: 前端已配置统一的错误拦截器

## 启动说明

### 后端启动
```bash
cd backend
source venv/bin/activate
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 前端启动
```bash
cd frontend
npm run dev
```

访问地址:
- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs
