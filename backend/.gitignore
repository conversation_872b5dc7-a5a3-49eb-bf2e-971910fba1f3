# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/
.ENV/
venv.bak/
env.bak/

# 环境变量和配置文件
.env
.env.local
.env.development
.env.test
.env.production
config.ini
settings.ini
*.conf

# 数据库相关
*.db
*.sqlite
*.sqlite3
*.db-journal
*.db-shm
*.db-wal
database.db
test.db

# 上传文件目录
uploads/
app/uploads/
static/uploads/
media/
files/

# 日志文件
*.log
logs/
log/
*.log.*
app.log
error.log
access.log
debug.log

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject
.settings/
*.sublime-project
*.sublime-workspace

# 操作系统生成的文件
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# 测试相关
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/
.cache
nosetests.xml
coverage/
.nyc_output

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# 临时文件
*.tmp
*.temp
*.bak
*.backup
*.old
temp/
tmp/
.tmp/

# 缓存文件
.cache/
*.cache

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 其他
.DS_Store
*.pid
*.seed
*.pid.lock
.sass-cache/
.connect.lock
.typings/

# FastAPI 特定
.pytest_cache/
instance/
.webassets-cache

# Alembic
# 注意：通常我们会保留 alembic/ 目录和版本文件，但忽略一些临时文件
# alembic/versions/ 中的迁移文件通常需要版本控制
# 如果有特定的临时迁移文件需要忽略，可以在这里添加

# 安全相关 - 确保敏感信息不被提交
*.pem
*.key
*.crt
*.p12
*.pfx
secret.txt
secrets/
