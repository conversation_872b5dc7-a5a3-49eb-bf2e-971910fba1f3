"""
时间线管理API路由
"""
from typing import Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from ..database import get_db
from ..schemas.timeline import (
    TimelineCreate, TimelineUpdate, TimelineResponse, 
    TimelineListResponse, TimelineWithEntityResponse
)
from ..schemas.auth import MessageResponse
from ..utils.crud_timeline import (
    get_timeline, get_timeline_with_entity, get_timelines, get_timelines_by_entity,
    create_timeline, update_timeline, delete_timeline, get_timeline_stats_by_entity
)
from ..utils.crud_life_entity import get_life_entity
from ..core.auth import get_current_admin_user
import math

router = APIRouter(prefix="/timelines", tags=["时间线管理"])


@router.get("/", response_model=TimelineListResponse, summary="获取时间线列表")
async def list_timelines(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    entity_id: Optional[int] = Query(None, description="实体ID过滤"),
    start_date: Optional[datetime] = Query(None, description="开始日期过滤"),
    end_date: Optional[datetime] = Query(None, description="结束日期过滤"),
    db: Session = Depends(get_db)
):
    """
    获取时间线列表
    
    - **page**: 页码（从1开始）
    - **size**: 每页大小（1-100）
    - **search**: 搜索关键词（可选）
    - **entity_id**: 实体ID过滤（可选）
    - **start_date**: 开始日期过滤（可选）
    - **end_date**: 结束日期过滤（可选）
    """
    skip = (page - 1) * size
    timelines, total = get_timelines(
        db, skip=skip, limit=size, search=search, 
        entity_id=entity_id, start_date=start_date, end_date=end_date
    )
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    return TimelineListResponse(
        items=timelines,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/entity/{entity_id}", response_model=TimelineListResponse, summary="获取指定实体的时间线列表")
async def list_timelines_by_entity(
    entity_id: int,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    db: Session = Depends(get_db)
):
    """
    获取指定实体的时间线列表
    
    - **entity_id**: 实体ID
    - **page**: 页码（从1开始）
    - **size**: 每页大小（1-100）
    """
    # 检查实体是否存在
    entity = get_life_entity(db, entity_id)
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指定的实体不存在"
        )
    
    skip = (page - 1) * size
    timelines, total = get_timelines_by_entity(db, entity_id, skip=skip, limit=size)
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    return TimelineListResponse(
        items=timelines,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/{timeline_id}", response_model=TimelineWithEntityResponse, summary="获取时间线详情")
async def get_timeline_detail(
    timeline_id: int,
    db: Session = Depends(get_db)
):
    """
    根据ID获取时间线详情（包含实体信息）
    
    - **timeline_id**: 时间线ID
    """
    timeline = get_timeline_with_entity(db, timeline_id)
    if not timeline:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="时间线不存在"
        )
    
    # 构造包含实体信息的响应
    timeline_dict = {
        **timeline.__dict__,
        "entity_name": timeline.entity.name,
        "entity_type": timeline.entity.type.value
    }
    
    return TimelineWithEntityResponse(**timeline_dict)


@router.post("/", response_model=TimelineResponse, summary="创建时间线")
async def create_new_timeline(
    timeline: TimelineCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    创建新时间线
    
    - **entity_id**: 实体ID（必填）
    - **title**: 标题（必填，1-200字符）
    - **content**: 详细内容（可选）
    - **event_date**: 事件时间（必填）
    - **image_url**: 图片URL（必填）
    - **video_url**: 视频URL（可选）
    - **sort_order**: 排序权重（可选，默认0）
    """
    # 检查实体是否存在
    entity = get_life_entity(db, timeline.entity_id)
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="指定的实体不存在"
        )
    
    return create_timeline(db, timeline)


@router.put("/{timeline_id}", response_model=TimelineResponse, summary="更新时间线")
async def update_timeline_info(
    timeline_id: int,
    timeline: TimelineUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    更新时间线信息
    
    - **timeline_id**: 时间线ID
    - **entity_id**: 实体ID（可选）
    - **title**: 标题（可选，1-200字符）
    - **content**: 详细内容（可选）
    - **event_date**: 事件时间（可选）
    - **image_url**: 图片URL（可选）
    - **video_url**: 视频URL（可选）
    - **sort_order**: 排序权重（可选）
    """
    # 检查时间线是否存在
    existing_timeline = get_timeline(db, timeline_id)
    if not existing_timeline:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="时间线不存在"
        )
    
    # 如果更新实体ID，检查实体是否存在
    if timeline.entity_id and timeline.entity_id != existing_timeline.entity_id:
        entity = get_life_entity(db, timeline.entity_id)
        if not entity:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指定的实体不存在"
            )
    
    updated_timeline = update_timeline(db, timeline_id, timeline)
    return updated_timeline


@router.delete("/{timeline_id}", response_model=MessageResponse, summary="删除时间线")
async def delete_timeline_by_id(
    timeline_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    删除时间线
    
    - **timeline_id**: 时间线ID
    """
    # 检查时间线是否存在
    timeline = get_timeline(db, timeline_id)
    if not timeline:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="时间线不存在"
        )
    
    # 删除时间线
    success = delete_timeline(db, timeline_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除时间线失败"
        )
    
    return MessageResponse(message="时间线删除成功")


@router.get("/entity/{entity_id}/stats", summary="获取指定实体的时间线统计")
async def get_entity_timeline_stats(
    entity_id: int,
    db: Session = Depends(get_db)
):
    """
    获取指定实体的时间线统计信息
    
    - **entity_id**: 实体ID
    """
    # 检查实体是否存在
    entity = get_life_entity(db, entity_id)
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指定的实体不存在"
        )
    
    stats = get_timeline_stats_by_entity(db, entity_id)
    return {
        "entity_id": entity_id,
        "entity_name": entity.name,
        "timeline_stats": stats
    }
