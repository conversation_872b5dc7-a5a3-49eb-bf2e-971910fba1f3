"""
分类管理API路由
"""
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from ..database import get_db
from ..schemas.category import CategoryCreate, CategoryUpdate, CategoryResponse, CategoryListResponse
from ..schemas.auth import MessageResponse
from ..utils.crud_category import (
    get_category, get_category_by_name, get_categories, 
    create_category, update_category, delete_category
)
from ..core.auth import get_current_admin_user
import math

router = APIRouter(prefix="/categories", tags=["分类管理"])


@router.get("/", response_model=CategoryListResponse, summary="获取分类列表")
async def list_categories(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db)
):
    """
    获取分类列表
    
    - **page**: 页码（从1开始）
    - **size**: 每页大小（1-100）
    - **search**: 搜索关键词（可选）
    """
    skip = (page - 1) * size
    categories, total = get_categories(db, skip=skip, limit=size, search=search)
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    return CategoryListResponse(
        items=categories,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/{category_id}", response_model=CategoryResponse, summary="获取分类详情")
async def get_category_detail(
    category_id: int,
    db: Session = Depends(get_db)
):
    """
    根据ID获取分类详情
    
    - **category_id**: 分类ID
    """
    category = get_category(db, category_id)
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="分类不存在"
        )
    return category


@router.post("/", response_model=CategoryResponse, summary="创建分类")
async def create_new_category(
    category: CategoryCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    创建新分类
    
    - **name**: 分类名称（必填，1-100字符）
    - **description**: 分类描述（可选）
    - **sort_order**: 排序权重（可选，默认0）
    """
    # 检查名称是否已存在
    existing_category = get_category_by_name(db, category.name)
    if existing_category:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="分类名称已存在"
        )
    
    return create_category(db, category)


@router.put("/{category_id}", response_model=CategoryResponse, summary="更新分类")
async def update_category_info(
    category_id: int,
    category: CategoryUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    更新分类信息
    
    - **category_id**: 分类ID
    - **name**: 分类名称（可选，1-100字符）
    - **description**: 分类描述（可选）
    - **sort_order**: 排序权重（可选）
    """
    # 检查分类是否存在
    existing_category = get_category(db, category_id)
    if not existing_category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="分类不存在"
        )
    
    # 如果更新名称，检查是否与其他分类重名
    if category.name and category.name != existing_category.name:
        name_conflict = get_category_by_name(db, category.name)
        if name_conflict:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="分类名称已存在"
            )
    
    updated_category = update_category(db, category_id, category)
    return updated_category


@router.delete("/{category_id}", response_model=MessageResponse, summary="删除分类")
async def delete_category_by_id(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    删除分类
    
    - **category_id**: 分类ID
    
    注意：如果分类下有生命实体，则不能删除
    """
    # 检查分类是否存在
    category = get_category(db, category_id)
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="分类不存在"
        )
    
    # 尝试删除
    success = delete_category(db, category_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法删除分类，该分类下存在生命实体"
        )
    
    return MessageResponse(message="分类删除成功")
