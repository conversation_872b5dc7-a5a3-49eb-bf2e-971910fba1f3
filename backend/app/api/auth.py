"""
认证API路由
"""
from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ..database import get_db
from ..schemas.auth import LoginRequest, TokenResponse, RefreshTokenRequest, UserInfo, MessageResponse
from ..core.security import create_access_token, create_refresh_token, verify_token, verify_password, get_password_hash
from ..core.auth import get_current_user
from ..config import settings

router = APIRouter(prefix="/auth", tags=["认证"])

# 临时的管理员账户（在实际应用中应该存储在数据库中）
ADMIN_USERS = {
    "admin": {
        "username": "admin",
        "password_hash": get_password_hash("admin123"),  # 默认密码：admin123
        "is_admin": True
    }
}


@router.post("/login", response_model=TokenResponse, summary="管理员登录")
async def login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """
    管理员登录接口
    
    - **username**: 用户名
    - **password**: 密码
    
    返回访问令牌和刷新令牌
    """
    # 验证用户凭据
    user = ADMIN_USERS.get(login_data.username)
    if not user or not verify_password(login_data.password, user["password_hash"]):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user["username"]},
        expires_delta=access_token_expires
    )
    
    # 创建刷新令牌
    refresh_token = create_refresh_token(data={"sub": user["username"]})
    
    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=settings.access_token_expire_minutes * 60
    )


@router.post("/refresh", response_model=TokenResponse, summary="刷新访问令牌")
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """
    使用刷新令牌获取新的访问令牌
    
    - **refresh_token**: 刷新令牌
    """
    # 验证刷新令牌
    payload = verify_token(refresh_data.refresh_token)
    if payload is None or payload.get("type") != "refresh":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    username = payload.get("sub")
    if username is None or username not in ADMIN_USERS:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建新的访问令牌
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": username},
        expires_delta=access_token_expires
    )
    
    # 创建新的刷新令牌
    new_refresh_token = create_refresh_token(data={"sub": username})
    
    return TokenResponse(
        access_token=access_token,
        refresh_token=new_refresh_token,
        expires_in=settings.access_token_expire_minutes * 60
    )


@router.get("/me", response_model=UserInfo, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: dict = Depends(get_current_user)
):
    """
    获取当前登录用户的信息
    """
    return UserInfo(
        username=current_user["username"],
        is_admin=current_user["is_admin"]
    )


@router.post("/logout", response_model=MessageResponse, summary="用户登出")
async def logout(
    current_user: dict = Depends(get_current_user)
):
    """
    用户登出接口
    
    注意：JWT是无状态的，实际的登出需要客户端删除令牌
    """
    return MessageResponse(message="登出成功")
