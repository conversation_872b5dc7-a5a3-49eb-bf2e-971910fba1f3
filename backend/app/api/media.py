"""
媒体文件管理API路由
"""
import os
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File, Form
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from ..database import get_db
from ..models.media_file import MediaType, MediaCategory
from ..schemas.media import (
    MediaFileResponse, MediaFileListResponse, MediaUploadResponse,
    MediaBatchUploadResponse, MediaUpdateRequest, MediaStatsResponse
)
from ..schemas.auth import MessageResponse
from ..utils.crud_media import (
    save_upload_file, create_media_file, get_media_file, get_media_files,
    update_media_file, delete_media_file, get_media_stats
)
from ..core.auth import get_current_admin_user
import math

router = APIRouter(prefix="/media", tags=["媒体文件管理"])

# 允许的文件类型
ALLOWED_IMAGE_TYPES = {
    "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
}
ALLOWED_VIDEO_TYPES = {
    "video/mp4", "video/avi", "video/mov", "video/wmv", "video/flv"
}
ALLOWED_DOCUMENT_TYPES = {
    "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "text/plain", "text/csv"
}

# 文件大小限制（字节）
MAX_IMAGE_SIZE = 10 * 1024 * 1024  # 10MB
MAX_VIDEO_SIZE = 100 * 1024 * 1024  # 100MB
MAX_DOCUMENT_SIZE = 20 * 1024 * 1024  # 20MB


def generate_file_url(file_path: str) -> str:
    """
    生成文件访问URL

    Args:
        file_path: 文件路径

    Returns:
        文件访问URL
    """
    # 获取backend目录
    app_dir = os.path.dirname(os.path.dirname(__file__))  # 当前文件所在目录 backend/app
    backend_dir = os.path.dirname(app_dir)  # 回到backend目录
    uploads_dir = os.path.join(backend_dir, "uploads")

    # 如果文件路径包含完整路径，提取相对于uploads目录的部分
    if file_path.startswith(uploads_dir):
        relative_path = os.path.relpath(file_path, uploads_dir)
    else:
        # 如果已经是相对路径，直接使用
        relative_path = file_path.replace('uploads/', '').replace('uploads\\', '')

    # 统一使用正斜杠作为URL分隔符
    url_path = relative_path.replace(os.sep, '/')

    # 返回完整的URL，包含后端服务器地址
    # 在开发环境中，前端通过代理访问后端，所以使用相对路径即可
    # 但为了确保文件能正确访问，这里返回完整的后端URL
    return f"http://localhost:8000/uploads/{url_path}"


def validate_upload_file(upload_file: UploadFile) -> None:
    """
    验证上传文件
    
    Args:
        upload_file: 上传的文件
        
    Raises:
        HTTPException: 验证失败时抛出异常
    """
    if not upload_file.filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="文件名不能为空"
        )
    
    # 检查文件类型
    content_type = upload_file.content_type
    allowed_types = ALLOWED_IMAGE_TYPES | ALLOWED_VIDEO_TYPES | ALLOWED_DOCUMENT_TYPES
    
    if content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的文件类型: {content_type}"
        )
    
    # 检查文件大小
    file_size = 0
    if hasattr(upload_file, 'size') and upload_file.size:
        file_size = upload_file.size
    else:
        # 如果无法直接获取大小，读取文件内容来计算
        upload_file.file.seek(0, 2)  # 移动到文件末尾
        file_size = upload_file.file.tell()
        upload_file.file.seek(0)  # 重置到文件开头
    
    if content_type in ALLOWED_IMAGE_TYPES and file_size > MAX_IMAGE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"图片文件大小不能超过 {MAX_IMAGE_SIZE // (1024*1024)}MB"
        )
    elif content_type in ALLOWED_VIDEO_TYPES and file_size > MAX_VIDEO_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"视频文件大小不能超过 {MAX_VIDEO_SIZE // (1024*1024)}MB"
        )
    elif content_type in ALLOWED_DOCUMENT_TYPES and file_size > MAX_DOCUMENT_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"文档文件大小不能超过 {MAX_DOCUMENT_SIZE // (1024*1024)}MB"
        )


@router.post("/upload", response_model=MediaUploadResponse, summary="上传单个文件")
async def upload_file(
    file: UploadFile = File(...),
    category: MediaCategory = Form(MediaCategory.GENERAL),
    description: Optional[str] = Form(None),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    上传单个媒体文件（需要管理员权限）
    
    - **file**: 要上传的文件
    - **category**: 媒体分类（可选，默认为general）
    - **description**: 文件描述（可选）
    """
    try:
        # 验证文件
        validate_upload_file(file)
        
        # 保存文件
        file_info = save_upload_file(file, category)
        
        # 创建数据库记录
        media_file = create_media_file(db, file_info, description)
        
        # 生成文件访问URL
        file_url = generate_file_url(file_info['file_path'])
        
        # 构造响应
        media_response = MediaFileResponse(
            id=media_file.id,
            filename=media_file.filename,
            original_filename=media_file.original_filename,
            file_path=media_file.file_path,
            file_url=file_url,
            file_size=media_file.file_size,
            mime_type=media_file.mime_type,
            media_type=media_file.media_type,
            category=media_file.category,
            description=media_file.description,
            created_at=media_file.created_at,
            updated_at=media_file.updated_at
        )
        
        return MediaUploadResponse(
            success=True,
            message="文件上传成功",
            file=media_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件上传失败: {str(e)}"
        )


@router.get("/", response_model=MediaFileListResponse, summary="获取媒体文件列表")
async def list_media_files(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    media_type: Optional[MediaType] = Query(None, description="媒体类型过滤"),
    category: Optional[MediaCategory] = Query(None, description="媒体分类过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    获取媒体文件列表（需要管理员权限）

    - **page**: 页码（从1开始）
    - **size**: 每页大小（1-100）
    - **media_type**: 媒体类型过滤（可选）
    - **category**: 媒体分类过滤（可选）
    - **search**: 搜索关键词（可选）
    """
    skip = (page - 1) * size
    files, total = get_media_files(
        db, skip=skip, limit=size, media_type=media_type,
        category=category, search=search
    )

    pages = math.ceil(total / size) if total > 0 else 1

    # 为每个文件生成访问URL
    file_responses = []
    for file in files:
        file_url = generate_file_url(file.file_path)
        file_response = MediaFileResponse(
            id=file.id,
            filename=file.filename,
            original_filename=file.original_filename,
            file_path=file.file_path,
            file_url=file_url,
            file_size=file.file_size,
            mime_type=file.mime_type,
            media_type=file.media_type,
            category=file.category,
            description=file.description,
            created_at=file.created_at,
            updated_at=file.updated_at
        )
        file_responses.append(file_response)

    return MediaFileListResponse(
        items=file_responses,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/{file_id}", response_model=MediaFileResponse, summary="获取媒体文件详情")
async def get_media_file_detail(
    file_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    根据ID获取媒体文件详情（需要管理员权限）

    - **file_id**: 文件ID
    """
    media_file = get_media_file(db, file_id)
    if not media_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="媒体文件不存在"
        )

    # 生成文件访问URL
    file_url = generate_file_url(media_file.file_path)

    return MediaFileResponse(
        id=media_file.id,
        filename=media_file.filename,
        original_filename=media_file.original_filename,
        file_path=media_file.file_path,
        file_url=file_url,
        file_size=media_file.file_size,
        mime_type=media_file.mime_type,
        media_type=media_file.media_type,
        category=media_file.category,
        description=media_file.description,
        created_at=media_file.created_at,
        updated_at=media_file.updated_at
    )


@router.put("/{file_id}", response_model=MediaFileResponse, summary="更新媒体文件信息")
async def update_media_file_info(
    file_id: int,
    update_data: MediaUpdateRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    更新媒体文件信息（需要管理员权限）

    - **file_id**: 文件ID
    - **description**: 文件描述（可选）
    - **category**: 媒体分类（可选）
    """
    # 检查文件是否存在
    existing_file = get_media_file(db, file_id)
    if not existing_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="媒体文件不存在"
        )

    # 更新文件信息
    updated_file = update_media_file(
        db, file_id,
        description=update_data.description,
        category=update_data.category
    )

    # 生成文件访问URL
    file_url = generate_file_url(updated_file.file_path)

    return MediaFileResponse(
        id=updated_file.id,
        filename=updated_file.filename,
        original_filename=updated_file.original_filename,
        file_path=updated_file.file_path,
        file_url=file_url,
        file_size=updated_file.file_size,
        mime_type=updated_file.mime_type,
        media_type=updated_file.media_type,
        category=updated_file.category,
        description=updated_file.description,
        created_at=updated_file.created_at,
        updated_at=updated_file.updated_at
    )


@router.delete("/{file_id}", response_model=MessageResponse, summary="删除媒体文件")
async def delete_media_file_by_id(
    file_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    删除媒体文件（需要管理员权限）

    - **file_id**: 文件ID

    注意：删除操作会同时删除物理文件和数据库记录
    """
    # 检查文件是否存在
    media_file = get_media_file(db, file_id)
    if not media_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="媒体文件不存在"
        )

    # 删除文件
    success = delete_media_file(db, file_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除媒体文件失败"
        )

    return MessageResponse(message="媒体文件删除成功")


@router.get("/stats/summary", response_model=MediaStatsResponse, summary="获取媒体文件统计信息")
async def get_media_statistics(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    获取媒体文件统计信息（需要管理员权限）

    包括总文件数、总大小、各类型文件统计、分类统计、最近上传等
    """
    stats = get_media_stats(db)

    # 为最近上传的文件生成访问URL
    recent_uploads_with_url = []
    for file in stats["recent_uploads"]:
        file_url = generate_file_url(file.file_path)
        file_response = MediaFileResponse(
            id=file.id,
            filename=file.filename,
            original_filename=file.original_filename,
            file_path=file.file_path,
            file_url=file_url,
            file_size=file.file_size,
            mime_type=file.mime_type,
            media_type=file.media_type,
            category=file.category,
            description=file.description,
            created_at=file.created_at,
            updated_at=file.updated_at
        )
        recent_uploads_with_url.append(file_response)

    return MediaStatsResponse(
        total_files=stats["total_files"],
        total_size=stats["total_size"],
        total_size_mb=stats["total_size_mb"],
        image_count=stats["image_count"],
        video_count=stats["video_count"],
        document_count=stats["document_count"],
        other_count=stats["other_count"],
        category_stats=stats["category_stats"],
        recent_uploads=recent_uploads_with_url
    )


@router.get("/download/{file_id}", summary="下载媒体文件")
async def download_media_file(
    file_id: int,
    db: Session = Depends(get_db)
):
    """
    下载媒体文件（公开接口）

    - **file_id**: 文件ID
    """
    media_file = get_media_file(db, file_id)
    if not media_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="媒体文件不存在"
        )

    # 检查文件是否存在
    if not os.path.exists(media_file.file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="物理文件不存在"
        )

    return FileResponse(
        path=media_file.file_path,
        filename=media_file.original_filename,
        media_type=media_file.mime_type
    )
