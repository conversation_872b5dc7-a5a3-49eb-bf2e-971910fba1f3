"""
生命实体管理API路由
"""
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from ..database import get_db
from ..models.life_entity import EntityType
from ..schemas.life_entity import (
    LifeEntityCreate, LifeEntityUpdate, LifeEntityResponse, 
    LifeEntityListResponse, LifeEntityInteraction, LifeEntityInteractionResponse
)
from ..schemas.auth import MessageResponse
from ..utils.crud_life_entity import (
    get_life_entity, get_life_entity_by_name, get_life_entities,
    create_life_entity, update_life_entity, delete_life_entity,
    increment_view_count, handle_user_interaction
)
from ..utils.crud_category import get_category
from ..core.auth import get_current_admin_user
import math

router = APIRouter(prefix="/life-entities", tags=["生命实体管理"])


@router.get("/", response_model=LifeEntityListResponse, summary="获取生命实体列表")
async def list_life_entities(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    category_id: Optional[int] = Query(None, description="分类ID过滤"),
    entity_type: Optional[EntityType] = Query(None, description="实体类型过滤"),
    db: Session = Depends(get_db)
):
    """
    获取生命实体列表
    
    - **page**: 页码（从1开始）
    - **size**: 每页大小（1-100）
    - **search**: 搜索关键词（可选）
    - **category_id**: 分类ID过滤（可选）
    - **entity_type**: 实体类型过滤（可选）
    """
    skip = (page - 1) * size
    entities, total = get_life_entities(
        db, skip=skip, limit=size, search=search, 
        category_id=category_id, entity_type=entity_type
    )
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    return LifeEntityListResponse(
        items=entities,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/{entity_id}", response_model=LifeEntityResponse, summary="获取生命实体详情")
async def get_life_entity_detail(
    entity_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    根据ID获取生命实体详情，并自动增加浏览次数
    
    - **entity_id**: 实体ID
    """
    # 获取用户IP和User-Agent
    user_ip = request.client.host
    user_agent = request.headers.get("user-agent", "")
    
    # 增加浏览次数并获取实体
    entity = increment_view_count(db, entity_id, user_ip, user_agent)
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="生命实体不存在"
        )
    return entity


@router.post("/", response_model=LifeEntityResponse, summary="创建生命实体")
async def create_new_life_entity(
    entity: LifeEntityCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    创建新生命实体
    
    - **category_id**: 分类ID（必填）
    - **type**: 实体类型（必填：person/event/enterprise）
    - **name**: 实体名称（必填，1-200字符）
    - **avatar**: 头像/图标URL（可选）
    - **summary**: 简介（可选）
    - **description**: 详细描述（可选）
    - **birth_date**: 开始时间（可选）
    - **death_date**: 结束时间（可选）
    """
    # 检查分类是否存在
    category = get_category(db, entity.category_id)
    if not category:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="指定的分类不存在"
        )
    
    # 检查名称是否已存在（在同一分类下）
    existing_entity = get_life_entity_by_name(db, entity.name, entity.category_id)
    if existing_entity:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该分类下已存在同名的生命实体"
        )
    
    return create_life_entity(db, entity)


@router.put("/{entity_id}", response_model=LifeEntityResponse, summary="更新生命实体")
async def update_life_entity_info(
    entity_id: int,
    entity: LifeEntityUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    更新生命实体信息
    
    - **entity_id**: 实体ID
    - **category_id**: 分类ID（可选）
    - **type**: 实体类型（可选）
    - **name**: 实体名称（可选，1-200字符）
    - **avatar**: 头像/图标URL（可选）
    - **summary**: 简介（可选）
    - **description**: 详细描述（可选）
    - **birth_date**: 开始时间（可选）
    - **death_date**: 结束时间（可选）
    """
    # 检查实体是否存在
    existing_entity = get_life_entity(db, entity_id)
    if not existing_entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="生命实体不存在"
        )
    
    # 如果更新分类，检查分类是否存在
    if entity.category_id and entity.category_id != existing_entity.category_id:
        category = get_category(db, entity.category_id)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指定的分类不存在"
            )
    
    # 如果更新名称，检查是否与其他实体重名（在目标分类下）
    if entity.name and entity.name != existing_entity.name:
        target_category_id = entity.category_id or existing_entity.category_id
        name_conflict = get_life_entity_by_name(db, entity.name, target_category_id)
        if name_conflict and name_conflict.id != entity_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该分类下已存在同名的生命实体"
            )
    
    updated_entity = update_life_entity(db, entity_id, entity)
    return updated_entity


@router.delete("/{entity_id}", response_model=MessageResponse, summary="删除生命实体")
async def delete_life_entity_by_id(
    entity_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    删除生命实体
    
    - **entity_id**: 实体ID
    
    注意：删除实体会同时删除其关联的时间线和用户行为记录
    """
    # 检查实体是否存在
    entity = get_life_entity(db, entity_id)
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="生命实体不存在"
        )
    
    # 删除实体
    success = delete_life_entity(db, entity_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除生命实体失败"
        )
    
    return MessageResponse(message="生命实体删除成功")


@router.post("/{entity_id}/interact", response_model=LifeEntityInteractionResponse, summary="用户互动")
async def interact_with_entity(
    entity_id: int,
    interaction: LifeEntityInteraction,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    用户与生命实体互动（点赞、送花）
    
    - **entity_id**: 实体ID
    - **action**: 互动类型（like/flower）
    """
    # 获取用户IP和User-Agent
    user_ip = request.client.host
    user_agent = request.headers.get("user-agent", "")
    
    # 处理用户互动
    entity = handle_user_interaction(db, entity_id, interaction.action, user_ip, user_agent)
    if not entity:
        if interaction.action not in ["like", "flower"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的互动类型，仅支持 like 或 flower"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="生命实体不存在"
            )
    
    action_text = "点赞" if interaction.action == "like" else "送花"
    return LifeEntityInteractionResponse(
        message=f"{action_text}成功",
        like_count=entity.like_count,
        flower_count=entity.flower_count
    )


@router.post("/{entity_id}/like", response_model=LifeEntityInteractionResponse, summary="点赞")
async def like_entity(
    entity_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    用户点赞生命实体

    - **entity_id**: 实体ID
    """
    # 获取用户IP和User-Agent
    user_ip = request.client.host
    user_agent = request.headers.get("user-agent", "")

    # 处理点赞
    entity = handle_user_interaction(db, entity_id, "like", user_ip, user_agent)
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="生命实体不存在"
        )

    return LifeEntityInteractionResponse(
        message="点赞成功",
        like_count=entity.like_count,
        flower_count=entity.flower_count
    )


@router.post("/{entity_id}/flower", response_model=LifeEntityInteractionResponse, summary="送花")
async def flower_entity(
    entity_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    用户送花给生命实体

    - **entity_id**: 实体ID
    """
    # 获取用户IP和User-Agent
    user_ip = request.client.host
    user_agent = request.headers.get("user-agent", "")

    # 处理送花
    entity = handle_user_interaction(db, entity_id, "flower", user_ip, user_agent)
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="生命实体不存在"
        )

    return LifeEntityInteractionResponse(
        message="送花成功",
        like_count=entity.like_count,
        flower_count=entity.flower_count
    )
