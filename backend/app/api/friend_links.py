from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List

from app.database import get_db
from app.core.auth import get_current_admin_user
from app.schemas.friend_link import (
    FriendLinkCreate, 
    FriendLinkUpdate, 
    FriendLinkResponse, 
    FriendLinkListResponse
)
from app.utils import crud_friend_link

router = APIRouter()

@router.get("/", response_model=FriendLinkListResponse, summary="获取友情链接列表（管理员）")
async def get_friend_links_admin(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="状态筛选"),
    sort_by: str = Query("sort_order", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_admin_user)
):
    """获取友情链接列表（管理员接口，需要认证）"""
    result = crud_friend_link.get_friend_links(
        db=db,
        page=page,
        size=size,
        search=search,
        is_active=is_active,
        sort_by=sort_by,
        sort_order=sort_order
    )
    return result

@router.get("/active", response_model=List[FriendLinkResponse], summary="获取启用的友情链接（公开）")
async def get_active_friend_links(db: Session = Depends(get_db)):
    """获取所有启用的友情链接（公开接口，无需认证）"""
    friend_links = crud_friend_link.get_active_friend_links(db)
    return friend_links

@router.get("/stats", summary="获取友情链接统计（管理员）")
async def get_friend_links_stats(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_admin_user)
):
    """获取友情链接统计信息（管理员接口）"""
    stats = crud_friend_link.get_friend_links_stats(db)
    return stats

@router.get("/{friend_link_id}", response_model=FriendLinkResponse, summary="获取友情链接详情")
async def get_friend_link(
    friend_link_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_admin_user)
):
    """获取友情链接详情（管理员接口）"""
    friend_link = crud_friend_link.get_friend_link_by_id(db, friend_link_id)
    if not friend_link:
        raise HTTPException(status_code=404, detail="友情链接不存在")
    return friend_link

@router.post("/", response_model=FriendLinkResponse, summary="创建友情链接")
async def create_friend_link(
    friend_link: FriendLinkCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_admin_user)
):
    """创建友情链接（管理员接口）"""
    try:
        db_friend_link = crud_friend_link.create_friend_link(db, friend_link)
        return db_friend_link
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"创建友情链接失败: {str(e)}")

@router.put("/{friend_link_id}", response_model=FriendLinkResponse, summary="更新友情链接")
async def update_friend_link(
    friend_link_id: int,
    friend_link_update: FriendLinkUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_admin_user)
):
    """更新友情链接（管理员接口）"""
    db_friend_link = crud_friend_link.update_friend_link(db, friend_link_id, friend_link_update)
    if not db_friend_link:
        raise HTTPException(status_code=404, detail="友情链接不存在")
    return db_friend_link

@router.delete("/{friend_link_id}", summary="删除友情链接")
async def delete_friend_link(
    friend_link_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_admin_user)
):
    """删除友情链接（管理员接口）"""
    success = crud_friend_link.delete_friend_link(db, friend_link_id)
    if not success:
        raise HTTPException(status_code=404, detail="友情链接不存在")
    return {"message": "友情链接删除成功"}
