"""
AI时间线批量创建API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ..database import get_db
from ..schemas.ai_timeline import AITimelineRequest, AITimelineResponse
from ..utils.crud_ai_timeline import process_ai_timeline_data
from ..core.auth import get_current_admin_user

router = APIRouter(prefix="/ai-timeline", tags=["AI时间线批量创建"])


@router.post("/batch-create", response_model=AITimelineResponse, summary="AI时间线批量创建")
async def batch_create_ai_timeline(
    request: AITimelineRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    根据AI返回的结构化数据批量创建实体和时间线
    
    **请求体结构**:
    ```json
    {
        "content": {
            "output": [
                {
                    "desc": "事件描述",
                    "image": "图片URL",
                    "time": "时间（中文格式）"
                }
            ],
            "things": "实体名称"
        },
        "category_id": 1,
        "entity_type": "person"
    }
    ```
    
    **支持的时间格式**:
    - 公元前XXX年
    - 公元XXX年
    - XXXX年
    - XXXX年XX月
    - XXXX年XX月XX日
    - 现代
    
    **实体类型**:
    - person: 人物
    - event: 事件
    - enterprise: 企业
    
    **响应说明**:
    - 如果实体不存在，会自动创建
    - 返回每个时间线事件的创建结果
    - 包含成功和失败的统计信息
    """
    try:
        # 验证请求数据
        if not request.content.things:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="实体名称不能为空"
            )

        if not request.content.output:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="时间线事件列表不能为空"
            )

        # 调试日志
        print(f"AI时间线批量创建请求:")
        print(f"  实体名称: {request.content.things}")
        print(f"  实体头像: {request.content.thingsImg}")
        print(f"  事件数量: {len(request.content.output)}")
        print(f"  分类ID: {request.category_id}")
        print(f"  实体类型: {request.entity_type}")
        
        # 处理AI时间线数据
        entity, entity_created, timeline_results = process_ai_timeline_data(
            db=db,
            entity_name=request.content.things,
            events=request.content.output,
            category_id=request.category_id or 1,
            entity_type=request.entity_type or "person",
            avatar_url=request.content.thingsImg  # 传递头像URL
        )
        
        if not entity:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建或获取实体失败"
            )
        
        # 统计结果
        total_events = len(timeline_results)
        successful_events = sum(1 for result in timeline_results if result.success)
        failed_events = total_events - successful_events
        
        # 构造响应
        success = failed_events == 0  # 只有全部成功才算成功
        
        if success:
            message = f"成功为实体 '{entity.name}' 创建了 {successful_events} 个时间线事件"
        else:
            message = f"为实体 '{entity.name}' 创建时间线事件完成：成功 {successful_events} 个，失败 {failed_events} 个"
        
        if entity_created:
            message += f"（实体为新创建）"
        
        return AITimelineResponse(
            success=success,
            entity_id=entity.id,
            entity_name=entity.name,
            entity_created=entity_created,
            total_events=total_events,
            successful_events=successful_events,
            failed_events=failed_events,
            results=timeline_results,
            message=message
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 捕获其他异常
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理AI时间线数据时发生错误: {str(e)}"
        )


@router.get("/test", summary="测试接口")
async def test_ai_timeline():
    """
    测试AI时间线接口是否正常工作
    """
    return {
        "message": "AI时间线批量创建接口正常工作",
        "version": "1.0.0",
        "supported_time_formats": [
            "公元前XXX年",
            "公元XXX年", 
            "XXXX年",
            "XXXX年XX月",
            "XXXX年XX月XX日",
            "现代"
        ],
        "supported_entity_types": [
            "person",
            "event", 
            "enterprise"
        ]
    }
