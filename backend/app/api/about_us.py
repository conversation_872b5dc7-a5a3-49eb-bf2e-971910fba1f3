from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app import schemas
from app.database import get_db
from app.utils import crud_about_us
from app.core.auth import get_current_admin_user

router = APIRouter()

@router.get("/", response_model=schemas.about_us.AboutUsPublic, summary="获取关于我们信息")
def read_about_us(db: Session = Depends(get_db)):
    """
    获取“关于我们”页面的内容和打赏图片URL。
    这是一个公开接口，无需认证。
    """
    db_about_us = crud_about_us.get_about_us(db)
    if db_about_us is None:
        # 如果数据库中没有记录，返回一个默认的空状态
        return schemas.about_us.AboutUsPublic(content="暂无内容", donation_image_url=None)
    return db_about_us

@router.post("/", response_model=schemas.about_us.AboutUsInDB, summary="创建或更新关于我们信息")
def create_or_update_about_us(
    *,
    db: Session = Depends(get_db),
    about_us_in: schemas.about_us.AboutUsCreate,
    current_user: schemas.auth.UserInfo = Depends(get_current_admin_user)
):
    """
    创建或更新“关于我们”页面的内容和打赏图片URL。
    此接口需要管理员权限。
    """
    return crud_about_us.create_or_update_about_us(db=db, about_us=about_us_in)
