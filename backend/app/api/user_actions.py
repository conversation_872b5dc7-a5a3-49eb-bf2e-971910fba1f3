"""
用户行为统计API路由
"""
from typing import Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from ..database import get_db
from ..models.user_action import ActionType
from ..schemas.user_action import (
    UserActionListResponse, UserActionStatsResponse, GlobalStatsResponse,
    ActionTrendListResponse, VisitorStatsResponse
)
from ..utils.crud_user_action import (
    get_user_actions, get_entity_action_stats, get_global_stats,
    get_action_trends, get_visitor_stats
)
from ..utils.crud_life_entity import get_life_entity
from ..core.auth import get_current_admin_user
import math

router = APIRouter(prefix="/user-actions", tags=["用户行为统计"])


@router.get("/", response_model=UserActionListResponse, summary="获取用户行为列表")
async def list_user_actions(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    entity_id: Optional[int] = Query(None, description="实体ID过滤"),
    action_type: Optional[ActionType] = Query(None, description="行为类型过滤"),
    start_date: Optional[datetime] = Query(None, description="开始日期过滤"),
    end_date: Optional[datetime] = Query(None, description="结束日期过滤"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    获取用户行为列表（需要管理员权限）
    
    - **page**: 页码（从1开始）
    - **size**: 每页大小（1-100）
    - **entity_id**: 实体ID过滤（可选）
    - **action_type**: 行为类型过滤（可选）
    - **start_date**: 开始日期过滤（可选）
    - **end_date**: 结束日期过滤（可选）
    """
    skip = (page - 1) * size
    actions, total = get_user_actions(
        db, skip=skip, limit=size, entity_id=entity_id,
        action_type=action_type, start_date=start_date, end_date=end_date
    )
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    return UserActionListResponse(
        items=actions,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/stats/entity/{entity_id}", response_model=UserActionStatsResponse, summary="获取指定实体的行为统计")
async def get_entity_stats(
    entity_id: int,
    db: Session = Depends(get_db)
):
    """
    获取指定实体的用户行为统计
    
    - **entity_id**: 实体ID
    """
    # 检查实体是否存在
    entity = get_life_entity(db, entity_id)
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指定的实体不存在"
        )
    
    stats = get_entity_action_stats(db, entity_id)
    if not stats:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="无法获取实体统计信息"
        )
    
    return UserActionStatsResponse(**stats)


@router.get("/stats/global", response_model=GlobalStatsResponse, summary="获取全局统计信息")
async def get_global_statistics(
    db: Session = Depends(get_db)
):
    """
    获取全局用户行为统计信息
    
    包括总实体数、总行为数、各类行为统计、独立访客数、热门实体等
    """
    stats = get_global_stats(db)
    return GlobalStatsResponse(**stats)


@router.get("/trends", response_model=ActionTrendListResponse, summary="获取行为趋势数据")
async def get_action_trend_data(
    entity_id: Optional[int] = Query(None, description="实体ID（可选，为空时获取全局趋势）"),
    period: str = Query("daily", regex="^(daily|weekly|monthly)$", description="统计周期"),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db)
):
    """
    获取用户行为趋势数据
    
    - **entity_id**: 实体ID（可选，为空时获取全局趋势）
    - **period**: 统计周期（daily/weekly/monthly）
    - **days**: 统计天数（1-365）
    """
    # 如果指定了实体ID，检查实体是否存在
    entity_name = None
    if entity_id:
        entity = get_life_entity(db, entity_id)
        if not entity:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="指定的实体不存在"
            )
        entity_name = entity.name
    
    trends = get_action_trends(db, entity_id=entity_id, period=period, days=days)
    
    return ActionTrendListResponse(
        entity_id=entity_id,
        entity_name=entity_name,
        trends=trends,
        period=period
    )


@router.get("/stats/visitors", response_model=VisitorStatsResponse, summary="获取访客统计信息")
async def get_visitor_statistics(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    获取访客统计信息（需要管理员权限）
    
    包括总访客数、今日新访客、回访访客、用户代理统计等
    """
    stats = get_visitor_stats(db)
    return VisitorStatsResponse(**stats)


@router.get("/stats/summary", summary="获取统计摘要信息")
async def get_stats_summary(
    db: Session = Depends(get_db)
):
    """
    获取统计摘要信息（公开接口）
    
    返回基本的统计数据，不包含敏感信息
    """
    global_stats = get_global_stats(db)
    
    # 只返回基本统计信息，不包含详细的热门实体列表
    return {
        "total_entities": global_stats["total_entities"],
        "total_views": global_stats["total_views"],
        "total_likes": global_stats["total_likes"],
        "total_flowers": global_stats["total_flowers"],
        "message": "数字生命馆统计摘要"
    }


@router.get("/stats/entity/{entity_id}/summary", summary="获取实体统计摘要")
async def get_entity_stats_summary(
    entity_id: int,
    db: Session = Depends(get_db)
):
    """
    获取指定实体的统计摘要（公开接口）

    - **entity_id**: 实体ID
    """
    # 检查实体是否存在
    entity = get_life_entity(db, entity_id)
    if not entity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指定的实体不存在"
        )

    stats = get_entity_action_stats(db, entity_id)
    if not stats:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="无法获取实体统计信息"
        )

    # 只返回基本统计信息，不包含详细的行为记录
    return {
        "entity_id": stats["entity_id"],
        "entity_name": stats["entity_name"],
        "view_count": stats["view_count"],
        "like_count": stats["like_count"],
        "flower_count": stats["flower_count"],
        "unique_visitors": stats["unique_visitors"]
    }


@router.get("/stats/dashboard", summary="获取管理后台仪表盘统计数据")
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    获取管理后台仪表盘的综合统计数据（需要管理员权限）

    包括：
    - 核心业务指标
    - 内容增长趋势
    - 用户行为分析
    - 热门内容排行
    """
    from ..utils.crud_user_action import get_dashboard_comprehensive_stats

    stats = get_dashboard_comprehensive_stats(db)
    return stats
