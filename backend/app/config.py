"""
数字生命馆应用配置文件
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = "数字生命馆 API"
    debug: bool = False
    
    # 数据库配置
    database_url: str = "mysql+pymysql://root:111111@localhost:3306/dielife?charset=utf8mb4&auth_plugin=mysql_native_password"
    
    # JWT配置
    secret_key: str = "your-secret-key-here"  # 在生产环境中应该使用环境变量
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    
    # 文件上传配置
    upload_dir: str = "../uploads"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_image_types: list = ["image/jpeg", "image/png", "image/gif", "image/webp"]
    allowed_video_types: list = ["video/mp4", "video/avi", "video/mov", "video/wmv"]
    
    # 安全配置
    cors_origins: list = ["http://localhost:3000", "http://localhost:5173"]
    
    class Config:
        env_file = ".env"


# 创建全局配置实例
settings = Settings()
