"""
数字生命馆 FastAPI 应用主入口文件
"""
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import os
import time
from .config import settings
from .api import auth, categories, life_entities, timelines, user_actions, media, ai_timeline, about_us, friend_links

# 创建FastAPI应用实例
app = FastAPI(
    title="数字生命馆 API",
    description="数字生命馆后端API服务 - 专注于记录和展示人物、事件、企业历史时间线的数字化平台",
    version="1.0.0",
    docs_url="/docs" if settings.debug else None,  # 生产环境中禁用文档
    redoc_url="/redoc" if settings.debug else None
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 添加请求处理时间中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加请求处理时间到响应头"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    return JSONResponse(
        status_code=500,
        content={
            "message": "服务器内部错误",
            "detail": str(exc) if settings.debug else "请联系管理员"
        }
    )

# 挂载静态文件服务
# 修正路径：文件实际存储在 backend/uploads 目录
app_dir = os.path.dirname(os.path.dirname(__file__))  # backend/app -> backend
uploads_dir = os.path.join(app_dir, "uploads")
if not os.path.exists(uploads_dir):
    os.makedirs(uploads_dir)
app.mount("/uploads", StaticFiles(directory=uploads_dir), name="uploads")

# 注册API路由
app.include_router(auth.router, prefix="/api")
app.include_router(categories.router, prefix="/api")
app.include_router(life_entities.router, prefix="/api")
app.include_router(timelines.router, prefix="/api")
app.include_router(user_actions.router, prefix="/api")
app.include_router(media.router, prefix="/api")
app.include_router(ai_timeline.router, prefix="/api")
app.include_router(about_us.router, prefix="/api/about-us", tags=["关于我们"])
app.include_router(friend_links.router, prefix="/api/friend-links", tags=["友情链接"])

@app.get("/", tags=["系统"])
async def root():
    """根路径健康检查"""
    return {
        "message": "数字生命馆 API 服务正在运行",
        "version": "1.0.0",
        "docs": "/docs" if settings.debug else "文档已禁用"
    }

@app.get("/health", tags=["系统"])
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "数字生命馆 API",
        "timestamp": int(time.time())
    }

@app.get("/api", tags=["系统"])
async def api_info():
    """API信息端点"""
    return {
        "name": "数字生命馆 API",
        "version": "1.0.0",
        "description": "专注于记录和展示人物、事件、企业历史时间线的数字化平台",
        "endpoints": {
            "auth": "/api/auth - 认证相关接口",
            "categories": "/api/categories - 分类管理接口",
            "life_entities": "/api/life-entities - 生命实体管理接口",
            "timelines": "/api/timelines - 时间线管理接口",
            "user_actions": "/api/user-actions - 用户行为统计接口",
            "media": "/api/media - 媒体文件管理接口",
            "ai_timeline": "/api/ai-timeline - AI时间线批量创建接口",
            "docs": "/docs - API文档（仅开发环境）"
        }
    }
