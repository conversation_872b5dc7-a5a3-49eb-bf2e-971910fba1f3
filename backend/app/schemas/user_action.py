"""
用户行为统计相关的Pydantic模式
"""
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field
from ..models.user_action import ActionType


class UserActionResponse(BaseModel):
    """用户行为响应模式"""
    id: int = Field(..., description="行为ID")
    entity_id: int = Field(..., description="实体ID")
    action_type: ActionType = Field(..., description="行为类型")
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    created_at: datetime = Field(..., description="创建时间")
    
    class Config:
        from_attributes = True


class UserActionListResponse(BaseModel):
    """用户行为列表响应模式"""
    items: List[UserActionResponse] = Field(..., description="行为列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class UserActionStatsResponse(BaseModel):
    """用户行为统计响应模式"""
    entity_id: int = Field(..., description="实体ID")
    entity_name: str = Field(..., description="实体名称")
    total_actions: int = Field(..., description="总行为数")
    view_count: int = Field(..., description="浏览次数")
    like_count: int = Field(..., description="点赞次数")
    flower_count: int = Field(..., description="送花次数")
    unique_visitors: int = Field(..., description="独立访客数")
    recent_actions: List[UserActionResponse] = Field(..., description="最近行为记录")


class GlobalStatsResponse(BaseModel):
    """全局统计响应模式"""
    total_entities: int = Field(..., description="总实体数")
    total_actions: int = Field(..., description="总行为数")
    total_views: int = Field(..., description="总浏览次数")
    total_likes: int = Field(..., description="总点赞次数")
    total_flowers: int = Field(..., description="总送花次数")
    unique_visitors: int = Field(..., description="独立访客数")
    popular_entities: List[dict] = Field(..., description="热门实体列表")


class ActionTrendResponse(BaseModel):
    """行为趋势响应模式"""
    date: str = Field(..., description="日期")
    view_count: int = Field(..., description="浏览次数")
    like_count: int = Field(..., description="点赞次数")
    flower_count: int = Field(..., description="送花次数")
    total_count: int = Field(..., description="总行为数")


class ActionTrendListResponse(BaseModel):
    """行为趋势列表响应模式"""
    entity_id: Optional[int] = Field(None, description="实体ID（全局统计时为None）")
    entity_name: Optional[str] = Field(None, description="实体名称（全局统计时为None）")
    trends: List[ActionTrendResponse] = Field(..., description="趋势数据")
    period: str = Field(..., description="统计周期（daily/weekly/monthly）")


class VisitorStatsResponse(BaseModel):
    """访客统计响应模式"""
    total_visitors: int = Field(..., description="总访客数")
    new_visitors_today: int = Field(..., description="今日新访客数")
    returning_visitors: int = Field(..., description="回访访客数")
    top_countries: List[dict] = Field(..., description="访客来源国家/地区统计")
    top_user_agents: List[dict] = Field(..., description="用户代理统计")
