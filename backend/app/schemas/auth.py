"""
认证相关的Pydantic模式
"""
from typing import Optional
from pydantic import BaseModel


class LoginRequest(BaseModel):
    """登录请求模式"""
    username: str
    password: str


class TokenResponse(BaseModel):
    """令牌响应模式"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # 访问令牌过期时间（秒）


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模式"""
    refresh_token: str


class UserInfo(BaseModel):
    """用户信息模式"""
    username: str
    is_admin: bool


class MessageResponse(BaseModel):
    """消息响应模式"""
    message: str
