"""
分类相关的Pydantic模式
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field


class CategoryBase(BaseModel):
    """分类基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="分类名称")
    description: Optional[str] = Field(None, description="分类描述")
    sort_order: int = Field(default=0, description="排序权重")


class CategoryCreate(CategoryBase):
    """创建分类模式"""
    pass


class CategoryUpdate(BaseModel):
    """更新分类模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="分类名称")
    description: Optional[str] = Field(None, description="分类描述")
    sort_order: Optional[int] = Field(None, description="排序权重")


class CategoryResponse(CategoryBase):
    """分类响应模式"""
    id: int = Field(..., description="分类ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class CategoryListResponse(BaseModel):
    """分类列表响应模式"""
    items: list[CategoryResponse] = Field(..., description="分类列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")
