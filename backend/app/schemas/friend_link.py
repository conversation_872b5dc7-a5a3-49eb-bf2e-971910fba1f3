from pydantic import BaseModel, HttpUrl, Field
from typing import Optional
from datetime import datetime

class FriendLinkBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="友情链接名称")
    url: str = Field(..., min_length=1, max_length=500, description="友情链接URL")
    description: Optional[str] = Field(None, max_length=1000, description="友情链接描述")
    icon_url: Optional[str] = Field(None, max_length=500, description="友情链接图标URL")
    sort_order: int = Field(default=0, description="排序权重，数字越大越靠前")
    is_active: bool = Field(default=True, description="是否启用")

class FriendLinkCreate(FriendLinkBase):
    pass

class FriendLinkUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="友情链接名称")
    url: Optional[str] = Field(None, min_length=1, max_length=500, description="友情链接URL")
    description: Optional[str] = Field(None, max_length=1000, description="友情链接描述")
    icon_url: Optional[str] = Field(None, max_length=500, description="友情链接图标URL")
    sort_order: Optional[int] = Field(None, description="排序权重，数字越大越靠前")
    is_active: Optional[bool] = Field(None, description="是否启用")

class FriendLinkResponse(FriendLinkBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class FriendLinkListResponse(BaseModel):
    items: list[FriendLinkResponse]
    total: int
    page: int
    size: int
    pages: int
