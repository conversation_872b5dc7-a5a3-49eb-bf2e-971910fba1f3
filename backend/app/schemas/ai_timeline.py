"""
AI时间线批量创建相关的Pydantic模式
"""
from typing import List, Optional
from pydantic import BaseModel, Field


class AITimelineEvent(BaseModel):
    """AI返回的单个时间线事件"""
    desc: str = Field(..., description="事件描述")
    image: str = Field(..., description="事件图片URL")
    time: str = Field(..., description="事件时间（中文格式）")


class AITimelineOutput(BaseModel):
    """AI返回的时间线输出结构"""
    output: List[AITimelineEvent] = Field(..., description="时间线事件列表")
    things: str = Field(..., description="实体名称")
    thingsImg: Optional[str] = Field(None, description="实体头像图标URL")


class AITimelineRequest(BaseModel):
    """AI时间线批量创建请求"""
    content: AITimelineOutput = Field(..., description="AI返回的内容")
    category_id: Optional[int] = Field(1, description="分类ID，默认为1")
    entity_type: Optional[str] = Field("person", description="实体类型，默认为person")


class TimelineCreationResult(BaseModel):
    """单个时间线创建结果"""
    success: bool = Field(..., description="是否创建成功")
    timeline_id: Optional[int] = Field(None, description="创建的时间线ID")
    original_time: str = Field(..., description="原始时间字符串")
    parsed_time: Optional[str] = Field(None, description="解析后的时间")
    error: Optional[str] = Field(None, description="错误信息")


class AITimelineResponse(BaseModel):
    """AI时间线批量创建响应"""
    success: bool = Field(..., description="整体操作是否成功")
    entity_id: Optional[int] = Field(None, description="实体ID")
    entity_name: str = Field(..., description="实体名称")
    entity_created: bool = Field(..., description="实体是否为新创建")
    total_events: int = Field(..., description="总事件数")
    successful_events: int = Field(..., description="成功创建的事件数")
    failed_events: int = Field(..., description="失败的事件数")
    results: List[TimelineCreationResult] = Field(..., description="每个事件的创建结果")
    message: str = Field(..., description="操作结果消息")
