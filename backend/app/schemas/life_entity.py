"""
生命实体相关的Pydantic模式
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field
from ..models.life_entity import EntityType


class LifeEntityBase(BaseModel):
    """生命实体基础模式"""
    category_id: int = Field(..., description="分类ID")
    type: EntityType = Field(..., description="实体类型")
    name: str = Field(..., min_length=1, max_length=200, description="名称")
    avatar: Optional[str] = Field(None, max_length=500, description="头像/图标URL")
    summary: Optional[str] = Field(None, description="简介")
    description: Optional[str] = Field(None, description="详细描述")
    birth_date: Optional[datetime] = Field(None, description="开始时间")
    death_date: Optional[datetime] = Field(None, description="结束时间")


class LifeEntityCreate(LifeEntityBase):
    """创建生命实体模式"""
    pass


class LifeEntityUpdate(BaseModel):
    """更新生命实体模式"""
    category_id: Optional[int] = Field(None, description="分类ID")
    type: Optional[EntityType] = Field(None, description="实体类型")
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="名称")
    avatar: Optional[str] = Field(None, max_length=500, description="头像/图标URL")
    summary: Optional[str] = Field(None, description="简介")
    description: Optional[str] = Field(None, description="详细描述")
    birth_date: Optional[datetime] = Field(None, description="开始时间")
    death_date: Optional[datetime] = Field(None, description="结束时间")


class LifeEntityResponse(LifeEntityBase):
    """生命实体响应模式"""
    id: int = Field(..., description="实体ID")
    view_count: int = Field(..., description="浏览次数")
    like_count: int = Field(..., description="点赞数")
    flower_count: int = Field(..., description="送花数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class LifeEntityListResponse(BaseModel):
    """生命实体列表响应模式"""
    items: list[LifeEntityResponse] = Field(..., description="实体列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class LifeEntityInteraction(BaseModel):
    """生命实体互动模式"""
    action: str = Field(..., description="互动类型（like/flower）")


class LifeEntityInteractionResponse(BaseModel):
    """生命实体互动响应模式"""
    message: str = Field(..., description="响应消息")
    like_count: int = Field(..., description="当前点赞数")
    flower_count: int = Field(..., description="当前送花数")
