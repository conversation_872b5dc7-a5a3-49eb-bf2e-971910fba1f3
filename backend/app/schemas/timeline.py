"""
时间线相关的Pydantic模式
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field


class TimelineBase(BaseModel):
    """时间线基础模式"""
    entity_id: int = Field(..., description="实体ID")
    title: str = Field(..., min_length=1, max_length=200, description="标题")
    content: Optional[str] = Field(None, description="详细内容")
    event_date: datetime = Field(..., description="事件时间")
    original_time_text: Optional[str] = Field(None, max_length=100, description="原始时间文本")
    image_url: Optional[str] = Field(None, max_length=500, description="图片URL（可选）")
    video_url: Optional[str] = Field(None, max_length=500, description="视频URL（可选）")
    sort_order: int = Field(0, description="排序权重")


class TimelineCreate(TimelineBase):
    """创建时间线模式"""
    pass


class TimelineUpdate(BaseModel):
    """更新时间线模式"""
    entity_id: Optional[int] = Field(None, description="实体ID")
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="标题")
    content: Optional[str] = Field(None, description="详细内容")
    event_date: Optional[datetime] = Field(None, description="事件时间")
    image_url: Optional[str] = Field(None, min_length=1, max_length=500, description="图片URL")
    video_url: Optional[str] = Field(None, max_length=500, description="视频URL")
    sort_order: Optional[int] = Field(None, description="排序权重")


class TimelineResponse(TimelineBase):
    """时间线响应模式"""
    id: int = Field(..., description="时间线ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class TimelineListResponse(BaseModel):
    """时间线列表响应模式"""
    items: list[TimelineResponse] = Field(..., description="时间线列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class TimelineWithEntityResponse(TimelineResponse):
    """包含实体信息的时间线响应模式"""
    entity_name: str = Field(..., description="实体名称")
    entity_type: str = Field(..., description="实体类型")
    
    class Config:
        from_attributes = True
