"""
媒体文件管理相关的Pydantic模式
"""
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class MediaType(str, Enum):
    """媒体类型枚举"""
    IMAGE = "image"
    VIDEO = "video"
    DOCUMENT = "document"
    OTHER = "other"


class MediaCategory(str, Enum):
    """媒体分类枚举"""
    AVATAR = "avatar"          # 头像
    TIMELINE_IMAGE = "timeline_image"  # 时间线图片
    TIMELINE_VIDEO = "timeline_video"  # 时间线视频
    GENERAL = "general"        # 通用文件


class MediaFileResponse(BaseModel):
    """媒体文件响应模式"""
    id: int = Field(..., description="文件ID")
    filename: str = Field(..., description="文件名")
    original_filename: str = Field(..., description="原始文件名")
    file_path: str = Field(..., description="文件路径")
    file_url: str = Field(..., description="文件访问URL")
    file_size: int = Field(..., description="文件大小（字节）")
    mime_type: str = Field(..., description="MIME类型")
    media_type: MediaType = Field(..., description="媒体类型")
    category: MediaCategory = Field(..., description="媒体分类")
    description: Optional[str] = Field(None, description="文件描述")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class MediaFileListResponse(BaseModel):
    """媒体文件列表响应模式"""
    items: List[MediaFileResponse] = Field(..., description="文件列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class MediaUploadResponse(BaseModel):
    """媒体上传响应模式"""
    success: bool = Field(..., description="上传是否成功")
    message: str = Field(..., description="响应消息")
    file: Optional[MediaFileResponse] = Field(None, description="上传的文件信息")


class MediaBatchUploadResponse(BaseModel):
    """批量媒体上传响应模式"""
    success_count: int = Field(..., description="成功上传数量")
    failed_count: int = Field(..., description="失败上传数量")
    total_count: int = Field(..., description="总上传数量")
    success_files: List[MediaFileResponse] = Field(..., description="成功上传的文件")
    failed_files: List[dict] = Field(..., description="失败上传的文件信息")
    message: str = Field(..., description="响应消息")


class MediaUpdateRequest(BaseModel):
    """媒体文件更新请求模式"""
    description: Optional[str] = Field(None, description="文件描述")
    category: Optional[MediaCategory] = Field(None, description="媒体分类")


class MediaStatsResponse(BaseModel):
    """媒体统计响应模式"""
    total_files: int = Field(..., description="总文件数")
    total_size: int = Field(..., description="总文件大小（字节）")
    total_size_mb: float = Field(..., description="总文件大小（MB）")
    image_count: int = Field(..., description="图片文件数")
    video_count: int = Field(..., description="视频文件数")
    document_count: int = Field(..., description="文档文件数")
    other_count: int = Field(..., description="其他文件数")
    category_stats: dict = Field(..., description="分类统计")
    recent_uploads: List[MediaFileResponse] = Field(..., description="最近上传的文件")


class MediaValidationError(BaseModel):
    """媒体验证错误模式"""
    field: str = Field(..., description="错误字段")
    message: str = Field(..., description="错误消息")
    code: str = Field(..., description="错误代码")
