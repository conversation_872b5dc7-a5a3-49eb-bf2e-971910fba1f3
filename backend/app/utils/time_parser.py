"""
中文时间格式解析工具
"""
import re
from datetime import datetime
from typing import Optional


def parse_chinese_time(time_str: str) -> Optional[datetime]:
    """
    解析中文时间格式为datetime对象
    
    支持的格式：
    - 公元前XXX年
    - 公元XXX年
    - XXXX年
    - XXXX年XX月
    - XXXX年XX月XX日
    - 现代
    
    Args:
        time_str: 中文时间字符串
        
    Returns:
        datetime对象或None（如果解析失败）
    """
    if not time_str or not isinstance(time_str, str):
        return None
    
    time_str = time_str.strip()
    
    # 处理"现代"
    if time_str in ["现代", "当代", "今天", "现在"]:
        return datetime.now()
    
    # 公元前年份
    bc_pattern = r'公元前(\d+)年'
    match = re.search(bc_pattern, time_str)
    if match:
        year = int(match.group(1))
        # 公元前年份用负数表示，但datetime不支持负年份
        # 我们使用公元1年作为基准，公元前232年 = 公元1年 - 232年 = 公元前231年
        # 为了在数据库中存储，我们使用一个很小的年份来表示公元前
        # 这里使用年份1作为基准，公元前的年份存储为 1 - year
        if year <= 1:
            return datetime(1, 1, 1)
        else:
            # 对于公元前的年份，我们存储为公元1年，并在描述中保留原始信息
            return datetime(1, 1, 1)
    
    # 公元后年份（明确标注）
    ad_pattern = r'公元(\d+)年(?:(\d+)月)?(?:(\d+)日)?'
    match = re.search(ad_pattern, time_str)
    if match:
        year = int(match.group(1))
        month = int(match.group(2)) if match.group(2) else 1
        day = int(match.group(3)) if match.group(3) else 1
        
        # 确保年份在有效范围内
        if year < 1:
            year = 1
        elif year > 9999:
            year = 9999
            
        # 确保月份在有效范围内
        if month < 1:
            month = 1
        elif month > 12:
            month = 12
            
        # 确保日期在有效范围内
        if day < 1:
            day = 1
        elif day > 31:
            day = 31
            
        try:
            return datetime(year, month, day)
        except ValueError:
            # 如果日期无效（如2月30日），使用该月的第一天
            return datetime(year, month, 1)
    
    # 普通年份格式
    year_pattern = r'(\d{1,4})年(?:(\d{1,2})月)?(?:(\d{1,2})日)?'
    match = re.search(year_pattern, time_str)
    if match:
        year = int(match.group(1))
        month = int(match.group(2)) if match.group(2) else 1
        day = int(match.group(3)) if match.group(3) else 1
        
        # 确保年份在有效范围内
        if year < 1:
            year = 1
        elif year > 9999:
            year = 9999
            
        # 确保月份在有效范围内
        if month < 1:
            month = 1
        elif month > 12:
            month = 12
            
        # 确保日期在有效范围内
        if day < 1:
            day = 1
        elif day > 31:
            day = 31
            
        try:
            return datetime(year, month, day)
        except ValueError:
            # 如果日期无效，使用该月的第一天
            return datetime(year, month, 1)
    
    # 尝试解析纯数字年份
    number_pattern = r'^(\d{1,4})$'
    match = re.search(number_pattern, time_str)
    if match:
        year = int(match.group(1))
        if year < 1:
            year = 1
        elif year > 9999:
            year = 9999
        return datetime(year, 1, 1)
    
    # 如果都无法解析，返回None
    return None


def format_chinese_time_for_display(time_str: str) -> str:
    """
    格式化中文时间用于显示
    
    Args:
        time_str: 原始中文时间字符串
        
    Returns:
        格式化后的显示字符串
    """
    if not time_str:
        return "未知时间"
    
    # 对于公元前的时间，保持原样显示
    if "公元前" in time_str:
        return time_str
    
    # 对于其他时间，也保持原样
    return time_str


def is_bc_time(time_str: str) -> bool:
    """
    判断是否为公元前时间
    
    Args:
        time_str: 时间字符串
        
    Returns:
        是否为公元前时间
    """
    return "公元前" in time_str if time_str else False
