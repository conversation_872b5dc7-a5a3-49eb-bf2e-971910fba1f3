"""
中文时间格式解析工具
"""
import re
from datetime import datetime
from typing import Optional


def parse_flexible_time(time_str: str) -> Optional[datetime]:
    """
    灵活解析各种时间格式为datetime对象

    支持的格式：
    - 标准ISO格式：2025-07-29 14:11、2025-07-29、2025/07/29
    - 中文格式：公元前XXX年、公元XXX年、XXXX年、XXXX年XX月、XXXX年XX月XX日
    - 相对时间：现代、最近、今天等
    - 任何其他格式：如果无法解析，返回当前时间作为默认值

    Args:
        time_str: 时间字符串

    Returns:
        datetime对象，如果无法解析则返回当前时间
    """
    if not time_str or not isinstance(time_str, str):
        return datetime.now()

    time_str = time_str.strip()

    # 1. 尝试解析标准ISO格式
    iso_formats = [
        '%Y-%m-%d %H:%M:%S',  # 2025-07-29 14:11:30
        '%Y-%m-%d %H:%M',     # 2025-07-29 14:11
        '%Y-%m-%d',           # 2025-07-29
        '%Y/%m/%d %H:%M:%S',  # 2025/07/29 14:11:30
        '%Y/%m/%d %H:%M',     # 2025/07/29 14:11
        '%Y/%m/%d',           # 2025/07/29
        '%Y.%m.%d',           # 2025.07.29
        '%Y年%m月%d日',        # 2025年07月29日
        '%Y年%m月',            # 2025年07月
        '%Y年',               # 2025年
    ]

    for fmt in iso_formats:
        try:
            return datetime.strptime(time_str, fmt)
        except ValueError:
            continue

    # 2. 处理相对时间词汇
    relative_times = ["现代", "当代", "今天", "现在", "最近", "近期", "目前", "当前"]
    if any(word in time_str for word in relative_times):
        return datetime.now()
    
    # 3. 处理公元前年份
    bc_pattern = r'公元前(\d+)年'
    match = re.search(bc_pattern, time_str)
    if match:
        # 公元前年份统一存储为公元1年，原始文本会保存在original_time_text字段
        return datetime(1, 1, 1)
    
    # 4. 处理公元后年份（明确标注）
    ad_pattern = r'公元(\d+)年(?:(\d+)月)?(?:(\d+)日)?'
    match = re.search(ad_pattern, time_str)
    if match:
        year = int(match.group(1))
        month = int(match.group(2)) if match.group(2) else 1
        day = int(match.group(3)) if match.group(3) else 1
        return _safe_datetime(year, month, day)
    
    # 5. 处理普通年份格式
    year_pattern = r'(\d{1,4})年(?:(\d{1,2})月)?(?:(\d{1,2})日)?'
    match = re.search(year_pattern, time_str)
    if match:
        year = int(match.group(1))
        month = int(match.group(2)) if match.group(2) else 1
        day = int(match.group(3)) if match.group(3) else 1
        return _safe_datetime(year, month, day)

    # 6. 尝试解析纯数字年份
    number_pattern = r'^(\d{1,4})$'
    match = re.search(number_pattern, time_str)
    if match:
        year = int(match.group(1))
        return _safe_datetime(year, 1, 1)

    # 7. 如果都无法解析，返回当前时间作为默认值
    return datetime.now()


def _safe_datetime(year: int, month: int, day: int) -> datetime:
    """
    安全创建datetime对象，确保参数在有效范围内
    """
    # 确保年份在有效范围内
    if year < 1:
        year = 1
    elif year > 9999:
        year = 9999

    # 确保月份在有效范围内
    if month < 1:
        month = 1
    elif month > 12:
        month = 12

    # 确保日期在有效范围内
    if day < 1:
        day = 1
    elif day > 31:
        day = 31

    try:
        return datetime(year, month, day)
    except ValueError:
        # 如果日期无效（如2月30日），使用该月的第一天
        return datetime(year, month, 1)


# 保持向后兼容性
def parse_chinese_time(time_str: str) -> Optional[datetime]:
    """
    向后兼容的中文时间解析函数
    现在调用新的灵活解析函数
    """
    return parse_flexible_time(time_str)


def format_chinese_time_for_display(time_str: str) -> str:
    """
    格式化中文时间用于显示
    
    Args:
        time_str: 原始中文时间字符串
        
    Returns:
        格式化后的显示字符串
    """
    if not time_str:
        return "未知时间"
    
    # 对于公元前的时间，保持原样显示
    if "公元前" in time_str:
        return time_str
    
    # 对于其他时间，也保持原样
    return time_str


def is_bc_time(time_str: str) -> bool:
    """
    判断是否为公元前时间
    
    Args:
        time_str: 时间字符串
        
    Returns:
        是否为公元前时间
    """
    return "公元前" in time_str if time_str else False
