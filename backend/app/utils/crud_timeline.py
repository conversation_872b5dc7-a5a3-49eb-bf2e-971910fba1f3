"""
时间线CRUD操作
"""
from typing import Optional, List
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_
from ..models.timeline import Timeline
from ..models.life_entity import LifeEntity
from ..schemas.timeline import TimelineCreate, TimelineUpdate
from datetime import datetime


def get_timeline(db: Session, timeline_id: int) -> Optional[Timeline]:
    """
    根据ID获取时间线
    
    Args:
        db: 数据库会话
        timeline_id: 时间线ID
        
    Returns:
        时间线对象或None
    """
    return db.query(Timeline).filter(Timeline.id == timeline_id).first()


def get_timeline_with_entity(db: Session, timeline_id: int) -> Optional[Timeline]:
    """
    根据ID获取时间线（包含实体信息）
    
    Args:
        db: 数据库会话
        timeline_id: 时间线ID
        
    Returns:
        时间线对象（包含实体信息）或None
    """
    return db.query(Timeline).options(joinedload(Timeline.entity)).filter(Timeline.id == timeline_id).first()


def get_timelines(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    search: Optional[str] = None,
    entity_id: Optional[int] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
) -> tuple[List[Timeline], int]:
    """
    获取时间线列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 限制返回的记录数
        search: 搜索关键词
        entity_id: 实体ID过滤
        start_date: 开始日期过滤
        end_date: 结束日期过滤
        
    Returns:
        时间线列表和总数的元组
    """
    query = db.query(Timeline).options(joinedload(Timeline.entity))
    
    # 实体过滤
    if entity_id:
        query = query.filter(Timeline.entity_id == entity_id)
    
    # 日期范围过滤
    if start_date:
        query = query.filter(Timeline.event_date >= start_date)
    if end_date:
        query = query.filter(Timeline.event_date <= end_date)
    
    # 搜索过滤
    if search:
        query = query.filter(
            or_(
                Timeline.title.contains(search),
                Timeline.content.contains(search)
            )
        )
    
    # 获取总数
    total = query.count()
    
    # 排序和分页（按事件时间倒序，然后按排序权重）
    timelines = query.order_by(Timeline.event_date.desc(), Timeline.sort_order.desc()).offset(skip).limit(limit).all()
    
    return timelines, total


def get_timelines_by_entity(
    db: Session, 
    entity_id: int,
    skip: int = 0, 
    limit: int = 100
) -> tuple[List[Timeline], int]:
    """
    根据实体ID获取时间线列表
    
    Args:
        db: 数据库会话
        entity_id: 实体ID
        skip: 跳过的记录数
        limit: 限制返回的记录数
        
    Returns:
        时间线列表和总数的元组
    """
    query = db.query(Timeline).options(joinedload(Timeline.entity)).filter(Timeline.entity_id == entity_id)
    
    # 获取总数
    total = query.count()
    
    # 排序和分页（按事件时间升序，然后按排序权重）
    timelines = query.order_by(Timeline.event_date.asc(), Timeline.sort_order.desc()).offset(skip).limit(limit).all()
    
    return timelines, total


def create_timeline(db: Session, timeline: TimelineCreate) -> Timeline:
    """
    创建时间线
    
    Args:
        db: 数据库会话
        timeline: 时间线创建数据
        
    Returns:
        创建的时间线对象
    """
    db_timeline = Timeline(**timeline.model_dump())
    db.add(db_timeline)
    db.commit()
    db.refresh(db_timeline)
    return db_timeline


def update_timeline(db: Session, timeline_id: int, timeline: TimelineUpdate) -> Optional[Timeline]:
    """
    更新时间线
    
    Args:
        db: 数据库会话
        timeline_id: 时间线ID
        timeline: 时间线更新数据
        
    Returns:
        更新后的时间线对象或None
    """
    db_timeline = get_timeline(db, timeline_id)
    if not db_timeline:
        return None
    
    # 更新字段
    update_data = timeline.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_timeline, field, value)
    
    db.commit()
    db.refresh(db_timeline)
    return db_timeline


def delete_timeline(db: Session, timeline_id: int) -> bool:
    """
    删除时间线
    
    Args:
        db: 数据库会话
        timeline_id: 时间线ID
        
    Returns:
        是否删除成功
    """
    db_timeline = get_timeline(db, timeline_id)
    if not db_timeline:
        return False
    
    db.delete(db_timeline)
    db.commit()
    return True


def get_timeline_stats_by_entity(db: Session, entity_id: int) -> dict:
    """
    获取指定实体的时间线统计信息
    
    Args:
        db: 数据库会话
        entity_id: 实体ID
        
    Returns:
        统计信息字典
    """
    total_count = db.query(func.count(Timeline.id)).filter(Timeline.entity_id == entity_id).scalar()
    
    # 获取最早和最晚的事件时间
    earliest_event = db.query(func.min(Timeline.event_date)).filter(Timeline.entity_id == entity_id).scalar()
    latest_event = db.query(func.max(Timeline.event_date)).filter(Timeline.entity_id == entity_id).scalar()
    
    return {
        "total_count": total_count or 0,
        "earliest_event": earliest_event,
        "latest_event": latest_event
    }
