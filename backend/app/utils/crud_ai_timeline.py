"""
AI时间线批量处理CRUD操作
"""
from typing import List, Tuple, Optional
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from ..models.life_entity import LifeEntity, EntityType
from ..models.timeline import Timeline
from ..schemas.ai_timeline import AITimelineEvent, TimelineCreationResult
from ..utils.time_parser import parse_flexible_time, is_bc_time
from ..utils.crud_life_entity import get_life_entity_by_name
from ..utils.crud_category import get_category


def get_or_create_entity(
    db: Session,
    entity_name: str,
    category_id: int = 1,
    entity_type: str = "person",
    avatar_url: Optional[str] = None
) -> Tu<PERSON>[Optional[LifeEntity], bool]:
    """
    根据名称获取或创建实体

    Args:
        db: 数据库会话
        entity_name: 实体名称
        category_id: 分类ID
        entity_type: 实体类型
        avatar_url: 实体头像URL

    Returns:
        (实体对象, 是否为新创建) 的元组
    """
    try:
        print(f"正在查找或创建实体: {entity_name}, 分类ID: {category_id}, 类型: {entity_type}")

        # 首先尝试根据名称查找现有实体
        existing_entity = get_life_entity_by_name(db, entity_name)
        if existing_entity:
            print(f"找到现有实体: {existing_entity.id} - {existing_entity.name}")
            return existing_entity, False

        print(f"未找到现有实体，准备创建新实体")

        # 验证分类是否存在
        category = get_category(db, category_id)
        if not category:
            print(f"分类ID {category_id} 不存在，使用默认分类ID 1")
            # 如果分类不存在，使用默认分类ID 1
            category_id = 1
            category = get_category(db, category_id)
            if not category:
                print(f"默认分类ID 1 也不存在！")
                return None, False
        else:
            print(f"找到分类: {category.id} - {category.name}")

        # 验证实体类型
        try:
            entity_type_enum = EntityType(entity_type)
            print(f"实体类型验证成功: {entity_type_enum}")
        except ValueError as ve:
            print(f"实体类型 {entity_type} 无效，使用默认类型 PERSON: {ve}")
            entity_type_enum = EntityType.PERSON

        # 创建新实体
        print(f"准备创建新实体: name={entity_name}, category_id={category_id}, type={entity_type_enum}, avatar={avatar_url}")
        new_entity = LifeEntity(
            name=entity_name,
            category_id=category_id,
            type=entity_type_enum,
            avatar=avatar_url,  # 设置头像URL
            summary=f"通过AI批量导入创建的{entity_type_enum.value}实体",
            description=f"这是通过AI时间线批量导入功能创建的{entity_name}实体。"
        )

        db.add(new_entity)
        print(f"实体已添加到数据库会话，准备提交")
        db.commit()
        print(f"数据库提交成功")
        db.refresh(new_entity)
        print(f"新实体创建成功: {new_entity.id} - {new_entity.name}")

        return new_entity, True

    except SQLAlchemyError as e:
        db.rollback()
        print(f"创建实体时发生数据库错误: {e}")
        print(f"错误类型: {type(e)}")
        return None, False
    except Exception as e:
        db.rollback()
        print(f"创建实体时发生未知错误: {e}")
        print(f"错误类型: {type(e)}")
        return None, False


def create_timeline_from_ai_event(
    db: Session, 
    entity_id: int, 
    event: AITimelineEvent
) -> TimelineCreationResult:
    """
    根据AI事件创建时间线记录
    
    Args:
        db: 数据库会话
        entity_id: 实体ID
        event: AI事件数据
        
    Returns:
        时间线创建结果
    """
    try:
        # 使用灵活的时间解析，总是能返回一个有效的datetime对象
        parsed_datetime = parse_flexible_time(event.time)

        # 创建时间线记录，保存原始时间文本
        timeline = Timeline(
            entity_id=entity_id,
            title=event.desc[:200] if len(event.desc) > 200 else event.desc,  # 标题限制200字符
            content=event.desc,  # 完整描述作为内容
            event_date=parsed_datetime,
            original_time_text=event.time,  # 保存原始时间文本
            image_url=event.image,
            sort_order=0  # 默认排序权重
        )
        
        db.add(timeline)
        db.commit()
        db.refresh(timeline)
        
        return TimelineCreationResult(
            success=True,
            timeline_id=timeline.id,
            original_time=event.time,
            parsed_time=parsed_datetime.isoformat(),
            error=None
        )
        
    except SQLAlchemyError as e:
        db.rollback()
        return TimelineCreationResult(
            success=False,
            timeline_id=None,
            original_time=event.time,
            parsed_time=parsed_datetime.isoformat() if parsed_datetime else None,
            error=f"数据库错误: {str(e)}"
        )
    except Exception as e:
        db.rollback()
        return TimelineCreationResult(
            success=False,
            timeline_id=None,
            original_time=event.time,
            parsed_time=parsed_datetime.isoformat() if parsed_datetime else None,
            error=f"未知错误: {str(e)}"
        )


def batch_create_timelines(
    db: Session,
    entity_id: int,
    events: List[AITimelineEvent]
) -> List[TimelineCreationResult]:
    """
    批量创建时间线记录
    
    Args:
        db: 数据库会话
        entity_id: 实体ID
        events: AI事件列表
        
    Returns:
        创建结果列表
    """
    results = []
    
    for event in events:
        result = create_timeline_from_ai_event(db, entity_id, event)
        results.append(result)
    
    return results


def process_ai_timeline_data(
    db: Session,
    entity_name: str,
    events: List[AITimelineEvent],
    category_id: int = 1,
    entity_type: str = "person",
    avatar_url: Optional[str] = None
) -> Tuple[Optional[LifeEntity], bool, List[TimelineCreationResult]]:
    """
    处理AI时间线数据的完整流程

    Args:
        db: 数据库会话
        entity_name: 实体名称
        events: AI事件列表
        category_id: 分类ID
        entity_type: 实体类型
        avatar_url: 实体头像URL

    Returns:
        (实体对象, 是否为新创建实体, 时间线创建结果列表) 的元组
    """
    try:
        # 获取或创建实体
        entity, entity_created = get_or_create_entity(
            db, entity_name, category_id, entity_type, avatar_url
        )
        
        if not entity:
            return None, False, []
        
        # 批量创建时间线
        timeline_results = batch_create_timelines(db, entity.id, events)
        
        return entity, entity_created, timeline_results
        
    except Exception as e:
        print(f"处理AI时间线数据时发生错误: {e}")
        return None, False, []
