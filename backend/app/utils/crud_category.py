"""
分类CRUD操作
"""
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import func
from ..models.category import Category
from ..schemas.category import CategoryCreate, CategoryUpdate


def get_category(db: Session, category_id: int) -> Optional[Category]:
    """
    根据ID获取分类
    
    Args:
        db: 数据库会话
        category_id: 分类ID
        
    Returns:
        分类对象或None
    """
    return db.query(Category).filter(Category.id == category_id).first()


def get_category_by_name(db: Session, name: str) -> Optional[Category]:
    """
    根据名称获取分类
    
    Args:
        db: 数据库会话
        name: 分类名称
        
    Returns:
        分类对象或None
    """
    return db.query(Category).filter(Category.name == name).first()


def get_categories(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    search: Optional[str] = None
) -> tuple[List[Category], int]:
    """
    获取分类列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 限制返回的记录数
        search: 搜索关键词
        
    Returns:
        分类列表和总数的元组
    """
    query = db.query(Category)
    
    # 搜索过滤
    if search:
        query = query.filter(
            Category.name.contains(search) | 
            Category.description.contains(search)
        )
    
    # 获取总数
    total = query.count()
    
    # 排序和分页
    categories = query.order_by(Category.sort_order.desc(), Category.created_at.desc()).offset(skip).limit(limit).all()
    
    return categories, total


def create_category(db: Session, category: CategoryCreate) -> Category:
    """
    创建分类
    
    Args:
        db: 数据库会话
        category: 分类创建数据
        
    Returns:
        创建的分类对象
    """
    db_category = Category(**category.model_dump())
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    return db_category


def update_category(db: Session, category_id: int, category: CategoryUpdate) -> Optional[Category]:
    """
    更新分类
    
    Args:
        db: 数据库会话
        category_id: 分类ID
        category: 分类更新数据
        
    Returns:
        更新后的分类对象或None
    """
    db_category = get_category(db, category_id)
    if not db_category:
        return None
    
    # 更新字段
    update_data = category.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_category, field, value)
    
    db.commit()
    db.refresh(db_category)
    return db_category


def delete_category(db: Session, category_id: int) -> bool:
    """
    删除分类
    
    Args:
        db: 数据库会话
        category_id: 分类ID
        
    Returns:
        是否删除成功
    """
    db_category = get_category(db, category_id)
    if not db_category:
        return False
    
    # 检查是否有关联的生命实体
    from ..models.life_entity import LifeEntity
    entity_count = db.query(func.count(LifeEntity.id)).filter(LifeEntity.category_id == category_id).scalar()
    if entity_count > 0:
        return False  # 有关联数据，不能删除
    
    db.delete(db_category)
    db.commit()
    return True
