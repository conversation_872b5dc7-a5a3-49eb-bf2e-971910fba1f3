from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, or_
from typing import Optional, List
import math

from app.models.friend_link import FriendLink
from app.schemas.friend_link import FriendLinkCreate, FriendLinkUpdate

def get_friend_links(
    db: Session,
    page: int = 1,
    size: int = 10,
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
    sort_by: str = "sort_order",
    sort_order: str = "desc"
):
    """获取友情链接列表（分页）"""
    query = db.query(FriendLink)
    
    # 搜索过滤
    if search:
        query = query.filter(
            or_(
                FriendLink.name.contains(search),
                FriendLink.description.contains(search)
            )
        )
    
    # 状态过滤
    if is_active is not None:
        query = query.filter(FriendLink.is_active == is_active)
    
    # 排序
    if sort_by == "sort_order":
        if sort_order == "desc":
            query = query.order_by(desc(FriendLink.sort_order), desc(FriendLink.created_at))
        else:
            query = query.order_by(asc(FriendLink.sort_order), asc(FriendLink.created_at))
    elif sort_by == "created_at":
        if sort_order == "desc":
            query = query.order_by(desc(FriendLink.created_at))
        else:
            query = query.order_by(asc(FriendLink.created_at))
    elif sort_by == "name":
        if sort_order == "desc":
            query = query.order_by(desc(FriendLink.name))
        else:
            query = query.order_by(asc(FriendLink.name))
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * size
    items = query.offset(offset).limit(size).all()
    
    # 计算总页数
    pages = math.ceil(total / size) if total > 0 else 1
    
    return {
        "items": items,
        "total": total,
        "page": page,
        "size": size,
        "pages": pages
    }

def get_active_friend_links(db: Session) -> List[FriendLink]:
    """获取所有启用的友情链接（按排序权重排序）"""
    return db.query(FriendLink).filter(
        FriendLink.is_active == True
    ).order_by(
        desc(FriendLink.sort_order),
        desc(FriendLink.created_at)
    ).all()

def get_friend_link_by_id(db: Session, friend_link_id: int) -> Optional[FriendLink]:
    """根据ID获取友情链接"""
    return db.query(FriendLink).filter(FriendLink.id == friend_link_id).first()

def create_friend_link(db: Session, friend_link: FriendLinkCreate) -> FriendLink:
    """创建友情链接"""
    db_friend_link = FriendLink(**friend_link.model_dump())
    db.add(db_friend_link)
    db.commit()
    db.refresh(db_friend_link)
    return db_friend_link

def update_friend_link(
    db: Session, 
    friend_link_id: int, 
    friend_link_update: FriendLinkUpdate
) -> Optional[FriendLink]:
    """更新友情链接"""
    db_friend_link = get_friend_link_by_id(db, friend_link_id)
    if not db_friend_link:
        return None
    
    update_data = friend_link_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_friend_link, field, value)
    
    db.commit()
    db.refresh(db_friend_link)
    return db_friend_link

def delete_friend_link(db: Session, friend_link_id: int) -> bool:
    """删除友情链接"""
    db_friend_link = get_friend_link_by_id(db, friend_link_id)
    if not db_friend_link:
        return False
    
    db.delete(db_friend_link)
    db.commit()
    return True

def get_friend_links_stats(db: Session) -> dict:
    """获取友情链接统计信息"""
    total_count = db.query(FriendLink).count()
    active_count = db.query(FriendLink).filter(FriendLink.is_active == True).count()
    inactive_count = total_count - active_count
    
    return {
        "total_count": total_count,
        "active_count": active_count,
        "inactive_count": inactive_count
    }
