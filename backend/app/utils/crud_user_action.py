"""
用户行为统计CRUD操作
"""
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_, desc, distinct, text
from datetime import datetime, timedelta
from ..models.user_action import UserAction, ActionType
from ..models.life_entity import LifeEntity
from ..models.category import Category


def get_user_actions(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    entity_id: Optional[int] = None,
    action_type: Optional[ActionType] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
) -> tuple[List[UserAction], int]:
    """
    获取用户行为列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 限制返回的记录数
        entity_id: 实体ID过滤
        action_type: 行为类型过滤
        start_date: 开始日期过滤
        end_date: 结束日期过滤
        
    Returns:
        用户行为列表和总数的元组
    """
    query = db.query(UserAction)
    
    # 实体过滤
    if entity_id:
        query = query.filter(UserAction.entity_id == entity_id)
    
    # 行为类型过滤
    if action_type:
        query = query.filter(UserAction.action_type == action_type)
    
    # 日期范围过滤
    if start_date:
        query = query.filter(UserAction.created_at >= start_date)
    if end_date:
        query = query.filter(UserAction.created_at <= end_date)
    
    # 获取总数
    total = query.count()
    
    # 排序和分页（按创建时间倒序）
    actions = query.order_by(UserAction.created_at.desc()).offset(skip).limit(limit).all()
    
    return actions, total


def get_entity_action_stats(db: Session, entity_id: int) -> Dict[str, Any]:
    """
    获取指定实体的行为统计
    
    Args:
        db: 数据库会话
        entity_id: 实体ID
        
    Returns:
        统计信息字典
    """
    # 获取实体信息
    entity = db.query(LifeEntity).filter(LifeEntity.id == entity_id).first()
    if not entity:
        return None
    
    # 统计各类行为数量
    stats = db.query(
        UserAction.action_type,
        func.count(UserAction.id).label('count')
    ).filter(UserAction.entity_id == entity_id).group_by(UserAction.action_type).all()
    
    # 初始化统计数据
    view_count = like_count = flower_count = 0
    for stat in stats:
        if stat.action_type == ActionType.VIEW:
            view_count = stat.count
        elif stat.action_type == ActionType.LIKE:
            like_count = stat.count
        elif stat.action_type == ActionType.FLOWER:
            flower_count = stat.count
    
    # 统计独立访客数（基于IP地址）
    unique_visitors = db.query(func.count(distinct(UserAction.ip_address))).filter(
        UserAction.entity_id == entity_id,
        UserAction.ip_address.isnot(None)
    ).scalar() or 0
    
    # 获取最近10条行为记录
    recent_actions = db.query(UserAction).filter(
        UserAction.entity_id == entity_id
    ).order_by(UserAction.created_at.desc()).limit(10).all()
    
    return {
        "entity_id": entity_id,
        "entity_name": entity.name,
        "total_actions": view_count + like_count + flower_count,
        "view_count": view_count,
        "like_count": like_count,
        "flower_count": flower_count,
        "unique_visitors": unique_visitors,
        "recent_actions": recent_actions
    }


def get_global_stats(db: Session) -> Dict[str, Any]:
    """
    获取全局统计信息
    
    Args:
        db: 数据库会话
        
    Returns:
        全局统计信息字典
    """
    # 统计总实体数
    total_entities = db.query(func.count(LifeEntity.id)).scalar() or 0
    
    # 统计各类行为总数
    action_stats = db.query(
        UserAction.action_type,
        func.count(UserAction.id).label('count')
    ).group_by(UserAction.action_type).all()
    
    total_views = total_likes = total_flowers = 0
    for stat in action_stats:
        if stat.action_type == ActionType.VIEW:
            total_views = stat.count
        elif stat.action_type == ActionType.LIKE:
            total_likes = stat.count
        elif stat.action_type == ActionType.FLOWER:
            total_flowers = stat.count
    
    total_actions = total_views + total_likes + total_flowers
    
    # 统计独立访客数
    unique_visitors = db.query(func.count(distinct(UserAction.ip_address))).filter(
        UserAction.ip_address.isnot(None)
    ).scalar() or 0
    
    # 获取热门实体（按总互动数排序）
    popular_entities = db.query(
        LifeEntity.id,
        LifeEntity.name,
        LifeEntity.type,
        (LifeEntity.view_count + LifeEntity.like_count + LifeEntity.flower_count).label('total_interactions')
    ).order_by(desc('total_interactions')).limit(10).all()
    
    popular_list = []
    for entity in popular_entities:
        popular_list.append({
            "id": entity.id,
            "name": entity.name,
            "type": entity.type.value,
            "total_interactions": entity.total_interactions
        })
    
    return {
        "total_entities": total_entities,
        "total_actions": total_actions,
        "total_views": total_views,
        "total_likes": total_likes,
        "total_flowers": total_flowers,
        "unique_visitors": unique_visitors,
        "popular_entities": popular_list
    }


def get_action_trends(
    db: Session, 
    entity_id: Optional[int] = None,
    period: str = "daily",
    days: int = 30
) -> List[Dict[str, Any]]:
    """
    获取行为趋势数据
    
    Args:
        db: 数据库会话
        entity_id: 实体ID（可选，为None时获取全局趋势）
        period: 统计周期（daily/weekly/monthly）
        days: 统计天数
        
    Returns:
        趋势数据列表
    """
    # 计算开始日期
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    # 构建基础查询
    query = db.query(UserAction).filter(
        UserAction.created_at >= start_date,
        UserAction.created_at <= end_date
    )
    
    if entity_id:
        query = query.filter(UserAction.entity_id == entity_id)
    
    # 根据周期类型构建日期格式
    if period == "daily":
        date_format = "%Y-%m-%d"
        date_trunc = func.date(UserAction.created_at)
    elif period == "weekly":
        date_format = "%Y-W%U"
        date_trunc = func.date_format(UserAction.created_at, "%Y-W%U")
    else:  # monthly
        date_format = "%Y-%m"
        date_trunc = func.date_format(UserAction.created_at, "%Y-%m")
    
    # 执行统计查询
    trends = query.with_entities(
        date_trunc.label('date'),
        UserAction.action_type,
        func.count(UserAction.id).label('count')
    ).group_by('date', UserAction.action_type).all()
    
    # 组织数据
    trend_dict = {}
    for trend in trends:
        date_str = trend.date.strftime(date_format) if hasattr(trend.date, 'strftime') else str(trend.date)
        if date_str not in trend_dict:
            trend_dict[date_str] = {
                "date": date_str,
                "view_count": 0,
                "like_count": 0,
                "flower_count": 0,
                "total_count": 0
            }
        
        if trend.action_type == ActionType.VIEW:
            trend_dict[date_str]["view_count"] = trend.count
        elif trend.action_type == ActionType.LIKE:
            trend_dict[date_str]["like_count"] = trend.count
        elif trend.action_type == ActionType.FLOWER:
            trend_dict[date_str]["flower_count"] = trend.count
        
        trend_dict[date_str]["total_count"] += trend.count
    
    # 转换为列表并排序
    trend_list = list(trend_dict.values())
    trend_list.sort(key=lambda x: x["date"])
    
    return trend_list


def get_visitor_stats(db: Session) -> Dict[str, Any]:
    """
    获取访客统计信息
    
    Args:
        db: 数据库会话
        
    Returns:
        访客统计信息字典
    """
    # 统计总访客数
    total_visitors = db.query(func.count(distinct(UserAction.ip_address))).filter(
        UserAction.ip_address.isnot(None)
    ).scalar() or 0
    
    # 统计今日新访客数
    today = datetime.now().date()
    today_start = datetime.combine(today, datetime.min.time())
    
    new_visitors_today = db.query(func.count(distinct(UserAction.ip_address))).filter(
        UserAction.created_at >= today_start,
        UserAction.ip_address.isnot(None)
    ).scalar() or 0
    
    # 统计回访访客数（有多次访问记录的IP）
    returning_visitors = db.query(func.count()).select_from(
        db.query(UserAction.ip_address).filter(
            UserAction.ip_address.isnot(None)
        ).group_by(UserAction.ip_address).having(func.count(UserAction.id) > 1).subquery()
    ).scalar() or 0
    
    # 统计用户代理（简化版本，只取前10个）
    top_user_agents = db.query(
        UserAction.user_agent,
        func.count(UserAction.id).label('count')
    ).filter(
        UserAction.user_agent.isnot(None)
    ).group_by(UserAction.user_agent).order_by(desc('count')).limit(10).all()
    
    user_agent_list = []
    for ua in top_user_agents:
        # 简化用户代理字符串显示
        ua_short = ua.user_agent[:50] + "..." if len(ua.user_agent) > 50 else ua.user_agent
        user_agent_list.append({
            "user_agent": ua_short,
            "count": ua.count
        })
    
    return {
        "total_visitors": total_visitors,
        "new_visitors_today": new_visitors_today,
        "returning_visitors": returning_visitors,
        "top_countries": [],  # 需要IP地理位置服务，暂时返回空列表
        "top_user_agents": user_agent_list
    }


def get_dashboard_comprehensive_stats(db: Session) -> Dict[str, Any]:
    """
    获取管理后台仪表盘的综合统计数据

    Args:
        db: 数据库会话

    Returns:
        综合统计信息字典
    """
    from ..models.timeline import Timeline
    from ..models.media_file import MediaFile

    # 获取基础全局统计
    global_stats = get_global_stats(db)

    # 统计时间线总数
    total_timelines = db.query(func.count(Timeline.id)).scalar() or 0

    # 统计分类总数
    total_categories = db.query(func.count(Category.id)).scalar() or 0

    # 统计媒体文件总数和大小
    media_stats = db.query(
        func.count(MediaFile.id).label('total_files'),
        func.sum(MediaFile.file_size).label('total_size')
    ).first()

    total_media_files = media_stats.total_files or 0
    total_media_size = media_stats.total_size or 0

    # 统计各类型实体数量
    entity_type_stats = db.query(
        LifeEntity.type,
        func.count(LifeEntity.id).label('count')
    ).group_by(LifeEntity.type).all()

    entity_types = {}
    for stat in entity_type_stats:
        entity_types[stat.type.value] = stat.count

    # 获取最近7天的内容增长趋势
    seven_days_ago = datetime.now() - timedelta(days=7)

    # 实体增长趋势
    entity_growth = db.query(
        func.date(LifeEntity.created_at).label('date'),
        func.count(LifeEntity.id).label('count')
    ).filter(
        LifeEntity.created_at >= seven_days_ago
    ).group_by(func.date(LifeEntity.created_at)).all()

    # 时间线增长趋势
    timeline_growth = db.query(
        func.date(Timeline.created_at).label('date'),
        func.count(Timeline.id).label('count')
    ).filter(
        Timeline.created_at >= seven_days_ago
    ).group_by(func.date(Timeline.created_at)).all()

    # 用户行为趋势（最近7天）
    action_growth = db.query(
        func.date(UserAction.created_at).label('date'),
        UserAction.action_type,
        func.count(UserAction.id).label('count')
    ).filter(
        UserAction.created_at >= seven_days_ago
    ).group_by(func.date(UserAction.created_at), UserAction.action_type).all()

    # 组织增长趋势数据
    growth_trends = {}
    for i in range(7):
        date = (datetime.now() - timedelta(days=6-i)).date()
        date_str = date.strftime('%Y-%m-%d')
        growth_trends[date_str] = {
            'date': date_str,
            'entities': 0,
            'timelines': 0,
            'views': 0,
            'likes': 0,
            'flowers': 0
        }

    # 填充实体增长数据
    for growth in entity_growth:
        date_str = growth.date.strftime('%Y-%m-%d')
        if date_str in growth_trends:
            growth_trends[date_str]['entities'] = growth.count

    # 填充时间线增长数据
    for growth in timeline_growth:
        date_str = growth.date.strftime('%Y-%m-%d')
        if date_str in growth_trends:
            growth_trends[date_str]['timelines'] = growth.count

    # 填充用户行为数据
    for growth in action_growth:
        date_str = growth.date.strftime('%Y-%m-%d')
        if date_str in growth_trends:
            if growth.action_type == ActionType.VIEW:
                growth_trends[date_str]['views'] = growth.count
            elif growth.action_type == ActionType.LIKE:
                growth_trends[date_str]['likes'] = growth.count
            elif growth.action_type == ActionType.FLOWER:
                growth_trends[date_str]['flowers'] = growth.count

    growth_trend_list = list(growth_trends.values())
    growth_trend_list.sort(key=lambda x: x['date'])

    # 获取最活跃的分类（按实体数量）
    active_categories = db.query(
        Category.id,
        Category.name,
        func.count(LifeEntity.id).label('entity_count')
    ).outerjoin(LifeEntity).group_by(Category.id, Category.name).order_by(desc('entity_count')).limit(5).all()

    category_stats = []
    for cat in active_categories:
        category_stats.append({
            'id': cat.id,
            'name': cat.name,
            'entity_count': cat.entity_count
        })

    return {
        # 核心业务指标
        'overview': {
            'total_entities': global_stats['total_entities'],
            'total_timelines': total_timelines,
            'total_categories': total_categories,
            'total_media_files': total_media_files,
            'total_media_size_mb': round(total_media_size / (1024 * 1024), 2) if total_media_size else 0
        },

        # 用户行为统计
        'user_actions': {
            'total_views': global_stats['total_views'],
            'total_likes': global_stats['total_likes'],
            'total_flowers': global_stats['total_flowers'],
            'unique_visitors': global_stats['unique_visitors']
        },

        # 实体类型分布
        'entity_types': entity_types,

        # 增长趋势（最近7天）
        'growth_trends': growth_trend_list,

        # 热门内容
        'popular_entities': global_stats['popular_entities'][:5],  # 只取前5个

        # 活跃分类
        'active_categories': category_stats
    }
