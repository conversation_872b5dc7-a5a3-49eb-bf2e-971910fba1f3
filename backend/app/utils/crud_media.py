"""
媒体文件CRUD操作
"""
import os
import uuid
import shutil
from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from fastapi import UploadFile
from ..models.media_file import MediaFile, MediaType, MediaCategory
from ..config import settings


def get_media_type_from_mime(mime_type: str) -> MediaType:
    """
    根据MIME类型判断媒体类型
    
    Args:
        mime_type: MIME类型
        
    Returns:
        媒体类型枚举
    """
    if mime_type.startswith('image/'):
        return MediaType.IMAGE
    elif mime_type.startswith('video/'):
        return MediaType.VIDEO
    elif mime_type.startswith('application/') or mime_type.startswith('text/'):
        return MediaType.DOCUMENT
    else:
        return MediaType.OTHER


def generate_unique_filename(original_filename: str) -> str:
    """
    生成唯一的文件名
    
    Args:
        original_filename: 原始文件名
        
    Returns:
        唯一文件名
    """
    # 获取文件扩展名
    file_ext = os.path.splitext(original_filename)[1].lower()
    # 生成UUID作为文件名
    unique_name = str(uuid.uuid4())
    return f"{unique_name}{file_ext}"


def get_upload_path(media_type: MediaType, category: MediaCategory) -> str:
    """
    获取上传路径
    
    Args:
        media_type: 媒体类型
        category: 媒体分类
        
    Returns:
        上传路径
    """
    # 按年月组织目录结构
    now = datetime.now()
    year_month = now.strftime("%Y/%m")
    
    # 根据媒体类型和分类创建目录结构
    base_path = f"{media_type.value}/{category.value}/{year_month}"
    return base_path


def save_upload_file(upload_file: UploadFile, category: MediaCategory = MediaCategory.GENERAL) -> Dict[str, Any]:
    """
    保存上传的文件
    
    Args:
        upload_file: 上传的文件
        category: 媒体分类
        
    Returns:
        文件信息字典
    """
    # 确定媒体类型
    media_type = get_media_type_from_mime(upload_file.content_type)
    
    # 生成唯一文件名
    unique_filename = generate_unique_filename(upload_file.filename)
    
    # 获取上传路径
    upload_path = get_upload_path(media_type, category)
    
    # 创建完整的文件路径（使用backend/uploads文件夹）
    app_dir = os.path.dirname(__file__)  # 当前文件所在目录 backend/app/utils
    backend_dir = os.path.dirname(os.path.dirname(app_dir))  # 回到backend目录
    uploads_dir = os.path.join(backend_dir, "uploads")
    full_upload_path = os.path.join(uploads_dir, upload_path)
    os.makedirs(full_upload_path, exist_ok=True)

    file_path = os.path.join(full_upload_path, unique_filename)
    
    # 保存文件
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(upload_file.file, buffer)
    
    # 获取文件大小
    file_size = os.path.getsize(file_path)
    
    return {
        "filename": unique_filename,
        "original_filename": upload_file.filename,
        "file_path": file_path,
        "file_size": file_size,
        "mime_type": upload_file.content_type,
        "media_type": media_type,
        "category": category
    }


def create_media_file(db: Session, file_info: Dict[str, Any], description: Optional[str] = None) -> MediaFile:
    """
    创建媒体文件记录
    
    Args:
        db: 数据库会话
        file_info: 文件信息
        description: 文件描述
        
    Returns:
        创建的媒体文件对象
    """
    media_file = MediaFile(
        filename=file_info["filename"],
        original_filename=file_info["original_filename"],
        file_path=file_info["file_path"],
        file_size=file_info["file_size"],
        mime_type=file_info["mime_type"],
        media_type=file_info["media_type"],
        category=file_info["category"],
        description=description
    )
    
    db.add(media_file)
    db.commit()
    db.refresh(media_file)
    
    return media_file


def get_media_file(db: Session, file_id: int) -> Optional[MediaFile]:
    """
    根据ID获取媒体文件
    
    Args:
        db: 数据库会话
        file_id: 文件ID
        
    Returns:
        媒体文件对象或None
    """
    return db.query(MediaFile).filter(MediaFile.id == file_id).first()


def get_media_files(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    media_type: Optional[MediaType] = None,
    category: Optional[MediaCategory] = None,
    search: Optional[str] = None
) -> tuple[List[MediaFile], int]:
    """
    获取媒体文件列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 限制返回的记录数
        media_type: 媒体类型过滤
        category: 媒体分类过滤
        search: 搜索关键词
        
    Returns:
        媒体文件列表和总数的元组
    """
    query = db.query(MediaFile)
    
    # 媒体类型过滤
    if media_type:
        query = query.filter(MediaFile.media_type == media_type)
    
    # 媒体分类过滤
    if category:
        query = query.filter(MediaFile.category == category)
    
    # 搜索过滤
    if search:
        query = query.filter(
            MediaFile.original_filename.contains(search) |
            MediaFile.description.contains(search)
        )
    
    # 获取总数
    total = query.count()
    
    # 排序和分页（按创建时间倒序）
    files = query.order_by(MediaFile.created_at.desc()).offset(skip).limit(limit).all()
    
    return files, total


def update_media_file(db: Session, file_id: int, description: Optional[str] = None, category: Optional[MediaCategory] = None) -> Optional[MediaFile]:
    """
    更新媒体文件信息
    
    Args:
        db: 数据库会话
        file_id: 文件ID
        description: 文件描述
        category: 媒体分类
        
    Returns:
        更新后的媒体文件对象或None
    """
    media_file = get_media_file(db, file_id)
    if not media_file:
        return None
    
    if description is not None:
        media_file.description = description
    if category is not None:
        media_file.category = category
    
    db.commit()
    db.refresh(media_file)
    
    return media_file


def delete_media_file(db: Session, file_id: int) -> bool:
    """
    删除媒体文件
    
    Args:
        db: 数据库会话
        file_id: 文件ID
        
    Returns:
        是否删除成功
    """
    media_file = get_media_file(db, file_id)
    if not media_file:
        return False
    
    # 删除物理文件
    try:
        if os.path.exists(media_file.file_path):
            os.remove(media_file.file_path)
    except Exception:
        pass  # 即使物理文件删除失败，也继续删除数据库记录
    
    # 删除数据库记录
    db.delete(media_file)
    db.commit()
    
    return True


def get_media_stats(db: Session) -> Dict[str, Any]:
    """
    获取媒体文件统计信息
    
    Args:
        db: 数据库会话
        
    Returns:
        统计信息字典
    """
    # 总文件数和总大小
    total_files = db.query(func.count(MediaFile.id)).scalar() or 0
    total_size = db.query(func.sum(MediaFile.file_size)).scalar() or 0
    
    # 按媒体类型统计
    type_stats = db.query(
        MediaFile.media_type,
        func.count(MediaFile.id).label('count')
    ).group_by(MediaFile.media_type).all()
    
    image_count = video_count = document_count = other_count = 0
    for stat in type_stats:
        if stat.media_type == MediaType.IMAGE:
            image_count = stat.count
        elif stat.media_type == MediaType.VIDEO:
            video_count = stat.count
        elif stat.media_type == MediaType.DOCUMENT:
            document_count = stat.count
        elif stat.media_type == MediaType.OTHER:
            other_count = stat.count
    
    # 按分类统计
    category_stats = db.query(
        MediaFile.category,
        func.count(MediaFile.id).label('count')
    ).group_by(MediaFile.category).all()
    
    category_dict = {}
    for stat in category_stats:
        category_dict[stat.category.value] = stat.count
    
    # 最近上传的文件
    recent_uploads = db.query(MediaFile).order_by(
        MediaFile.created_at.desc()
    ).limit(10).all()
    
    return {
        "total_files": total_files,
        "total_size": total_size,
        "total_size_mb": round(total_size / (1024 * 1024), 2),
        "image_count": image_count,
        "video_count": video_count,
        "document_count": document_count,
        "other_count": other_count,
        "category_stats": category_dict,
        "recent_uploads": recent_uploads
    }
