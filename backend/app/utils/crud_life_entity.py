"""
生命实体CRUD操作
"""
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from ..models.life_entity import LifeEntity, EntityType
from ..models.user_action import UserAction, ActionType
from ..schemas.life_entity import LifeEntityCreate, LifeEntityUpdate
from datetime import datetime


def get_life_entity(db: Session, entity_id: int) -> Optional[LifeEntity]:
    """
    根据ID获取生命实体
    
    Args:
        db: 数据库会话
        entity_id: 实体ID
        
    Returns:
        生命实体对象或None
    """
    return db.query(LifeEntity).filter(LifeEntity.id == entity_id).first()


def get_life_entity_by_name(db: Session, name: str, category_id: Optional[int] = None) -> Optional[LifeEntity]:
    """
    根据名称获取生命实体
    
    Args:
        db: 数据库会话
        name: 实体名称
        category_id: 分类ID（可选，用于限制搜索范围）
        
    Returns:
        生命实体对象或None
    """
    query = db.query(LifeEntity).filter(LifeEntity.name == name)
    if category_id:
        query = query.filter(LifeEntity.category_id == category_id)
    return query.first()


def get_life_entities(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    search: Optional[str] = None,
    category_id: Optional[int] = None,
    entity_type: Optional[EntityType] = None
) -> tuple[List[LifeEntity], int]:
    """
    获取生命实体列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 限制返回的记录数
        search: 搜索关键词
        category_id: 分类ID过滤
        entity_type: 实体类型过滤
        
    Returns:
        生命实体列表和总数的元组
    """
    query = db.query(LifeEntity)
    
    # 分类过滤
    if category_id:
        query = query.filter(LifeEntity.category_id == category_id)
    
    # 类型过滤
    if entity_type:
        query = query.filter(LifeEntity.type == entity_type)
    
    # 搜索过滤
    if search:
        query = query.filter(
            or_(
                LifeEntity.name.contains(search),
                LifeEntity.summary.contains(search),
                LifeEntity.description.contains(search)
            )
        )
    
    # 获取总数
    total = query.count()
    
    # 排序和分页（按创建时间倒序）
    entities = query.order_by(LifeEntity.created_at.desc()).offset(skip).limit(limit).all()
    
    return entities, total


def create_life_entity(db: Session, entity: LifeEntityCreate) -> LifeEntity:
    """
    创建生命实体
    
    Args:
        db: 数据库会话
        entity: 生命实体创建数据
        
    Returns:
        创建的生命实体对象
    """
    db_entity = LifeEntity(**entity.model_dump())
    db.add(db_entity)
    db.commit()
    db.refresh(db_entity)
    return db_entity


def update_life_entity(db: Session, entity_id: int, entity: LifeEntityUpdate) -> Optional[LifeEntity]:
    """
    更新生命实体
    
    Args:
        db: 数据库会话
        entity_id: 实体ID
        entity: 生命实体更新数据
        
    Returns:
        更新后的生命实体对象或None
    """
    db_entity = get_life_entity(db, entity_id)
    if not db_entity:
        return None
    
    # 更新字段
    update_data = entity.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_entity, field, value)
    
    db.commit()
    db.refresh(db_entity)
    return db_entity


def delete_life_entity(db: Session, entity_id: int) -> bool:
    """
    删除生命实体
    
    Args:
        db: 数据库会话
        entity_id: 实体ID
        
    Returns:
        是否删除成功
    """
    db_entity = get_life_entity(db, entity_id)
    if not db_entity:
        return False
    
    # 删除实体（关联的时间线和用户行为会通过级联删除自动处理）
    db.delete(db_entity)
    db.commit()
    return True


def increment_view_count(db: Session, entity_id: int, user_ip: str, user_agent: str) -> Optional[LifeEntity]:
    """
    增加浏览次数并记录用户行为
    
    Args:
        db: 数据库会话
        entity_id: 实体ID
        user_ip: 用户IP
        user_agent: 用户代理
        
    Returns:
        更新后的生命实体对象或None
    """
    db_entity = get_life_entity(db, entity_id)
    if not db_entity:
        return None
    
    # 增加浏览次数
    db_entity.view_count += 1
    
    # 记录用户行为
    user_action = UserAction(
        entity_id=entity_id,
        action_type=ActionType.VIEW,
        ip_address=user_ip,
        user_agent=user_agent
    )
    db.add(user_action)
    
    db.commit()
    db.refresh(db_entity)
    return db_entity


def handle_user_interaction(
    db: Session, 
    entity_id: int, 
    action: str, 
    user_ip: str, 
    user_agent: str
) -> Optional[LifeEntity]:
    """
    处理用户互动（点赞、送花）
    
    Args:
        db: 数据库会话
        entity_id: 实体ID
        action: 互动类型（like/flower）
        user_ip: 用户IP
        user_agent: 用户代理
        
    Returns:
        更新后的生命实体对象或None
    """
    db_entity = get_life_entity(db, entity_id)
    if not db_entity:
        return None
    
    # 根据动作类型更新计数
    if action == "like":
        db_entity.like_count += 1
        action_type = ActionType.LIKE
    elif action == "flower":
        db_entity.flower_count += 1
        action_type = ActionType.FLOWER
    else:
        return None  # 无效的动作类型
    
    # 记录用户行为
    user_action = UserAction(
        entity_id=entity_id,
        action_type=action_type,
        ip_address=user_ip,
        user_agent=user_agent
    )
    db.add(user_action)
    
    db.commit()
    db.refresh(db_entity)
    return db_entity
