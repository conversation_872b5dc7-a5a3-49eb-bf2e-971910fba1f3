from sqlalchemy.orm import Session
from app.models.about_us import AboutUs
from app.schemas.about_us import AboutUsCreate, AboutUsUpdate

def get_about_us(db: Session):
    """
    获取“关于我们”的设置。由于通常只有一个，我们获取第一个。
    """
    return db.query(AboutUs).first()

def create_or_update_about_us(db: Session, about_us: AboutUsCreate) -> AboutUs:
    """
    创建或更新“关于我们”的设置。
    如果不存在，则创建；如果存在，则更新。
    """
    db_about_us = get_about_us(db)
    if db_about_us:
        update_data = about_us.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_about_us, key, value)
        db.commit()
        db.refresh(db_about_us)
        return db_about_us
    else:
        db_about_us = AboutUs(**about_us.model_dump())
        db.add(db_about_us)
        db.commit()
        db.refresh(db_about_us)
        return db_about_us
