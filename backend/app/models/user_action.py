"""
用户行为统计数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..database import Base
import enum


class ActionType(enum.Enum):
    """行为类型枚举"""
    VIEW = "view"
    LIKE = "like"
    FLOWER = "flower"


class UserAction(Base):
    """用户行为统计表模型"""
    __tablename__ = "user_actions"

    id = Column(Integer, primary_key=True, index=True, comment="主键")
    entity_id = Column(Integer, ForeignKey("life_entities.id"), nullable=False, comment="实体ID")
    action_type = Column(Enum(ActionType), nullable=False, comment="行为类型（view/like/flower）")
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    # 关联关系
    entity = relationship("LifeEntity", back_populates="user_actions")

    def __repr__(self):
        return f"<UserAction(id={self.id}, action_type='{self.action_type.value}', entity_id={self.entity_id})>"
