"""
生命实体数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..database import Base
import enum


class EntityType(enum.Enum):
    """实体类型枚举"""
    PERSON = "person"
    EVENT = "event"
    ENTERPRISE = "enterprise"


class LifeEntity(Base):
    """生命实体表模型"""
    __tablename__ = "life_entities"

    id = Column(Integer, primary_key=True, index=True, comment="主键")
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=False, comment="分类ID")
    type = Column(Enum(EntityType), nullable=False, comment="类型（person/event/enterprise）")
    name = Column(String(200), nullable=False, index=True, comment="名称")
    avatar = Column(String(500), nullable=True, comment="头像/图标URL")
    summary = Column(Text, nullable=True, comment="简介")
    description = Column(Text, nullable=True, comment="详细描述")
    birth_date = Column(DateTime, nullable=True, comment="开始时间")
    death_date = Column(DateTime, nullable=True, comment="结束时间")
    view_count = Column(Integer, default=0, comment="浏览次数")
    like_count = Column(Integer, default=0, comment="点赞数")
    flower_count = Column(Integer, default=0, comment="送花数")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    category = relationship("Category", back_populates="life_entities")
    timelines = relationship("Timeline", back_populates="entity", cascade="all, delete-orphan")
    user_actions = relationship("UserAction", back_populates="entity")

    def __repr__(self):
        return f"<LifeEntity(id={self.id}, name='{self.name}', type='{self.type.value}')>"
