"""
时间线数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..database import Base


class Timeline(Base):
    """时间线表模型"""
    __tablename__ = "timelines"

    id = Column(Integer, primary_key=True, index=True, comment="主键")
    entity_id = Column(Integer, ForeignKey("life_entities.id"), nullable=False, comment="实体ID")
    title = Column(String(200), nullable=False, comment="标题")
    content = Column(Text, nullable=True, comment="详细内容")
    event_date = Column(DateTime, nullable=False, comment="事件时间")
    image_url = Column(String(500), nullable=False, comment="图片URL（必选）")
    video_url = Column(String(500), nullable=True, comment="视频URL（可选）")
    sort_order = Column(Integer, default=0, comment="排序权重")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    entity = relationship("LifeEntity", back_populates="timelines")

    def __repr__(self):
        return f"<Timeline(id={self.id}, title='{self.title}', entity_id={self.entity_id})>"
