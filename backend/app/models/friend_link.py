from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime
from sqlalchemy.sql import func
from app.database import Base

class FriendLink(Base):
    __tablename__ = "friend_links"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="友情链接名称")
    url = Column(String(500), nullable=False, comment="友情链接URL")
    description = Column(Text, nullable=True, comment="友情链接描述")
    icon_url = Column(String(500), nullable=True, comment="友情链接图标URL")
    sort_order = Column(Integer, default=0, comment="排序权重，数字越大越靠前")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    def __repr__(self):
        return f"<FriendLink(id={self.id}, name='{self.name}', url='{self.url}')>"
