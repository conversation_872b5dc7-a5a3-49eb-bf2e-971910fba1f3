"""
媒体文件数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, BigInteger, Enum
from sqlalchemy.sql import func
from ..database import Base
import enum


class MediaType(enum.Enum):
    """媒体类型枚举"""
    IMAGE = "image"
    VIDEO = "video"
    DOCUMENT = "document"
    OTHER = "other"


class MediaCategory(enum.Enum):
    """媒体分类枚举"""
    AVATAR = "avatar"
    TIMELINE_IMAGE = "timeline_image"
    TIMELINE_VIDEO = "timeline_video"
    GENERAL = "general"


class MediaFile(Base):
    """媒体文件表模型"""
    __tablename__ = "media_files"

    id = Column(Integer, primary_key=True, index=True, comment="主键")
    filename = Column(String(255), nullable=False, comment="文件名")
    original_filename = Column(String(255), nullable=False, comment="原始文件名")
    file_path = Column(String(500), nullable=False, comment="文件路径")
    file_size = Column(BigInteger, nullable=False, comment="文件大小（字节）")
    mime_type = Column(String(100), nullable=False, comment="MIME类型")
    media_type = Column(Enum(MediaType), nullable=False, comment="媒体类型")
    category = Column(Enum(MediaCategory), default=MediaCategory.GENERAL, comment="媒体分类")
    description = Column(Text, nullable=True, comment="文件描述")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    def __repr__(self):
        return f"<MediaFile(id={self.id}, filename='{self.filename}', type='{self.media_type.value}')>"
