"""
认证依赖模块
"""
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from ..database import get_db
from .security import verify_token

# HTTP Bearer认证方案
security = HTTPBearer()


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> dict:
    """
    获取当前用户信息
    
    Args:
        credentials: HTTP认证凭据
        db: 数据库会话
        
    Returns:
        用户信息字典
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 验证JWT令牌
        payload = verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception
            
        # 检查令牌类型（确保不是刷新令牌）
        token_type = payload.get("type")
        if token_type == "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="不能使用刷新令牌访问此资源",
                headers={"WWW-Authenticate": "Bearer"},
            )
            
        # 获取用户标识
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
            
        # 在实际应用中，这里应该从数据库查询用户信息
        # 目前简化处理，直接返回用户名
        return {"username": username, "is_admin": True}
        
    except Exception:
        raise credentials_exception


def get_current_admin_user(current_user: dict = Depends(get_current_user)) -> dict:
    """
    获取当前管理员用户
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        管理员用户信息
        
    Raises:
        HTTPException: 非管理员用户时抛出403错误
    """
    if not current_user.get("is_admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限"
        )
    return current_user


# 可选的认证依赖（用于不强制要求认证的端点）
def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: Session = Depends(get_db)
) -> Optional[dict]:
    """
    可选的用户认证（不强制要求认证）
    
    Args:
        credentials: HTTP认证凭据（可选）
        db: 数据库会话
        
    Returns:
        用户信息字典或None
    """
    if credentials is None:
        return None
        
    try:
        payload = verify_token(credentials.credentials)
        if payload is None:
            return None
            
        username: str = payload.get("sub")
        if username is None:
            return None
            
        return {"username": username, "is_admin": True}
    except Exception:
        return None
