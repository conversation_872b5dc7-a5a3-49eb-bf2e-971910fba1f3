# 数字生命馆项目需求规格说明书

## 1. 项目概述

### 1.1 项目背景
数字生命馆是一个专注于记录和展示人物、事件、企业历史时间线的数字化平台。通过时间线的形式，为用户提供直观、详细的历史信息浏览和互动体验。

### 1.2 项目目标
- 构建一个综合性的历史信息展示平台
- 提供用户友好的信息检索和浏览体验
- 建立完善的内容管理和数据统计系统
- 促进历史文化的数字化传承和传播

### 1.3 项目范围
- 前端展示系统：面向公众的信息浏览平台
- 后台管理系统：内容管理和数据统计平台
- 数据库系统：存储和管理所有业务数据

## 2. 核心概念定义

### 2.1 实体定义
- **人物（Person）**：历史人物或当代重要人物
- **事件（Event）**：重要历史事件或里程碑事件
- **企业（Enterprise）**：具有历史意义的企业或组织
- **时间线（Timeline）**：按时间顺序排列的相关信息集合
- **分类（Category）**：用于组织和分类不同类型的内容

### 2.2 实体关系
- 每个分类可包含多个人物/事件/企业
- 每个人物/事件/企业可拥有一条或多条时间线
- 时间线由多个时间节点组成，每个节点包含详细信息

## 3. 功能需求

### 3.1 前台展示系统

#### 3.1.1 首页展示
- **列表展示**：以卡片形式展示所有生命实体
- **分类筛选**：支持按分类筛选内容
- **搜索功能**：支持关键词模糊搜索
- **排序功能**：支持按时间、热度等维度排序

#### 3.1.2 详情页面
- **基本信息**：显示实体的基本信息和简介
- **时间线展示**：以时间轴形式展示详细历史
- **多媒体支持**：支持图片、视频等多媒体内容
- **互动功能**：支持点赞、送花等用户互动

#### 3.1.3 用户体验
- **响应式设计**：适配PC端和移动端
- **无需登录**：所有内容对公众开放
- **快速加载**：优化页面加载速度
- **SEO优化**：支持搜索引擎优化

### 3.2 后台管理系统

#### 3.2.1 管理员认证
- **JWT登录**：管理员账号JWT令牌认证
- **令牌管理**：访问令牌和刷新令牌机制
- **会话保持**：基于JWT的登录状态维持

#### 3.2.2 内容管理
- **分类管理**：分类的增删改查操作
- **实体管理**：人物/事件/企业的完整CRUD操作
- **时间线管理**：时间线节点的管理和编辑
- **媒体管理**：图片、视频等媒体文件管理

#### 3.2.3 数据统计
- **访问统计**：页面访问量、用户行为统计
- **互动统计**：点赞、送花等互动数据统计
- **内容统计**：内容数量、分类分布等统计
- **报表生成**：支持数据报表导出

## 4. 数据模型设计

### 4.1 核心数据表

#### 4.1.1 分类表 (categories)
```sql
- id: 主键
- name: 分类名称
- description: 分类描述
- sort_order: 排序权重
- created_at: 创建时间
- updated_at: 更新时间
```

#### 4.1.2 生命实体表 (life_entities)
```sql
- id: 主键
- category_id: 分类ID（外键）
- type: 类型（person/event/enterprise）
- name: 名称
- avatar: 头像/图标
- summary: 简介
- description: 详细描述
- birth_date: 开始时间
- death_date: 结束时间（可为空）
- view_count: 浏览次数
- like_count: 点赞数
- flower_count: 送花数
- created_at: 创建时间
- updated_at: 更新时间
```

#### 4.1.3 时间线表 (timelines)
```sql
- id: 主键
- entity_id: 实体ID（外键）
- title: 标题
- content: 详细内容
- event_date: 事件时间
- image_url: 图片URL（必选）
- video_url: 视频URL（可选）
- sort_order: 排序权重
- created_at: 创建时间
- updated_at: 更新时间
```

#### 4.1.4 用户行为统计表 (user_actions)
```sql
- id: 主键
- entity_id: 实体ID（外键）
- action_type: 行为类型（view/like/flower）
- ip_address: IP地址
- user_agent: 用户代理
- created_at: 创建时间
```

## 5. 技术架构

### 5.1 前端技术栈
- **框架**：Vue 3 + Composition API
- **状态管理**：Pinia
- **HTTP客户端**：Axios
- **UI组件库**：Ant Design Vue
- **样式预处理**：SCSS
- **构建工具**：Vite
- **路由管理**：Vue Router

### 5.2 后端技术栈
- **框架**：FastAPI (Python)
- **数据库ORM**：SQLAlchemy
- **数据验证**：Pydantic
- **认证授权**：JWT (JSON Web Token)
- **文件存储**：本地存储/云存储
- **API文档**：自动生成OpenAPI文档

### 5.3 数据库配置
- **数据库**：MySQL 8.0+
- **连接信息**：
  - 主机：localhost:3306
  - 用户名：root
  - 数据库名：dielife
  - 字符集：utf8mb4

## 6. 非功能需求

### 6.1 性能要求
- 页面加载时间 < 3秒
- 支持并发用户数 > 1000
- 数据库查询响应时间 < 500ms

### 6.2 安全要求
- 数据传输加密（HTTPS）
- SQL注入防护
- XSS攻击防护
- 文件上传安全检查
- JWT令牌安全管理
- 管理员身份认证保护

### 6.3 可用性要求
- 系统可用性 > 99%
- 支持主流浏览器
- 移动端适配

## 7. 开发计划

### 7.1 第一阶段（基础功能）
- 数据库设计和搭建
- 后端API基础框架
- 前端基础页面和路由
- 基本的CRUD功能

### 7.2 第二阶段（核心功能）
- 完整的内容管理系统
- 前台展示和搜索功能
- 用户互动功能
- 基础数据统计

### 7.3 第三阶段（优化完善）
- 性能优化
- 高级数据统计和报表
- SEO优化
- 安全加固

## 8. 验收标准

### 8.1 功能验收
- 所有功能模块正常运行
- 数据操作准确无误
- 用户体验流畅

### 8.2 性能验收
- 满足性能指标要求
- 通过压力测试
- 兼容性测试通过

### 8.3 安全验收
- 通过安全漏洞扫描
- 数据安全保护到位
- 权限控制有效
